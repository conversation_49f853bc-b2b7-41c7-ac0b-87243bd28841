# Comprehensive Deployment Guide - DMS Service

## 📋 Overview

This guide provides complete deployment instructions for the Document Management Service (DMS) across different environments and deployment strategies, from traditional server deployments to modern cloud-native Kubernetes deployments.

## 📑 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Configuration](#environment-configuration)
3. [Database Setup](#database-setup)
4. [Deployment Strategies](#deployment-strategies)
5. [Infrastructure as Code](#infrastructure-as-code)
6. [CI/CD Pipeline](#cicd-pipeline)
7. [Monitoring & Observability](#monitoring--observability)
8. [Security Configuration](#security-configuration)
9. [Performance Optimization](#performance-optimization)
10. [Backup & Recovery](#backup--recovery)
11. [Troubleshooting](#troubleshooting)
12. [Maintenance & Support](#maintenance--support)

## 🔧 Prerequisites

### System Requirements
- **Java**: OpenJDK 21 or higher
- **Database**: MySQL 8.0+ or PostgreSQL 13+
- **Cache**: Redis 7.0+
- **Memory**: Minimum 2GB RAM, Recommended 4GB+
- **Storage**: 50GB+ for application, additional for file storage
- **Network**: HTTPS support, firewall configuration

### Required Tools

```bash
# Install Java 21
sudo apt update
sudo apt install openjdk-21-jdk

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Install Terraform
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
unzip terraform_1.6.0_linux_amd64.zip
sudo mv terraform /usr/local/bin/

# Install Helm
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
```

### Access Requirements
- AWS CLI configured with appropriate permissions
- Docker registry access
- Kubernetes cluster access (for K8s deployments)
- GitHub repository access (for CI/CD)

## ⚙️ Environment Configuration

### Environment Variables

Create environment-specific configuration:

```bash
# Database Configuration
SPRING_DATASOURCE_URL=*****************************************
SPRING_DATASOURCE_USERNAME=dms_user
SPRING_DATASOURCE_PASSWORD=<strong_password>

# Redis Configuration
SPRING_REDIS_HOST=prod-redis-server
SPRING_REDIS_PORT=6379
SPRING_REDIS_PASSWORD=<redis_password>

# JWT Security
DMS_JWT_SECRET=<256-bit-secret-key>
DMS_JWT_EXPIRATION=86400000

# Storage Configuration
DMS_STORAGE_PROVIDER=S3
DMS_STORAGE_LOCAL_BASE_PATH=/var/dms/storage

# S3 Configuration (if using S3)
DMS_STORAGE_S3_BUCKET_NAME=my-dms-bucket
DMS_STORAGE_S3_REGION=us-west-2
AWS_ACCESS_KEY_ID=<access_key>
AWS_SECRET_ACCESS_KEY=<secret_key>

# SharePoint Configuration (if using SharePoint)
DMS_STORAGE_SHAREPOINT_SITE_URL=https://company.sharepoint.com/sites/dms
DMS_STORAGE_SHAREPOINT_CLIENT_ID=<client_id>
DMS_STORAGE_SHAREPOINT_CLIENT_SECRET=<client_secret>
DMS_STORAGE_SHAREPOINT_TENANT_ID=<tenant_id>
DMS_STORAGE_SHAREPOINT_DOCUMENT_LIBRARY=Documents

# Logging
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_COM_ASCENTBUSINESS=DEBUG
```

### Application Properties

Create `application-prod.properties`:

```properties
# Server Configuration
server.port=8080
server.servlet.context-path=/dms
server.tomcat.threads.max=200
server.tomcat.threads.min-spare=10

# Database
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.idle-timeout=300000

# JPA
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Cache
spring.cache.type=redis
spring.cache.redis.time-to-live=600000
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=dms:

# GraphQL
spring.graphql.cors.allowed-origins=https://your-frontend-domain.com
spring.graphql.cors.allowed-methods=GET,POST,OPTIONS
spring.graphql.cors.allowed-headers=*

# Security
dms.security.cors.allowed-origins=https://your-frontend-domain.com
dms.security.rate-limit.enabled=true
dms.security.rate-limit.requests-per-minute=100

# Monitoring
management.endpoints.web.exposure.include=health,metrics,prometheus
management.endpoint.health.show-details=when-authorized
management.metrics.export.prometheus.enabled=true
```

## 🗄️ Database Setup

### MySQL Setup

```sql
-- Create production database
CREATE DATABASE dms_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user
CREATE USER 'dms_user'@'%' IDENTIFIED BY '<strong_password>';
GRANT ALL PRIVILEGES ON dms_prod.* TO 'dms_user'@'%';
FLUSH PRIVILEGES;

-- Performance optimization indexes
CREATE INDEX idx_document_created_by ON documents(created_by);
CREATE INDEX idx_document_storage_provider ON documents(storage_provider);
CREATE INDEX idx_audit_log_action_timestamp ON audit_logs(action, timestamp);
```

### Database Migration

```bash
# Build application first
mvn clean package -Pprod -DskipTests

# Run Liquibase migrations
java -jar target/dms-svc-*.jar \
  --spring.profiles.active=prod \
  --spring.liquibase.contexts=prod \
  --spring.datasource.url="**********************************" \
  --spring.datasource.username=dms_user \
  --spring.datasource.password=<password>
```

## 🚀 Deployment Strategies

### Strategy 1: Traditional Server Deployment

#### Build Application

```bash
# Clone repository
git clone <repository-url>
cd dms-svc

# Build with production profile
mvn clean package -Pprod -DskipTests

# Verify JAR file
ls -la target/dms-svc-*.jar
```

#### Systemd Service Deployment

Create `/etc/systemd/system/dms.service`:

```ini
[Unit]
Description=Document Management Service
After=network.target

[Service]
Type=simple
User=dms
Group=dms
ExecStart=/usr/bin/java -jar -Xmx4g -Xms2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -Dspring.profiles.active=prod /opt/dms/dms-svc.jar
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=dms
Environment=JAVA_OPTS="-XX:+UseStringDeduplication"

[Install]
WantedBy=multi-user.target
```

```bash
# Create user and directories
sudo useradd -r -s /bin/false dms
sudo mkdir -p /opt/dms /var/log/dms /var/dms/storage
sudo chown dms:dms /opt/dms /var/log/dms /var/dms/storage

# Deploy application
sudo cp target/dms-svc-*.jar /opt/dms/dms-svc.jar
sudo chown dms:dms /opt/dms/dms-svc.jar

# Enable and start service
sudo systemctl enable dms
sudo systemctl start dms
sudo systemctl status dms
```

### Strategy 2: Docker Deployment

#### Single Container Deployment

Create `Dockerfile`:

```dockerfile
FROM eclipse-temurin:21-jre-alpine

# Create non-root user
RUN addgroup -g 1001 dms && adduser -D -u 1001 -G dms dms

# Set working directory
WORKDIR /app

# Copy application
COPY target/dms-svc-*.jar app.jar
RUN chown dms:dms app.jar

# Switch to non-root user
USER dms

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/dms/actuator/health || exit 1

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "-Xmx2g", "-Xms1g", "-XX:+UseG1GC", "-Dspring.profiles.active=prod", "app.jar"]
```

```bash
# Build and run
docker build -t dms-service:latest .
docker run -d --name dms \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e SPRING_DATASOURCE_URL=***************************** \
  -e SPRING_DATASOURCE_PASSWORD=${DB_PASSWORD} \
  -e DMS_JWT_SECRET=${JWT_SECRET} \
  --restart unless-stopped \
  dms-service:latest
```

#### Docker Compose Deployment

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  dms:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_URL=*****************************
      - SPRING_DATASOURCE_USERNAME=dms_user
      - SPRING_DATASOURCE_PASSWORD=${DB_PASSWORD}
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD}
      - DMS_JWT_SECRET=${JWT_SECRET}
    depends_on:
      - db
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/dms/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: dms_prod
      MYSQL_USER: dms_user
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - dms
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

### Strategy 3: Kubernetes Deployment

#### Prepare Kubernetes Cluster

```bash
# Update kubeconfig
aws eks update-kubeconfig --region us-east-1 --name dms-prod-cluster

# Verify cluster access
kubectl cluster-info
kubectl get nodes
```

#### Deploy Infrastructure Components

```bash
# Create namespace and basic resources
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/persistent-volumes.yaml
kubectl apply -f k8s/rbac.yaml
```

#### Configure Secrets

```bash
# Create secrets (replace with actual values)
kubectl create secret generic dms-secrets \
  --from-literal=SPRING_DATASOURCE_PASSWORD='your-db-password' \
  --from-literal=SPRING_REDIS_PASSWORD='your-redis-password' \
  --from-literal=DMS_JWT_SECRET='your-jwt-secret' \
  --from-literal=AWS_ACCESS_KEY_ID='your-aws-key' \
  --from-literal=AWS_SECRET_ACCESS_KEY='your-aws-secret' \
  -n dms-system

# Create TLS secret
kubectl create secret tls dms-tls-secret \
  --cert=path/to/tls.crt \
  --key=path/to/tls.key \
  -n dms-system
```

#### Deploy Application

```bash
# Deploy configuration and services
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/services.yaml
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/ingress.yaml

# Verify deployment
kubectl get pods -n dms-system
kubectl get services -n dms-system
kubectl rollout status deployment/dms-deployment -n dms-system
```

#### Automated Deployment Script

```bash
# Make script executable
chmod +x scripts/deploy.sh

# Deploy to development
./scripts/deploy.sh -e dev -t v1.0.0

# Deploy to production with dry run
./scripts/deploy.sh -e prod -t v1.0.0 --dry-run

# Deploy to production
./scripts/deploy.sh -e prod -t v1.0.0

# Rollback if needed
./scripts/deploy.sh -e prod --rollback
```

## 🏗️ Infrastructure as Code

### Terraform Infrastructure Provisioning

```bash
# Navigate to terraform directory
cd terraform/

# Initialize Terraform
terraform init

# Create workspace for environment
terraform workspace new prod
terraform workspace select prod

# Plan infrastructure changes
terraform plan -var-file="environments/prod.tfvars"

# Apply infrastructure
terraform apply -var-file="environments/prod.tfvars"
```

### Environment-Specific Variables

Create `terraform/environments/prod.tfvars`:

```hcl
# Production environment variables
environment = "prod"
aws_region = "us-east-1"

# VPC Configuration
vpc_cidr = "10.0.0.0/16"
public_subnet_cidrs = ["********/24", "********/24", "********/24"]
private_subnet_cidrs = ["*********/24", "*********/24", "*********/24"]
database_subnet_cidrs = ["*********/24", "*********/24", "*********/24"]

# Database Configuration
db_instance_class = "db.t3.large"
db_allocated_storage = 100
db_backup_retention_period = 7

# EKS Configuration
eks_cluster_version = "1.28"
eks_node_desired_capacity = 3
eks_node_max_capacity = 10
eks_node_min_capacity = 2

# Security
ssl_certificate_arn = "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"
```

## 🔄 CI/CD Pipeline

### GitHub Actions Configuration

Set up the following secrets in your GitHub repository:

```bash
# Required GitHub Secrets
DOCKER_USERNAME          # Docker registry username
DOCKER_PASSWORD          # Docker registry password
SONAR_TOKEN             # SonarCloud token
KUBE_CONFIG_DEV         # Kubernetes config for dev environment
KUBE_CONFIG_STAGING     # Kubernetes config for staging environment
KUBE_CONFIG_PROD        # Kubernetes config for production environment
SLACK_WEBHOOK_URL       # Slack webhook for notifications
```

### Pipeline Stages

The CI/CD pipeline includes:

1. **Code Quality & Security**: Checkstyle, SpotBugs, OWASP dependency check, SonarCloud
2. **Test Suite**: Unit tests, integration tests with TestContainers
3. **Security Scan**: Trivy vulnerability scanning
4. **Build & Push**: Docker image build and push to registry
5. **Deploy**: Environment-specific deployments with health checks

### Deployment Environments

- **Development**: Automatic deployment on `develop` branch
- **Staging**: Automatic deployment on `release/*` branches
- **Production**: Manual deployment on release creation

## 🔒 Security Configuration

### Network Security
- Use HTTPS only in production
- Configure firewall rules
- Restrict database access to application servers only
- Use VPC/private networks where possible

### Application Security
- Use strong JWT secrets (256-bit minimum)
- Enable rate limiting
- Configure CORS appropriately
- Use environment variables for secrets
- Regular security updates

### Database Security
- Use strong passwords
- Enable SSL connections
- Regular backups
- Principle of least privilege

### Load Balancer Configuration (Nginx)

Create `/etc/nginx/sites-available/dms`:

```nginx
upstream dms_backend {
    server localhost:8080;
    # Add more servers for load balancing
    # server localhost:8081;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # GraphQL endpoint
    location /dms/graphql {
        proxy_pass http://dms_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Health check
    location /dms/actuator/health {
        proxy_pass http://dms_backend;
        access_log off;
    }
}
```

## 📊 Monitoring & Observability

### Prometheus & Grafana Setup

```bash
# Add Prometheus Helm repository
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

# Install Prometheus
helm install prometheus prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace \
  --values monitoring/prometheus/values.yaml

# Access Grafana
kubectl port-forward svc/prometheus-grafana 3000:80 -n monitoring
```

### Distributed Tracing

```bash
# Install Jaeger
kubectl apply -f https://github.com/jaegertracing/jaeger-operator/releases/download/v1.49.0/jaeger-operator.yaml

# Deploy Jaeger instance
kubectl apply -f monitoring/jaeger/jaeger.yaml
```

### Log Aggregation

```bash
# Install ELK stack
helm repo add elastic https://helm.elastic.co
helm install elasticsearch elastic/elasticsearch -n logging --create-namespace
helm install kibana elastic/kibana -n logging
helm install filebeat elastic/filebeat -n logging
```

### Application Monitoring

```yaml
# Prometheus configuration
scrape_configs:
  - job_name: 'dms'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/dms/actuator/prometheus'
```

## ⚡ Performance Optimization

### JVM Tuning

```bash
# Production JVM settings
JAVA_OPTS="-Xmx4g -Xms2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication -XX:+UseCompressedOops"
```

### Database Optimization

```sql
-- Performance indexes
CREATE INDEX idx_document_created_by ON documents(created_by);
CREATE INDEX idx_document_storage_provider ON documents(storage_provider);
CREATE INDEX idx_audit_log_action_timestamp ON audit_logs(action, timestamp);
CREATE INDEX idx_document_permissions_user ON document_permissions(user_id);
CREATE INDEX idx_document_permissions_doc ON document_permissions(document_id);
```

### Cache Configuration

```properties
# Redis optimization
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=dms:
spring.cache.redis.time-to-live=600000
```

## 💾 Backup & Recovery

### Database Backup

```bash
#!/bin/bash
# Daily database backup script
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/mysql"
DB_NAME="dms_prod"

mysqldump -u dms_user -p${DB_PASSWORD} \
  --single-transaction \
  --quick \
  --lock-tables=false \
  ${DB_NAME} | gzip > ${BACKUP_DIR}/dms_backup_${DATE}.sql.gz

# Keep only last 30 days
find ${BACKUP_DIR} -name "dms_backup_*.sql.gz" -type f -mtime +30 -delete
```

### File Storage Backup
- **Local Storage**: Regular filesystem backups
- **S3**: Cross-region replication, versioning
- **SharePoint**: Built-in versioning and backup

### Log Management

```bash
# Configure log rotation
sudo nano /etc/logrotate.d/dms

/var/log/dms/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 0644 dms dms
    postrotate
        systemctl reload dms
    endscript
}
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Pod Startup Issues (Kubernetes)

```bash
# Check pod status
kubectl get pods -n dms-system

# View pod logs
kubectl logs -f deployment/dms-deployment -n dms-system

# Describe pod for events
kubectl describe pod <pod-name> -n dms-system
```

#### 2. Database Connection Issues

```bash
# Test database connectivity (Kubernetes)
kubectl run mysql-test --image=mysql:8.0 --rm -it --restart=Never -- \
  mysql -h mysql-service.dms-system.svc.cluster.local -u dms_user -p

# Test database connectivity (Traditional)
mysql -h prod-db-server -u dms_user -p dms_prod
```

#### 3. Application Issues

```bash
# Application health
curl -f http://localhost:8080/dms/actuator/health

# Database connectivity
curl -f http://localhost:8080/dms/actuator/health/db

# Redis connectivity
curl -f http://localhost:8080/dms/actuator/health/redis
```

#### 4. Performance Issues

```bash
# Check resource usage (Kubernetes)
kubectl top pods -n dms-system
kubectl top nodes

# Check resource usage (Traditional)
htop
iostat -x 1
```

### Health Check Commands

```bash
# Application health
curl -f http://dms.company.com/dms/actuator/health

# Database health (Kubernetes)
kubectl exec -it deployment/mysql-deployment -n dms-system -- \
  mysqladmin ping -h localhost -u root -p

# Redis health (Kubernetes)
kubectl exec -it deployment/redis-deployment -n dms-system -- \
  redis-cli ping
```

## 🛠️ Maintenance & Support

### Regular Tasks
- Monitor application metrics
- Review security logs
- Update dependencies
- Performance tuning
- Backup verification

### Scaling Considerations

#### Horizontal Scaling
- Use load balancer
- Stateless application design
- Shared cache (Redis)
- Shared database

#### Vertical Scaling
- Increase CPU/RAM
- SSD storage
- Database optimization

### Rollback Procedures

#### Application Rollback
1. Stop current version
2. Deploy previous version
3. Update load balancer
4. Monitor health checks

#### Database Rollback
1. Restore from backup
2. Run rollback migrations if available
3. Verify data integrity

### Emergency Contacts
- DevOps Team: <EMAIL>
- Database Admin: <EMAIL>
- Security Team: <EMAIL>

## 📚 Additional Resources

- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Docker Documentation](https://docs.docker.com/)
- [Terraform Documentation](https://www.terraform.io/docs/)
- [Prometheus Documentation](https://prometheus.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)

---

**Note**: This comprehensive guide covers multiple deployment strategies. Choose the approach that best fits your infrastructure and requirements. Ensure all security credentials are properly managed and never committed to version control.