#!/bin/bash

# DMS Service - Comprehensive Test Execution Script
# This script runs all test categories and generates detailed reports
# Includes: Unit, Integration, E2E, Security, Performance, Contract, Config, Infrastructure, API, GraphQL, Compliance, Retention, Document Sharing, and Documentation tests

echo "========================================"
echo "DMS Service Automated Testing Suite"
echo "========================================"
echo "Version: 2.1 - Enhanced with Document Sharing Tests"
echo

# Parse command line arguments
RUN_UNIT_TESTS=1
RUN_INTEGRATION_TESTS=1
RUN_E2E_TESTS=1
RUN_SECURITY_TESTS=1
RUN_PERFORMANCE_TESTS=1
RUN_CONTRACT_TESTS=1
RUN_CONFIG_TESTS=1
RUN_INFRASTRUCTURE_TESTS=1
RUN_API_TESTS=1
RUN_GRAPHQL_TESTS=1
RUN_COMPLIANCE_TESTS=1
RUN_RETENTION_TESTS=1
RUN_DOCUMENT_SHARING_TESTS=1
RUN_CONVERSION_TESTS=1
RUN_DOCUMENTATION_TESTS=1
FAIL_FAST=0
GENERATE_REPORTS=1

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --unit-only)
            RUN_INTEGRATION_TESTS=0
            RUN_E2E_TESTS=0
            RUN_SECURITY_TESTS=0
            RUN_PERFORMANCE_TESTS=0
            RUN_CONTRACT_TESTS=0
            RUN_CONFIG_TESTS=0
            RUN_INFRASTRUCTURE_TESTS=0
            RUN_API_TESTS=0
            RUN_GRAPHQL_TESTS=0
            RUN_COMPLIANCE_TESTS=0
            RUN_RETENTION_TESTS=0
            RUN_DOCUMENT_SHARING_TESTS=0
            RUN_CONVERSION_TESTS=0
            RUN_DOCUMENTATION_TESTS=0
            shift
            ;;
        --integration-only)
            RUN_UNIT_TESTS=0
            RUN_E2E_TESTS=0
            RUN_SECURITY_TESTS=0
            RUN_PERFORMANCE_TESTS=0
            RUN_CONTRACT_TESTS=0
            RUN_CONFIG_TESTS=0
            RUN_INFRASTRUCTURE_TESTS=0
            RUN_API_TESTS=0
            RUN_GRAPHQL_TESTS=0
            RUN_COMPLIANCE_TESTS=0
            RUN_RETENTION_TESTS=0
            RUN_DOCUMENT_SHARING_TESTS=0
            RUN_CONVERSION_TESTS=0
            RUN_DOCUMENTATION_TESTS=0
            shift
            ;;
        --security-only)
            RUN_UNIT_TESTS=0
            RUN_INTEGRATION_TESTS=0
            RUN_E2E_TESTS=0
            RUN_PERFORMANCE_TESTS=0
            RUN_CONTRACT_TESTS=0
            RUN_CONFIG_TESTS=0
            RUN_INFRASTRUCTURE_TESTS=0
            RUN_API_TESTS=0
            RUN_GRAPHQL_TESTS=0
            RUN_COMPLIANCE_TESTS=0
            RUN_RETENTION_TESTS=0
            RUN_DOCUMENT_SHARING_TESTS=0
            RUN_CONVERSION_TESTS=0
            RUN_DOCUMENTATION_TESTS=0
            shift
            ;;
        --sharing-only)
            RUN_UNIT_TESTS=0
            RUN_INTEGRATION_TESTS=0
            RUN_E2E_TESTS=0
            RUN_SECURITY_TESTS=0
            RUN_PERFORMANCE_TESTS=0
            RUN_CONTRACT_TESTS=0
            RUN_CONFIG_TESTS=0
            RUN_INFRASTRUCTURE_TESTS=0
            RUN_API_TESTS=0
            RUN_GRAPHQL_TESTS=0
            RUN_COMPLIANCE_TESTS=0
            RUN_RETENTION_TESTS=0
            RUN_CONVERSION_TESTS=0
            RUN_DOCUMENTATION_TESTS=0
            shift
            ;;
        --documentation-only)
            RUN_UNIT_TESTS=0
            RUN_INTEGRATION_TESTS=0
            RUN_E2E_TESTS=0
            RUN_SECURITY_TESTS=0
            RUN_PERFORMANCE_TESTS=0
            RUN_CONTRACT_TESTS=0
            RUN_CONFIG_TESTS=0
            RUN_INFRASTRUCTURE_TESTS=0
            RUN_API_TESTS=0
            RUN_GRAPHQL_TESTS=0
            RUN_COMPLIANCE_TESTS=0
            RUN_RETENTION_TESTS=0
            RUN_DOCUMENT_SHARING_TESTS=0
            RUN_CONVERSION_TESTS=0
            shift
            ;;
        --fail-fast)
            FAIL_FAST=1
            shift
            ;;
        --no-reports)
            GENERATE_REPORTS=0
            shift
            ;;
        --help)
            echo
            echo "Usage: run-all-tests.sh [OPTIONS]"
            echo
            echo "Options:"
            echo "  --unit-only         Run only unit tests"
            echo "  --integration-only  Run only integration tests"
            echo "  --security-only     Run only security tests"
            echo "  --sharing-only      Run only document sharing tests"
            echo "  --documentation-only Run only documentation and automation tests"
            echo "  --fail-fast         Stop on first test failure"
            echo "  --no-reports        Skip report generation"
            echo "  --help              Show this help message"
            echo
            echo "Examples:"
            echo "  ./run-all-tests.sh                    # Run all tests"
            echo "  ./run-all-tests.sh --unit-only        # Run only unit tests"
            echo "  ./run-all-tests.sh --sharing-only     # Run only document sharing tests"
            echo "  ./run-all-tests.sh --fail-fast        # Stop on first failure"
            echo
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Set variables
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_DIR="target/test-reports/$TIMESTAMP"
LOG_FILE="$REPORT_DIR/test-execution.log"

# Create report directory
mkdir -p "$REPORT_DIR"

echo "Test execution started at $(date)" > "$LOG_FILE"
echo "Report directory: $REPORT_DIR" >> "$LOG_FILE"
echo "Arguments: $*" >> "$LOG_FILE"
echo >> "$LOG_FILE"

echo "Starting comprehensive test execution..."
echo "Logs will be saved to: $LOG_FILE"
echo

# Initialize failure counters for all test categories
UNIT_TEST_FAILED=0
INTEGRATION_TEST_FAILED=0
E2E_TEST_FAILED=0
SECURITY_TEST_FAILED=0
PERFORMANCE_TEST_FAILED=0
CONTRACT_TEST_FAILED=0
CONFIG_TEST_FAILED=0
INFRASTRUCTURE_TEST_FAILED=0
API_TEST_FAILED=0
GRAPHQL_TEST_FAILED=0
COMPLIANCE_TEST_FAILED=0
RETENTION_TEST_FAILED=0
DOCUMENT_SHARING_TEST_FAILED=0
CONVERSION_TEST_FAILED=0
DOCUMENTATION_TEST_FAILED=0
EXTENDED_PROCESSING_TEST_FAILED=0
VIRUS_SCANNING_TEST_FAILED=0

# Check prerequisites
echo "Checking prerequisites..."
if ! command -v mvn &> /dev/null; then
    echo "ERROR: Maven is not installed or not in PATH"
    exit 1
fi

if ! command -v java &> /dev/null; then
    echo "ERROR: Java is not installed or not in PATH"
    exit 1
fi

# Clean and compile
echo "[1/18] Cleaning and compiling project..."
mvn clean compile test-compile >> "$LOG_FILE" 2>&1
if [ $? -ne 0 ]; then
    echo "ERROR: Compilation failed. Check $LOG_FILE for details."
    exit 1
fi
echo "✓ Compilation successful"

# Run Unit Tests
if [ $RUN_UNIT_TESTS -eq 1 ]; then
    echo "[2/18] Running Unit Tests..."
    mvn test -Dtest="**/*Test,**/*LargeFileUploadTest,**/*DocumentShar*Test,**/*BulkShar*Test,**/*Shar*Test,**/*AsyncProcessingJobRepositoryTest,**/*AsyncProcessingJobEntityTest,**/*AsyncJobStatusTest,**/*ChunkedUploadManagerTest,**/*AsyncProcessingResolverTest" -DexcludedGroups="integration,e2e,security,performance,contract" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Unit tests had failures. Check reports for details."
        UNIT_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Unit tests passed"
        UNIT_TEST_FAILED=0
    fi
else
    echo "[2/18] Skipping Unit Tests..."
fi

# Run Integration Tests
if [ $RUN_INTEGRATION_TESTS -eq 1 ]; then
    echo "[3/18] Running Integration Tests..."
    mvn integration-test -Dtest="**/*IntegrationTest,**/*DocumentSharingIntegrationTest,**/*WorkflowIntegrationTest,**/*SharePointIntegrationTest,**/*ElasticsearchIntegrationTest,**/*ExtendedFileProcessingIntegrationTest,**/*MarkdownConversionIntegrationTest,**/*MarkdownConversionGraphQLIntegrationTest,**/*PdfConversionIntegrationTest,**/*PdfConversionGraphQLIntegrationTest,**/*WordConversionIntegrationTest,**/*WordConversionGraphQLIntegrationTest,**/*TamperProofAuditIntegrationTest,**/*VirusScanningIntegrationTest,**/*CorrelationIdIntegrationTest,**/*DocumentPermissionIntegrationTest,**/*DocumentServiceIntegrationTest,**/*DocumentationAndAutomationIntegrationTest,**/*AuditGraphQLIntegrationTest,**/*ConversionGraphQLIntegrationTest,**/*DiagnosticsGraphQLIntegrationTest,**/*DocumentUploadGraphQLIntegrationTest,**/*TemplateManagementGraphQLIntegrationTest,**/*GraphQLMigrationIntegrationTest,**/*GraphQLMultipartUploadTest,**/*GraphQLSchemaValidationTest,**/*UrlUploadIntegrationTest,**/*UrlDownloadServiceTest" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Integration tests had failures. Check reports for details."
        INTEGRATION_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Integration tests passed"
        INTEGRATION_TEST_FAILED=0
    fi
else
    echo "[3/18] Skipping Integration Tests..."
fi

# Run E2E Tests
if [ $RUN_E2E_TESTS -eq 1 ]; then
    echo "[4/18] Running End-to-End Tests..."
    mvn integration-test -Dtest="**/*E2ETest,**/*DocumentSharingGraphQLE2ETest,**/*WorkflowE2ETest,**/*SharePointE2ETest,**/*DocumentGraphQLE2ETest,**/*AdvancedSearchGraphQLE2ETest,**/*DocumentPermissionGraphQLTest,**/*ApplicationContextLoadTest,**/*CompleteWorkflowE2ETest,**/*DocumentSharingGraphQLE2ETestNoDocker" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ E2E tests had failures. Check reports for details."
        E2E_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ E2E tests passed"
        E2E_TEST_FAILED=0
    fi
else
    echo "[4/18] Skipping E2E Tests..."
fi

# Run Security Tests
if [ $RUN_SECURITY_TESTS -eq 1 ]; then
    echo "[5/18] Running Security Tests..."
    mvn test -Dtest="**/*SecurityTest,**/*VulnerabilityScannerTest,**/*AuditSecurityTest,**/*WorkflowSecurityTest,**/*PIIEncryptionServiceTest,**/*GraphQLSecurityIntegrationTest" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Security tests had failures. Check reports for details."
        SECURITY_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Security tests passed"
        SECURITY_TEST_FAILED=0
    fi
else
    echo "[5/18] Skipping Security Tests..."
fi

# Run Performance Tests
if [ $RUN_PERFORMANCE_TESTS -eq 1 ]; then
    echo "[6/18] Running Performance Tests..."
    mvn test -Dtest="**/*PerformanceTest,**/*BenchmarkTest,**/*AuditPerformanceTest,**/*SearchPerformanceTest,**/*AsyncProcessingPerformanceTest,**/*GraphQLPerformanceTest" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Performance tests had failures. Check reports for details."
        PERFORMANCE_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Performance tests passed"
        PERFORMANCE_TEST_FAILED=0
    fi
else
    echo "[6/18] Skipping Performance Tests..."
fi

# Run Contract Tests
if [ $RUN_CONTRACT_TESTS -eq 1 ]; then
    echo "[7/18] Running Contract Tests..."
    mvn test -Dtest="**/*ContractTest,**/*GraphQLContractTest,**/*APIContractTest" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Contract tests had failures. Check reports for details."
        CONTRACT_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Contract tests passed"
        CONTRACT_TEST_FAILED=0
    fi
else
    echo "[7/18] Skipping Contract Tests..."
fi

# Run Configuration Tests
if [ $RUN_CONFIG_TESTS -eq 1 ]; then
    echo "[8/18] Running Configuration Tests..."
    mvn test -Dtest="**/*ConfigTest,**/*BasicTest" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Configuration tests had failures. Check reports for details."
        CONFIG_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Configuration tests passed"
        CONFIG_TEST_FAILED=0
    fi
else
    echo "[8/18] Skipping Configuration Tests..."
fi

# Run Infrastructure Tests
if [ $RUN_INFRASTRUCTURE_TESTS -eq 1 ]; then
    echo "[9/18] Running Infrastructure Tests..."
    if [ -f "scripts/run-infrastructure-tests.sh" ]; then
        bash scripts/run-infrastructure-tests.sh >> "$LOG_FILE" 2>&1
        if [ $? -ne 0 ]; then
            echo "⚠ Infrastructure tests had failures. Check reports for details."
            INFRASTRUCTURE_TEST_FAILED=1
            if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
        else
            echo "✓ Infrastructure tests passed"
            INFRASTRUCTURE_TEST_FAILED=0
        fi
    else
        echo "⚠ Infrastructure test script not found, skipping..."
        INFRASTRUCTURE_TEST_FAILED=0
    fi
else
    echo "[9/18] Skipping Infrastructure Tests..."
fi

# Run API Tests
if [ $RUN_API_TESTS -eq 1 ]; then
    echo "[10/18] Running API Tests..."
    if [ -f "tests/scripts/test-api-functionality.sh" ]; then
        bash tests/scripts/test-api-functionality.sh >> "$LOG_FILE" 2>&1
        if [ $? -ne 0 ]; then
            echo "⚠ API tests had failures. Check reports for details."
            API_TEST_FAILED=1
            if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
        else
            echo "✓ API tests passed"
            API_TEST_FAILED=0
        fi
    else
        echo "⚠ API test script not found, skipping..."
        API_TEST_FAILED=0
    fi
else
    echo "[10/18] Skipping API Tests..."
fi

# Run GraphQL Tests
if [ $RUN_GRAPHQL_TESTS -eq 1 ]; then
    echo "[11/18] Running GraphQL Tests..."
    if [ -f "tests/scripts/test-graphql.sh" ]; then
        bash tests/scripts/test-graphql.sh >> "$LOG_FILE" 2>&1
        if [ $? -ne 0 ]; then
            echo "⚠ GraphQL tests had failures. Check reports for details."
            GRAPHQL_TEST_FAILED=1
            if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
        else
            echo "✓ GraphQL tests passed"
            GRAPHQL_TEST_FAILED=0
        fi
    else
        echo "⚠ GraphQL test script not found, running individual GraphQL tests..."
        mvn test -Dtest="**/*GraphQL*Test,**/*AuditGraphQLResolverTest,**/*GraphQLConfigurationTest" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
        if [ $? -ne 0 ]; then
            echo "⚠ GraphQL tests had failures. Check reports for details."
            GRAPHQL_TEST_FAILED=1
            if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
        else
            echo "✓ GraphQL tests passed"
            GRAPHQL_TEST_FAILED=0
        fi
    fi
else
    echo "[11/18] Skipping GraphQL Tests..."
fi

# Run Compliance Tests
if [ $RUN_COMPLIANCE_TESTS -eq 1 ]; then
    echo "[12/18] Running Compliance Tests..."
    mvn test -Dtest="**/*ComplianceTest,**/*Compliance*BasicTest" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Compliance tests had failures. Check reports for details."
        COMPLIANCE_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Compliance tests passed"
        COMPLIANCE_TEST_FAILED=0
    fi
else
    echo "[12/18] Skipping Compliance Tests..."
fi

# Run Retention Policy Tests
if [ $RUN_RETENTION_TESTS -eq 1 ]; then
    echo "[13/18] Running Retention Policy Tests..."
    mvn test -Dtest="**/*RetentionTest,**/*Retention*BasicTest" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Retention tests had failures. Check reports for details."
        RETENTION_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Retention tests passed"
        RETENTION_TEST_FAILED=0
    fi
else
    echo "[13/18] Skipping Retention Tests..."
fi

# Run Document Sharing Tests
if [ $RUN_DOCUMENT_SHARING_TESTS -eq 1 ]; then
    echo "[14/17] Running Document Sharing Tests..."
    mvn test -Dtest="**/*DocumentShar*Test,**/*BulkShar*Test,**/*Shar*Test,**/*DocumentSharingIntegrationTest,**/*DocumentSharingGraphQLE2ETest" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Document sharing tests had failures. Check reports for details."
        DOCUMENT_SHARING_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Document sharing tests passed"
        DOCUMENT_SHARING_TEST_FAILED=0
    fi
else
    echo "[14/17] Skipping Document Sharing Tests..."
fi

# Run Document Conversion Tests (Pandoc, Markdown, PDF, Word)
if [ $RUN_CONVERSION_TESTS -eq 1 ]; then
    echo "[15/17] Running Document Conversion Tests..."
    mvn test -Dtest="**/*PandocConversionServiceTest,**/*MarkdownToWordConversionServiceTest,**/*MarkdownConversionResolverTest,**/*PandocConfigTest,**/*MarkdownConversionIntegrationTest,**/*MarkdownConversionGraphQLIntegrationTest,**/*PdfToWordConversionServiceTest,**/*WordToPdfConversionServiceTest,**/*WordToPdfConversionManualTest,**/*PdfConversionResolverTest,**/*WordConversionResolverTest" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Document conversion tests had failures. Check reports for details."
        CONVERSION_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Document conversion tests completed successfully."
        CONVERSION_TEST_FAILED=0
    fi
else
    echo "[15/17] Skipping Document Conversion Tests..."
fi

# Run Extended File Processing Tests
echo "[16/17] Running Extended File Processing Tests..."
if [ -f "test-scripts/run-extended-file-processing-tests.sh" ]; then
    bash test-scripts/run-extended-file-processing-tests.sh >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Extended file processing tests had failures. Check reports for details."
        EXTENDED_PROCESSING_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Extended file processing tests passed"
        EXTENDED_PROCESSING_TEST_FAILED=0
    fi
elif [ -f "test-scripts/run-async-processing-tests.sh" ]; then
    echo "⚠ Extended file processing test script not found, running async processing tests..."
    bash test-scripts/run-async-processing-tests.sh >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Async processing tests had failures. Check reports for details."
        EXTENDED_PROCESSING_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Async processing tests passed"
        EXTENDED_PROCESSING_TEST_FAILED=0
    fi
else
    echo "⚠ Extended file processing test script not found, running individual tests..."
    mvn test -Dtest="**/*FileProcessingConfigTest,**/*ProcessingStrategyTest,**/*AsyncDocumentProcessorTest,**/*ExtendedFileProcessingIntegrationTest,**/*AsyncProcessingJobRepositoryTest,**/*AsyncProcessingJobEntityTest,**/*AsyncJobStatusTest,**/*ChunkedUploadManagerTest,**/*AsyncProcessingResolverTest" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Extended file processing tests had failures. Check reports for details."
        EXTENDED_PROCESSING_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Extended file processing tests passed"
        EXTENDED_PROCESSING_TEST_FAILED=0
    fi
fi

# Run Virus Scanning Tests
echo "[17/18] Running Virus Scanning Tests..."
if [ -f "scripts/test-virus-scanning.sh" ]; then
    bash scripts/test-virus-scanning.sh >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Virus scanning tests had failures. Check reports for details."
        VIRUS_SCANNING_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Virus scanning tests passed"
        VIRUS_SCANNING_TEST_FAILED=0
    fi
else
    echo "⚠ Virus scanning test script not found, running individual tests..."
    mvn test -Dtest="**/*VirusScanner*Test,**/*VirusScanning*Test,**/*BulkUpload*Test" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Virus scanning tests had failures. Check reports for details."
        VIRUS_SCANNING_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Virus scanning tests passed"
        VIRUS_SCANNING_TEST_FAILED=0
    fi
fi

# Run Documentation and Automation Tests
if [ $RUN_DOCUMENTATION_TESTS -eq 1 ]; then
    echo "[18/18] Running Documentation and Automation Tests..."
    mvn test -Dtest="**/*ApiDocumentationTest,**/*JavaDocCoverageTest,**/*TroubleshootingGuidesTest,**/*DependencyManagementTest,**/*RollbackProceduresTest,**/*DocumentationAndAutomationIntegrationTest" -Dmaven.test.failure.ignore=true >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Documentation and automation tests had failures. Check reports for details."
        DOCUMENTATION_TEST_FAILED=1
        if [ $FAIL_FAST -eq 1 ]; then exit 1; fi
    else
        echo "✓ Documentation and automation tests passed"
        DOCUMENTATION_TEST_FAILED=0
    fi
else
    echo "[18/18] Skipping Documentation and Automation Tests..."
fi

# Generate Code Coverage Report and Test Reports
if [ $GENERATE_REPORTS -eq 1 ]; then
    echo "Generating code coverage report and test reports..."
    mvn jacoco:report >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Code coverage report generation failed"
    else
        echo "✓ Code coverage report generated"
    fi

    # Generate Test Reports
    mvn surefire-report:report >> "$LOG_FILE" 2>&1
    if [ $? -ne 0 ]; then
        echo "⚠ Test report generation had issues"
    else
        echo "✓ Test reports generated"
    fi

    # Copy reports to timestamped directory
    echo
    echo "Copying reports to timestamped directory..."
    if [ -d "target/surefire-reports" ]; then
        cp -r target/surefire-reports "$REPORT_DIR/" >> "$LOG_FILE" 2>&1
    fi
    if [ -d "target/failsafe-reports" ]; then
        cp -r target/failsafe-reports "$REPORT_DIR/" >> "$LOG_FILE" 2>&1
    fi
    if [ -d "target/site/jacoco" ]; then
        cp -r target/site/jacoco "$REPORT_DIR/coverage" >> "$LOG_FILE" 2>&1
    fi
    if [ -d "target/test-reports" ]; then
        cp -r target/test-reports "$REPORT_DIR/custom-reports" >> "$LOG_FILE" 2>&1
    fi
    if [ -d "target/infrastructure-test-reports" ]; then
        cp -r target/infrastructure-test-reports "$REPORT_DIR/infrastructure-reports" >> "$LOG_FILE" 2>&1
    fi
    if [ -d "target/documentation-test-results" ]; then
        cp -r target/documentation-test-results "$REPORT_DIR/documentation-reports" >> "$LOG_FILE" 2>&1
    fi

    # Copy shell script test logs if they exist
    if ls /tmp/dms-*.log 1> /dev/null 2>&1; then
        mkdir -p "$REPORT_DIR/shell-logs"
        cp /tmp/dms-*.log "$REPORT_DIR/shell-logs/" >> "$LOG_FILE" 2>&1
    fi
else
    echo "Skipping report generation..."
fi

# Generate summary
echo
echo "========================================"
echo "TEST EXECUTION SUMMARY"
echo "========================================"
echo "Execution completed at $(date)"
echo

if [ $RUN_UNIT_TESTS -eq 1 ]; then
    if [ $UNIT_TEST_FAILED -eq 0 ]; then
        echo "✓ Unit Tests: PASSED"
    else
        echo "✗ Unit Tests: FAILED"
    fi
fi

if [ $RUN_INTEGRATION_TESTS -eq 1 ]; then
    if [ $INTEGRATION_TEST_FAILED -eq 0 ]; then
        echo "✓ Integration Tests: PASSED"
    else
        echo "✗ Integration Tests: FAILED"
    fi
fi

if [ $RUN_E2E_TESTS -eq 1 ]; then
    if [ $E2E_TEST_FAILED -eq 0 ]; then
        echo "✓ E2E Tests: PASSED"
    else
        echo "✗ E2E Tests: FAILED"
    fi
fi

if [ $RUN_SECURITY_TESTS -eq 1 ]; then
    if [ $SECURITY_TEST_FAILED -eq 0 ]; then
        echo "✓ Security Tests: PASSED"
    else
        echo "✗ Security Tests: FAILED"
    fi
fi

if [ $RUN_PERFORMANCE_TESTS -eq 1 ]; then
    if [ $PERFORMANCE_TEST_FAILED -eq 0 ]; then
        echo "✓ Performance Tests: PASSED"
    else
        echo "✗ Performance Tests: FAILED"
    fi
fi

if [ $RUN_CONTRACT_TESTS -eq 1 ]; then
    if [ $CONTRACT_TEST_FAILED -eq 0 ]; then
        echo "✓ Contract Tests: PASSED"
    else
        echo "✗ Contract Tests: FAILED"
    fi
fi

if [ $RUN_CONFIG_TESTS -eq 1 ]; then
    if [ $CONFIG_TEST_FAILED -eq 0 ]; then
        echo "✓ Configuration Tests: PASSED"
    else
        echo "✗ Configuration Tests: FAILED"
    fi
fi

if [ $RUN_INFRASTRUCTURE_TESTS -eq 1 ]; then
    if [ $INFRASTRUCTURE_TEST_FAILED -eq 0 ]; then
        echo "✓ Infrastructure Tests: PASSED"
    else
        echo "✗ Infrastructure Tests: FAILED"
    fi
fi

if [ $RUN_API_TESTS -eq 1 ]; then
    if [ $API_TEST_FAILED -eq 0 ]; then
        echo "✓ API Tests: PASSED"
    else
        echo "✗ API Tests: FAILED"
    fi
fi

if [ $RUN_GRAPHQL_TESTS -eq 1 ]; then
    if [ $GRAPHQL_TEST_FAILED -eq 0 ]; then
        echo "✓ GraphQL Tests: PASSED"
    else
        echo "✗ GraphQL Tests: FAILED"
    fi
fi

if [ $RUN_COMPLIANCE_TESTS -eq 1 ]; then
    if [ $COMPLIANCE_TEST_FAILED -eq 0 ]; then
        echo "✓ Compliance Tests: PASSED"
    else
        echo "✗ Compliance Tests: FAILED"
    fi
fi

if [ $RUN_RETENTION_TESTS -eq 1 ]; then
    if [ $RETENTION_TEST_FAILED -eq 0 ]; then
        echo "✓ Retention Tests: PASSED"
    else
        echo "✗ Retention Tests: FAILED"
    fi
fi

if [ $RUN_DOCUMENT_SHARING_TESTS -eq 1 ]; then
    if [ $DOCUMENT_SHARING_TEST_FAILED -eq 0 ]; then
        echo "✓ Document Sharing Tests: PASSED"
    else
        echo "✗ Document Sharing Tests: FAILED"
    fi
fi

if [ $RUN_CONVERSION_TESTS -eq 1 ]; then
    if [ $CONVERSION_TEST_FAILED -eq 0 ]; then
        echo "✓ Document Conversion Tests: PASSED"
    else
        echo "✗ Document Conversion Tests: FAILED"
    fi
fi

# Extended File Processing Tests (always run)
if [ $EXTENDED_PROCESSING_TEST_FAILED -eq 0 ]; then
    echo "✓ Extended File Processing Tests: PASSED"
else
    echo "✗ Extended File Processing Tests: FAILED"
fi

# Virus Scanning Tests (always run)
if [ $VIRUS_SCANNING_TEST_FAILED -eq 0 ]; then
    echo "✓ Virus Scanning Tests: PASSED"
else
    echo "✗ Virus Scanning Tests: FAILED"
fi

if [ $RUN_DOCUMENTATION_TESTS -eq 1 ]; then
    if [ $DOCUMENTATION_TEST_FAILED -eq 0 ]; then
        echo "✓ Documentation and Automation Tests: PASSED"
    else
        echo "✗ Documentation and Automation Tests: FAILED"
    fi
fi

echo
echo "Reports available in: $REPORT_DIR"
echo "- Surefire Reports: $REPORT_DIR/surefire-reports/"
echo "- Failsafe Reports: $REPORT_DIR/failsafe-reports/"
echo "- Coverage Report: $REPORT_DIR/coverage/index.html"
echo "- Custom Reports: $REPORT_DIR/custom-reports/"
echo "- Infrastructure Reports: $REPORT_DIR/infrastructure-reports/"
echo "- Documentation Reports: $REPORT_DIR/documentation-reports/"
echo "- Shell Logs: $REPORT_DIR/shell-logs/"
echo "- Execution Log: $LOG_FILE"

# Calculate overall result
TOTAL_FAILURES=$((UNIT_TEST_FAILED + INTEGRATION_TEST_FAILED + E2E_TEST_FAILED + SECURITY_TEST_FAILED + PERFORMANCE_TEST_FAILED + CONTRACT_TEST_FAILED + CONFIG_TEST_FAILED + INFRASTRUCTURE_TEST_FAILED + API_TEST_FAILED + GRAPHQL_TEST_FAILED + COMPLIANCE_TEST_FAILED + RETENTION_TEST_FAILED + DOCUMENT_SHARING_TEST_FAILED + CONVERSION_TEST_FAILED + DOCUMENTATION_TEST_FAILED + EXTENDED_PROCESSING_TEST_FAILED + VIRUS_SCANNING_TEST_FAILED))

# Count total test suites run (Extended Processing Tests and Virus Scanning Tests always run, so add 2)
TOTAL_SUITES_RUN=$((RUN_UNIT_TESTS + RUN_INTEGRATION_TESTS + RUN_E2E_TESTS + RUN_SECURITY_TESTS + RUN_PERFORMANCE_TESTS + RUN_CONTRACT_TESTS + RUN_CONFIG_TESTS + RUN_INFRASTRUCTURE_TESTS + RUN_API_TESTS + RUN_GRAPHQL_TESTS + RUN_COMPLIANCE_TESTS + RUN_RETENTION_TESTS + RUN_DOCUMENT_SHARING_TESTS + RUN_CONVERSION_TESTS + RUN_DOCUMENTATION_TESTS + 2))

echo
echo "Test Suites Run: $TOTAL_SUITES_RUN"
echo "Test Suites Failed: $TOTAL_FAILURES"

if [ $TOTAL_FAILURES -eq 0 ]; then
    echo
    echo "🎉 OVERALL RESULT: ALL TESTS PASSED"
    echo "========================================"
    exit 0
else
    echo
    echo "❌ OVERALL RESULT: $TOTAL_FAILURES TEST SUITE(S) FAILED"
    echo "========================================"
    exit 1
fi
