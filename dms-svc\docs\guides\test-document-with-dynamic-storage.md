# 📄 Testing Document Operations with Dynamic Storage Configuration

## 🎯 **Objective**
Verify that document upload/download operations work correctly with the database-driven storage configuration.

## 🔧 **Test Scenarios**

### **1. Test Document Upload with Dynamic LOCAL Storage**

#### **GraphQL Mutation:**
```graphql
mutation {
  uploadDocument(
    file: "test-document.pdf"
    title: "Test Document with Dynamic Storage"
    description: "Testing document upload with database-driven storage configuration"
    tags: ["test", "dynamic-storage", "local"]
    accessRoles: ["USER", "ADMIN"]
  ) {
    id
    title
    description
    fileName
    filePath
    storageProvider
    fileSize
    mimeType
    uploadDate
    uploadedBy
    tags
    accessRoles
  }
}
```

**Expected Result:**
- Document should be uploaded to LOCAL storage (./storage/documents/)
- `storageProvider` field should show "LOCAL"
- File should be physically stored in the configured local path

### **2. Test Storage Provider Switch**

#### **Step 1: Activate S3 Configuration**
```graphql
mutation {
  saveStorageConfiguration(input: {
    id: "2"
    providerType: S3
    isActive: true
    isDefault: false
    configurationJson: "{\"bucketName\":\"test-bucket\",\"region\":\"us-east-1\",\"accessKey\":\"test\",\"secretKey\":\"test\"}"
    description: "Test S3 Configuration"
    priority: 200
    healthCheckEnabled: true
  }) {
    id
    providerType
    isActive
    description
  }
}
```

#### **Step 2: Set S3 as Default**
```graphql
mutation {
  setStorageConfigurationAsDefault(configurationId: "2")
}
```

#### **Step 3: Upload Document with S3 Storage**
```graphql
mutation {
  uploadDocument(
    file: "test-document-s3.pdf"
    title: "Test Document with S3 Storage"
    description: "Testing document upload with S3 storage configuration"
    tags: ["test", "dynamic-storage", "s3"]
    accessRoles: ["USER", "ADMIN"]
  ) {
    id
    title
    storageProvider
    filePath
  }
}
```

**Expected Result:**
- Document should be uploaded to S3 storage
- `storageProvider` field should show "S3"
- File should be stored in the configured S3 bucket

### **3. Test Configuration Health Check**

#### **Check Provider Availability:**
```graphql
query {
  isStorageProviderAvailable(providerType: LOCAL)
}

query {
  isStorageProviderAvailable(providerType: S3)
}
```

#### **Test Configuration:**
```graphql
mutation {
  testStorageConfiguration(configurationId: "1")
}

mutation {
  testStorageConfiguration(configurationId: "2")
}
```

### **4. Test Fallback Mechanism**

#### **Scenario: Database Unavailable**
1. Stop database temporarily
2. Restart application
3. Verify fallback to application.properties (should fail gracefully)
4. Restart database
5. Verify dynamic configuration resumes

### **5. Test Document Download with Dynamic Storage**

#### **Download Document:**
```graphql
query {
  downloadDocument(documentId: "1") {
    id
    title
    fileName
    storageProvider
    downloadUrl
    fileSize
    mimeType
  }
}
```

**Expected Result:**
- Document should be retrieved from the correct storage provider
- Download should work regardless of which provider is configured

## 📊 **Test Results Tracking**

### **Test 1: LOCAL Storage Upload**
- [ ] Document uploaded successfully
- [ ] Storage provider correctly identified as LOCAL
- [ ] File physically stored in ./storage/documents/
- [ ] Database record created with correct storage provider

### **Test 2: Dynamic Provider Switch**
- [ ] S3 configuration activated successfully
- [ ] Default provider switched to S3
- [ ] New documents uploaded to S3
- [ ] Existing LOCAL documents still accessible

### **Test 3: Health Monitoring**
- [ ] Provider availability check working
- [ ] Configuration test functionality working
- [ ] Health status updated correctly

### **Test 4: Fallback Mechanism**
- [ ] Graceful degradation when database unavailable
- [ ] Automatic recovery when database restored
- [ ] No data loss during failover

### **Test 5: Document Download**
- [ ] Documents downloadable from LOCAL storage
- [ ] Documents downloadable from S3 storage
- [ ] Correct storage provider identified in response

## 🎯 **Success Criteria**

✅ **All tests pass successfully**
✅ **No application restart required for configuration changes**
✅ **Storage provider correctly identified in all operations**
✅ **Fallback mechanism works as expected**
✅ **Health monitoring provides accurate status**
✅ **Admin interface allows complete configuration management**

## 📝 **Notes**

- Test with actual file uploads using GraphQL file upload
- Monitor application logs for storage provider selection
- Verify physical file storage locations
- Test with different file types and sizes
- Validate security and access controls

## 🚀 **Production Readiness Checklist**

- [ ] All test scenarios pass
- [ ] Performance testing completed
- [ ] Security testing completed
- [ ] Documentation updated
- [ ] Monitoring and alerting configured
- [ ] Backup and recovery procedures tested
