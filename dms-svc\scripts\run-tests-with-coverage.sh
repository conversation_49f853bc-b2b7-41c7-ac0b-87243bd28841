#!/bin/bash

# Comprehensive Test Execution Script with Coverage Reporting
# This script runs all tests and generates coverage reports for the DMS Workflow functionality

set -e  # Exit on any error

echo "=========================================="
echo "DMS Workflow Test Suite with Coverage"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if <PERSON>ven is available
if ! command -v mvn &> /dev/null; then
    print_error "Maven is not installed or not in PATH"
    exit 1
fi

print_status "Starting comprehensive test execution..."

# Clean previous test results and coverage data
print_status "Cleaning previous test results..."
mvn clean -q

# Run unit tests with coverage
print_status "Running unit tests..."
if mvn test -q; then
    print_success "Unit tests completed successfully"
else
    print_error "Unit tests failed"
    exit 1
fi

# Run integration tests
print_status "Running integration tests..."
if mvn verify -q; then
    print_success "Integration tests completed successfully"
else
    print_warning "Integration tests had issues, but continuing..."
fi

# Generate JaCoCo coverage report
print_status "Generating coverage reports..."
mvn jacoco:report -q

# Generate Surefire test reports
print_status "Generating test reports..."
mvn surefire-report:report -q

# Check if coverage reports were generated
COVERAGE_REPORT="target/site/jacoco/index.html"
TEST_REPORT="target/site/surefire-report.html"

if [ -f "$COVERAGE_REPORT" ]; then
    print_success "Coverage report generated: $COVERAGE_REPORT"
else
    print_warning "Coverage report not found at $COVERAGE_REPORT"
fi

if [ -f "$TEST_REPORT" ]; then
    print_success "Test report generated: $TEST_REPORT"
else
    print_warning "Test report not found at $TEST_REPORT"
fi

# Extract coverage summary from JaCoCo report
print_status "Extracting coverage summary..."

# Check if jacoco.csv exists for summary
COVERAGE_CSV="target/site/jacoco/jacoco.csv"
if [ -f "$COVERAGE_CSV" ]; then
    echo ""
    echo "=========================================="
    echo "COVERAGE SUMMARY"
    echo "=========================================="
    
    # Parse CSV and calculate overall coverage
    python3 -c "
import csv
import sys

try:
    with open('$COVERAGE_CSV', 'r') as f:
        reader = csv.DictReader(f)
        total_instructions = 0
        covered_instructions = 0
        total_branches = 0
        covered_branches = 0
        total_lines = 0
        covered_lines = 0
        
        for row in reader:
            if 'workflow' in row['PACKAGE'].lower() or 'workflow' in row['CLASS'].lower():
                total_instructions += int(row['INSTRUCTION_MISSED']) + int(row['INSTRUCTION_COVERED'])
                covered_instructions += int(row['INSTRUCTION_COVERED'])
                total_branches += int(row['BRANCH_MISSED']) + int(row['BRANCH_COVERED'])
                covered_branches += int(row['BRANCH_COVERED'])
                total_lines += int(row['LINE_MISSED']) + int(row['LINE_COVERED'])
                covered_lines += int(row['LINE_COVERED'])
        
        if total_instructions > 0:
            instruction_coverage = (covered_instructions / total_instructions) * 100
            print(f'Workflow Instruction Coverage: {instruction_coverage:.1f}% ({covered_instructions}/{total_instructions})')
        
        if total_branches > 0:
            branch_coverage = (covered_branches / total_branches) * 100
            print(f'Workflow Branch Coverage: {branch_coverage:.1f}% ({covered_branches}/{total_branches})')
        
        if total_lines > 0:
            line_coverage = (covered_lines / total_lines) * 100
            print(f'Workflow Line Coverage: {line_coverage:.1f}% ({covered_lines}/{total_lines})')
            
except Exception as e:
    print(f'Could not parse coverage data: {e}')
" 2>/dev/null || print_warning "Could not parse coverage summary"
fi

# Run specific workflow tests and show results
print_status "Running workflow-specific tests..."
echo ""
echo "=========================================="
echo "WORKFLOW TEST RESULTS"
echo "=========================================="

# Run workflow tests specifically
mvn test -Dtest="*Workflow*Test" -q 2>/dev/null || print_warning "Some workflow tests may have failed"

# Count test files
UNIT_TESTS=$(find src/test/java -name "*Workflow*Test.java" | wc -l)
INTEGRATION_TESTS=$(find src/test/java -name "*Workflow*IntegrationTest.java" | wc -l)
SECURITY_TESTS=$(find src/test/java -name "*WorkflowSecurity*Test.java" | wc -l)

echo "Test Files Created:"
echo "  - Unit Tests: $UNIT_TESTS"
echo "  - Integration Tests: $INTEGRATION_TESTS"
echo "  - Security Tests: $SECURITY_TESTS"
echo "  - Total: $((UNIT_TESTS + INTEGRATION_TESTS + SECURITY_TESTS))"

# List all workflow test files
echo ""
echo "Workflow Test Files:"
find src/test/java -name "*Workflow*Test.java" -o -name "*Workflow*IntegrationTest.java" | sort

echo ""
echo "=========================================="
echo "REPORTS LOCATION"
echo "=========================================="
echo "Coverage Report: file://$(pwd)/$COVERAGE_REPORT"
echo "Test Report: file://$(pwd)/$TEST_REPORT"
echo "JaCoCo CSV: $(pwd)/$COVERAGE_CSV"

echo ""
print_success "Test execution completed!"
print_status "Open the coverage report in your browser to view detailed results"

# Optional: Open reports in browser (uncomment if desired)
# if command -v xdg-open &> /dev/null; then
#     xdg-open "$COVERAGE_REPORT"
# elif command -v open &> /dev/null; then
#     open "$COVERAGE_REPORT"
# fi
