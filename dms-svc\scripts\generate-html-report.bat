@echo off
REM DMS Service - Generate HTML Test Report
REM Generates an HTML test report from existing XML test results

echo ========================================
echo DMS Service - HTML Test Report Generator
echo ========================================
echo.

set PROJECT_ROOT=%~dp0..
set SCRIPT_DIR=%~dp0

REM Check if XML test results exist
if not exist "%PROJECT_ROOT%\target\surefire-reports\TEST-*.xml" (
    echo ⚠ No test XML results found!
    echo.
    echo Please run tests first to generate XML results:
    echo   scripts\run-all-tests.bat
    echo.
    echo Or run specific test categories:
    echo   scripts\run-all-tests.bat --unit-only
    echo   scripts\run-all-tests.bat --integration-only
    echo.
    pause
    exit /b 1
)

echo Generating HTML test report from XML results...
echo.

REM Generate HTML report using PowerShell script
powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%generate-simple-report.ps1"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ HTML test report generated successfully!
    echo.
    echo Report location: %PROJECT_ROOT%\target\site\surefire-report.html
    echo.
    echo Opening report in default browser...
    start "" "file:///%PROJECT_ROOT%\target\site\surefire-report.html"
    echo.
    echo You can also manually open the report at:
    echo file:///%PROJECT_ROOT%\target\site\surefire-report.html
) else (
    echo.
    echo ✗ Failed to generate HTML test report
    echo Check the PowerShell script for errors
    pause
    exit /b 1
)

echo.
echo ========================================
echo HTML report generation complete
echo ========================================
echo.
