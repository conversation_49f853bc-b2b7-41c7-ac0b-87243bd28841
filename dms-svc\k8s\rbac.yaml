# Service Account for DMS Service
apiVersion: v1
kind: ServiceAccount
metadata:
  name: dms-service-account
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: service-account
    app.kubernetes.io/part-of: dms-system
automountServiceAccountToken: true

---
# ClusterRole for DMS Service
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: dms-cluster-role
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: cluster-role
    app.kubernetes.io/part-of: dms-system
rules:
# Read access to nodes for health checks and metrics
- apiGroups: [""]
  resources: ["nodes", "nodes/metrics"]
  verbs: ["get", "list"]
# Read access to pods for service discovery
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
# Read access to services for service discovery
- apiGroups: [""]
  resources: ["services", "endpoints"]
  verbs: ["get", "list", "watch"]
# Read access to configmaps and secrets (limited)
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
# Access to custom resources if needed
- apiGroups: ["apiextensions.k8s.io"]
  resources: ["customresourcedefinitions"]
  verbs: ["get", "list"]

---
# Role for DMS Service (namespace-specific)
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: dms-role
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: role
    app.kubernetes.io/part-of: dms-system
rules:
# Full access to own resources
- apiGroups: [""]
  resources: ["pods", "pods/log", "pods/status"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["services", "endpoints"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
# Access to deployments and replicasets
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets", "statefulsets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
# Access to ingress resources
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
# Access to horizontal pod autoscalers
- apiGroups: ["autoscaling"]
  resources: ["horizontalpodautoscalers"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
# Access to network policies
- apiGroups: ["networking.k8s.io"]
  resources: ["networkpolicies"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---
# ClusterRoleBinding for DMS Service
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: dms-cluster-role-binding
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: cluster-role-binding
    app.kubernetes.io/part-of: dms-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: dms-cluster-role
subjects:
- kind: ServiceAccount
  name: dms-service-account
  namespace: dms-system

---
# RoleBinding for DMS Service
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: dms-role-binding
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: role-binding
    app.kubernetes.io/part-of: dms-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: dms-role
subjects:
- kind: ServiceAccount
  name: dms-service-account
  namespace: dms-system

---
# Service Account for Monitoring Components
apiVersion: v1
kind: ServiceAccount
metadata:
  name: monitoring-service-account
  namespace: dms-system
  labels:
    app.kubernetes.io/name: monitoring
    app.kubernetes.io/component: service-account
    app.kubernetes.io/part-of: dms-system
automountServiceAccountToken: true

---
# ClusterRole for Monitoring (Prometheus)
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: monitoring-cluster-role
  labels:
    app.kubernetes.io/name: monitoring
    app.kubernetes.io/component: cluster-role
    app.kubernetes.io/part-of: dms-system
rules:
# Access to metrics endpoints
- apiGroups: [""]
  resources: ["nodes", "nodes/metrics", "services", "endpoints", "pods"]
  verbs: ["get", "list", "watch"]
# Access to non-resource URLs for metrics
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]
# Access to configmaps for service discovery
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get"]
# Access to ingresses for service discovery
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]

---
# ClusterRoleBinding for Monitoring
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: monitoring-cluster-role-binding
  labels:
    app.kubernetes.io/name: monitoring
    app.kubernetes.io/component: cluster-role-binding
    app.kubernetes.io/part-of: dms-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: monitoring-cluster-role
subjects:
- kind: ServiceAccount
  name: monitoring-service-account
  namespace: dms-system

---
# Pod Security Policy (if PSP is enabled)
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: dms-psp
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: pod-security-policy
    app.kubernetes.io/part-of: dms-system
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
  readOnlyRootFilesystem: false

---
# Role for Pod Security Policy
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: dms-psp-role
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: psp-role
    app.kubernetes.io/part-of: dms-system
rules:
- apiGroups: ['policy']
  resources: ['podsecuritypolicies']
  verbs: ['use']
  resourceNames:
  - dms-psp

---
# RoleBinding for Pod Security Policy
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: dms-psp-role-binding
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: psp-role-binding
    app.kubernetes.io/part-of: dms-system
roleRef:
  kind: Role
  name: dms-psp-role
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: dms-service-account
  namespace: dms-system
- kind: ServiceAccount
  name: monitoring-service-account
  namespace: dms-system
