# SharePoint Integration User Guide - DMS Service

## Overview

This guide provides comprehensive instructions for configuring and using SharePoint Online integration with the Document Management Service (DMS). The integration uses Microsoft Graph API to seamlessly store and retrieve documents from SharePoint document libraries.

## Prerequisites

### System Requirements
- **SharePoint Online**: Office 365 or Microsoft 365 subscription
- **Azure AD**: Access to Azure Active Directory for app registration
- **Permissions**: SharePoint administrator or Application administrator rights
- **Network**: Internet connectivity for Microsoft Graph API access

### Supported Features
- ✅ Document upload/download to SharePoint
- ✅ Document versioning with SharePoint version history
- ✅ Folder organization with year/month structure
- ✅ Metadata synchronization
- ✅ OAuth2 authentication with token refresh
- ✅ Automatic retry and error handling
- ✅ Site and document library flexibility

## Configuration Setup

### Step 1: Azure App Registration

#### 1.1 Create Azure AD Application

1. **Access Azure Portal**
   - Navigate to [Azure Portal](https://portal.azure.com)
   - Sign in with administrator credentials

2. **Register New Application**
   - Go to **Azure Active Directory** > **App registrations**
   - Click **New registration**
   - Fill in application details:
     ```
     Name: DMS SharePoint Integration
     Supported account types: Single tenant
     Redirect URI: Not required for this integration
     ```
   - Click **Register**

3. **Note Application Details**
   - Copy **Application (client) ID**
   - Copy **Directory (tenant) ID**
   - These will be needed for configuration

#### 1.2 Configure API Permissions

1. **Add Microsoft Graph Permissions**
   - Go to **API permissions** tab
   - Click **Add a permission**
   - Select **Microsoft Graph** > **Application permissions**
   - Add the following permissions:
     ```
     Sites.ReadWrite.All
     Files.ReadWrite.All
     Sites.Selected (optional, for specific site access)
     ```

2. **Grant Admin Consent**
   - Click **Grant admin consent for [Your Organization]**
   - Confirm the consent

#### 1.3 Create Client Secret

1. **Generate Secret**
   - Go to **Certificates & secrets** tab
   - Click **New client secret**
   - Add description: "DMS SharePoint Integration Secret"
   - Select expiration period (recommended: 24 months)
   - Click **Add**

2. **Save Secret Value**
   - Copy the secret **Value** (not the Secret ID)
   - ⚠️ **Important**: Save this immediately - it won't be shown again

### Step 2: SharePoint Site Preparation

#### 2.1 Identify SharePoint Site

1. **Get Site URL**
   - Navigate to your SharePoint site
   - Copy the full URL (e.g., `https://contoso.sharepoint.com/sites/documents`)

2. **Create or Identify Document Library**
   - Ensure you have a document library for DMS files
   - Default library name: "Documents"
   - Custom library: Create new library if needed

#### 2.2 Test Site Access

1. **Verify Permissions**
   - Ensure the registered app has access to the site
   - Test using Graph Explorer if needed:
     ```
     GET https://graph.microsoft.com/v1.0/sites/{site-id}
     ```

### Step 3: DMS Configuration

#### 3.1 Application Properties

Add the following configuration to your `application.properties` or `application.yml`:

```properties
# SharePoint Storage Configuration
dms.storage.provider=SHAREPOINT

# SharePoint OAuth2 Settings
dms.storage.sharepoint.client-id=your-client-id-here
dms.storage.sharepoint.client-secret=your-client-secret-here
dms.storage.sharepoint.tenant-id=your-tenant-id-here

# SharePoint Site Configuration
dms.storage.sharepoint.site-url=https://yourcompany.sharepoint.com/sites/dms
dms.storage.sharepoint.document-library=Documents

# Microsoft Graph API Configuration
dms.storage.sharepoint.graph-api-url=https://graph.microsoft.com/v1.0
dms.storage.sharepoint.token-url=https://login.microsoftonline.com/${dms.storage.sharepoint.tenant-id}/oauth2/v2.0/token

# Performance and Reliability Settings
dms.storage.sharepoint.max-retries=3
dms.storage.sharepoint.retry-delay-ms=1000
dms.storage.sharepoint.debug=false
```

#### 3.2 Environment Variables (Production)

For production deployments, use environment variables:

```bash
# Required Settings
export DMS_STORAGE_PROVIDER=SHAREPOINT
export DMS_STORAGE_SHAREPOINT_CLIENT_ID=your-client-id
export DMS_STORAGE_SHAREPOINT_CLIENT_SECRET=your-client-secret
export DMS_STORAGE_SHAREPOINT_TENANT_ID=your-tenant-id
export DMS_STORAGE_SHAREPOINT_SITE_URL=https://yourcompany.sharepoint.com/sites/dms
export DMS_STORAGE_SHAREPOINT_DOCUMENT_LIBRARY=Documents

# Optional Settings
export DMS_STORAGE_SHAREPOINT_MAX_RETRIES=3
export DMS_STORAGE_SHAREPOINT_RETRY_DELAY_MS=1000
export DMS_STORAGE_SHAREPOINT_DEBUG=false
```

#### 3.3 Docker Configuration

For Docker deployments, add to your `docker-compose.yml`:

```yaml
version: '3.8'
services:
  dms:
    image: dms-service:latest
    environment:
      - DMS_STORAGE_PROVIDER=SHAREPOINT
      - DMS_STORAGE_SHAREPOINT_CLIENT_ID=${SHAREPOINT_CLIENT_ID}
      - DMS_STORAGE_SHAREPOINT_CLIENT_SECRET=${SHAREPOINT_CLIENT_SECRET}
      - DMS_STORAGE_SHAREPOINT_TENANT_ID=${SHAREPOINT_TENANT_ID}
      - DMS_STORAGE_SHAREPOINT_SITE_URL=${SHAREPOINT_SITE_URL}
      - DMS_STORAGE_SHAREPOINT_DOCUMENT_LIBRARY=${SHAREPOINT_DOCUMENT_LIBRARY}
```

## Usage Guide

### Document Operations

#### Upload Documents

**GraphQL Mutation:**
```graphql
mutation UploadDocument($file: Upload!) {
  uploadDocument(input: {
    file: $file
    title: "My Document"
    description: "Document stored in SharePoint"
  }) {
    id
    title
    filename
    storagePath
    storageProvider
    createdAt
  }
}
```

**REST API:**
```bash
curl -X POST \
  http://localhost:8080/dms/api/documents/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@document.pdf" \
  -F "title=My Document" \
  -F "description=Document stored in SharePoint"
```

#### Download Documents

**GraphQL Query:**
```graphql
query DownloadDocument($id: ID!) {
  downloadDocument(id: $id) {
    content
    filename
    contentType
    size
  }
}
```

**REST API:**
```bash
curl -X GET \
  http://localhost:8080/dms/api/documents/123/download \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o downloaded-document.pdf
```

#### Search Documents

**GraphQL Query:**
```graphql
query SearchDocuments {
  searchDocuments(input: {
    title: "report"
    storageProvider: SHAREPOINT
    pagination: { page: 0, size: 20 }
  }) {
    content {
      id
      title
      filename
      storagePath
      storageProvider
      createdAt
    }
    totalElements
  }
}
```

### File Organization

#### Directory Structure

SharePoint files are organized as follows:
```
Document Library/
├── 2025/
│   ├── 01/
│   │   ├── document_20250115_143022_001.pdf
│   │   └── report_20250116_091543_002.docx
│   ├── 02/
│   │   └── presentation_20250201_100000_003.pptx
│   └── 06/
│       └── current_document_20250609_140000_004.xlsx
└── 2024/
    └── 12/
        └── archived_document_20241215_120000_005.pdf
```

#### File Naming Convention

- **Format**: `{sanitized_filename}_{timestamp}_{random_suffix}.{extension}`
- **Timestamp**: `YYYYMMDD_HHMMSS_SSS`
- **Example**: `my_document_20250609_143022_123.pdf`

#### Filename Sanitization

SharePoint has specific filename restrictions:
- **Forbidden characters**: `< > : " / \ | ? *`
- **Replacement**: Forbidden characters replaced with `_`
- **Spaces**: Converted to `_`
- **Case**: Converted to lowercase

## Advanced Configuration

### Custom Site and Library

#### Different Document Library

```properties
# Use custom document library
dms.storage.sharepoint.document-library=DMSDocuments
```

#### Specific SharePoint Site

```properties
# Use specific site (not default)
dms.storage.sharepoint.site-url=https://yourcompany.sharepoint.com/sites/finance
```

### Authentication Options

#### Certificate-Based Authentication (Advanced)

For enhanced security, you can use certificate-based authentication:

```properties
# Certificate-based auth (instead of client secret)
dms.storage.sharepoint.certificate-path=/path/to/certificate.p12
dms.storage.sharepoint.certificate-password=certificate-password
```

### Performance Tuning

#### Retry Configuration

```properties
# Adjust retry settings for your environment
dms.storage.sharepoint.max-retries=5
dms.storage.sharepoint.retry-delay-ms=2000
```

#### Debug Mode

```properties
# Enable debug logging
dms.storage.sharepoint.debug=true
logging.level.com.ascentbusiness.dms_svc.service.SharePoint=DEBUG
```

## Monitoring and Troubleshooting

### Health Checks

#### Verify SharePoint Connection

```bash
# Check application health
curl http://localhost:8080/dms/actuator/health

# Check SharePoint specific health
curl http://localhost:8080/dms/actuator/health/sharepoint
```

#### Test Authentication

Use the DMS API to test SharePoint authentication:

```graphql
query TestSharePointConnection {
  systemInfo {
    storageProvider
    storageStatus
    configuration {
      sharepoint {
        siteUrl
        documentLibrary
        connected
        lastTestedAt
      }
    }
  }
}
```

### Common Issues and Solutions

#### 1. Authentication Failures

**Problem**: `SharePointAuthenticationException: Authentication failed`

**Solutions**:
- Verify client ID and secret are correct
- Check tenant ID matches your organization
- Ensure app permissions are granted and consented
- Verify app registration is not expired

**Debug Steps**:
```bash
# Enable debug logging
logging.level.com.ascentbusiness.dms_svc.service.SharePointAuthProvider=DEBUG

# Check token endpoint response
curl -X POST https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/token \
  -d "grant_type=client_credentials" \
  -d "client_id={client-id}" \
  -d "client_secret={client-secret}" \
  -d "scope=https://graph.microsoft.com/.default"
```

#### 2. Site Access Issues

**Problem**: `SharePointException: Site not found or access denied`

**Solutions**:
- Verify site URL is correct and accessible
- Check app has Sites.ReadWrite.All permissions
- Ensure site exists and is not archived
- Verify SharePoint Online license is active

**Debug Steps**:
```bash
# Test site access via Graph Explorer
https://developer.microsoft.com/en-us/graph/graph-explorer

# Query: GET https://graph.microsoft.com/v1.0/sites/{hostname}:/{site-path}
```

#### 3. Document Library Issues

**Problem**: `SharePointException: Document library not found`

**Solutions**:
- Verify document library name is correct
- Check library exists and is not hidden
- Ensure app has Files.ReadWrite.All permissions
- Create library if it doesn't exist

#### 4. File Upload Failures

**Problem**: Files fail to upload with timeout or size errors

**Solutions**:
- Check file size limits (SharePoint: 250GB max)
- Verify network connectivity and speed
- Increase retry settings for large files
- Check SharePoint storage quota

**Configuration for Large Files**:
```properties
# Increase timeouts for large files
dms.storage.sharepoint.max-retries=5
dms.storage.sharepoint.retry-delay-ms=3000
server.servlet.multipart.max-file-size=100MB
server.servlet.multipart.max-request-size=100MB
```

### Logging Configuration

#### Enable SharePoint Logging

```properties
# Enable comprehensive SharePoint logging
logging.level.com.ascentbusiness.dms_svc.service.SharePointStorageService=DEBUG
logging.level.com.ascentbusiness.dms_svc.service.SharePointAuthProvider=DEBUG
logging.level.org.springframework.web.client.RestTemplate=DEBUG
```

#### Log Analysis

Look for these log patterns:

```
# Successful authentication
INFO SharePointAuthProvider - Successfully refreshed SharePoint access token

# File operations
INFO SharePointStorageService - Successfully uploaded file to SharePoint: key=2025/06/document_20250609_143022_123.pdf
INFO SharePointStorageService - Successfully downloaded file from SharePoint: key=2025/06/document_20250609_143022_123.pdf

# Errors
ERROR SharePointAuthProvider - Failed to refresh SharePoint access token
ERROR SharePointStorageService - Failed to upload file to SharePoint: key=document.pdf
```

## Performance Considerations

### Upload Performance

- **Small files (< 1MB)**: Excellent performance
- **Medium files (1-10MB)**: Good performance with retry logic
- **Large files (> 10MB)**: May require timeout adjustments

### Network Optimization

```properties
# Connection pool settings for better performance
server.tomcat.threads.max=200
server.tomcat.accept-count=100
server.tomcat.max-connections=8192
```

### Caching Strategy

SharePoint operations benefit from caching:

```properties
# Enable caching for metadata operations
spring.cache.type=redis
spring.cache.redis.time-to-live=600000
```

## Security Best Practices

### Client Secret Management

1. **Rotate Secrets Regularly**
   - Set calendar reminders for secret expiration
   - Use Azure Key Vault for secret management
   - Implement automated secret rotation

2. **Environment-Specific Secrets**
   - Use different app registrations for dev/test/prod
   - Never commit secrets to version control
   - Use environment variables or secret management systems

### Access Control

1. **Principle of Least Privilege**
   - Grant only necessary permissions
   - Use Sites.Selected when possible for specific sites
   - Regular permission audits

2. **Network Security**
   - Use HTTPS only
   - Implement firewall rules
   - Consider VPN for sensitive environments

## Migration Guide

### From Local/S3 to SharePoint

#### 1. Prepare SharePoint Environment

- Set up Azure app registration
- Configure SharePoint site and library
- Test connectivity

#### 2. Update Configuration

```properties
# Change storage provider
dms.storage.provider=SHAREPOINT

# Add SharePoint configuration
dms.storage.sharepoint.client-id=your-client-id
# ... other SharePoint settings
```

#### 3. Migrate Existing Files (Optional)

Use the DMS migration tools:

```bash
# Run migration command
java -jar dms-svc.jar --migrate-storage \
  --from=LOCAL \
  --to=SHAREPOINT \
  --batch-size=10
```

### Zero-Downtime Migration

1. **Deploy new configuration** without changing storage provider
2. **Test SharePoint connectivity** using health checks
3. **Switch storage provider** during maintenance window
4. **Verify all operations** work correctly

## Compliance and Governance

### Data Residency

- SharePoint data residency follows Microsoft 365 tenant location
- Review Microsoft 365 compliance features
- Consider regional compliance requirements

### Audit and Compliance

- DMS audit logs capture all SharePoint operations
- SharePoint maintains its own audit logs
- Integration with Microsoft 365 compliance center

### Backup and Recovery

- SharePoint Online includes built-in backup and versioning
- Consider additional backup solutions for critical data
- Test recovery procedures regularly

## API Reference

### SharePoint-Specific Endpoints

#### Health Check
```
GET /dms/actuator/health/sharepoint
```

#### Configuration Test
```
POST /dms/api/admin/test-sharepoint-connection
```

#### Storage Migration
```
POST /dms/api/admin/migrate-storage
Content-Type: application/json
{
  "from": "LOCAL",
  "to": "SHAREPOINT",
  "batchSize": 10
}
```

## Support and Resources

### Microsoft Resources

- [Microsoft Graph API Documentation](https://docs.microsoft.com/en-us/graph/)
- [SharePoint REST API Reference](https://docs.microsoft.com/en-us/sharepoint/dev/sp-add-ins/sharepoint-rest-service)
- [Azure AD App Registration Guide](https://docs.microsoft.com/en-us/azure/active-directory/develop/quickstart-register-app)

### DMS Resources

- [API Documentation](../api/API_DOCUMENTATION.md)
- [Troubleshooting Guide](../troubleshooting/TROUBLESHOOTING_GUIDE.md)
- [Performance Guide](../performance/PERFORMANCE_BENCHMARKING_GUIDE.md)

### Getting Help

1. **Check Logs**: Enable debug logging and check application logs
2. **Health Checks**: Use built-in health check endpoints
3. **Test Connectivity**: Use Graph Explorer to test permissions
4. **Community Support**: Stack Overflow, Microsoft Tech Community
5. **Enterprise Support**: Microsoft Premier Support, Azure Support

---

This guide provides comprehensive information for setting up and using SharePoint integration with the DMS service. For additional help or specific use cases, refer to the troubleshooting section or contact your system administrator.
