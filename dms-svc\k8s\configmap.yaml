# ConfigMap for DMS Service Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: dms-config
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: dms-system
data:
  # Application Configuration
  SPRING_PROFILES_ACTIVE: "prod,k8s"
  SERVER_PORT: "8080"
  SERVER_SERVLET_CONTEXT_PATH: "/dms"
  
  # Database Configuration
  SPRING_DATASOURCE_URL: "****************************************************************************************************"
  SPRING_DATASOURCE_USERNAME: "dms_user"
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "com.mysql.cj.jdbc.Driver"
  
  # Redis Configuration
  SPRING_REDIS_HOST: "redis-service"
  SPRING_REDIS_PORT: "6379"
  SPRING_REDIS_TIMEOUT: "2000ms"
  
  # Cache Configuration
  SPRING_CACHE_TYPE: "redis"
  SPRING_CACHE_REDIS_TIME_TO_LIVE: "1800000"
  SPRING_CACHE_REDIS_CACHE_NULL_VALUES: "false"
  SPRING_CACHE_REDIS_USE_KEY_PREFIX: "true"
  SPRING_CACHE_REDIS_KEY_PREFIX: "dms:"
  
  # Elasticsearch Configuration
  SPRING_ELASTICSEARCH_URIS: "http://elasticsearch-service:9200"
  SPRING_ELASTICSEARCH_CONNECTION_TIMEOUT: "10s"
  SPRING_ELASTICSEARCH_SOCKET_TIMEOUT: "30s"
  
  # Storage Configuration
  DMS_STORAGE_PROVIDER: "S3"
  DMS_STORAGE_LOCAL_BASE_PATH: "/app/storage"
  DMS_STORAGE_LOCAL_CREATE_DIRECTORIES: "true"
  
  # File Upload Configuration
  SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE: "100MB"
  SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE: "100MB"
  SPRING_SERVLET_MULTIPART_ENABLED: "true"
  
  # JWT Configuration
  DMS_JWT_HEADER: "Authorization"
  DMS_JWT_PREFIX: "Bearer"
  JWT_EXPIRATION: "86400000"
  
  # Actuator Configuration
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,info,metrics,prometheus"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: "when-authorized"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_COMPONENTS: "always"
  MANAGEMENT_HEALTH_DEFAULTS_ENABLED: "true"
  
  # Health Indicators
  MANAGEMENT_HEALTH_DB_ENABLED: "true"
  MANAGEMENT_HEALTH_DISKSPACE_ENABLED: "true"
  MANAGEMENT_HEALTH_REDIS_ENABLED: "true"
  MANAGEMENT_HEALTH_PING_ENABLED: "true"
  MANAGEMENT_HEALTH_LIVENESSSTATE_ENABLED: "true"
  MANAGEMENT_HEALTH_READINESSSTATE_ENABLED: "true"
  
  # Metrics Configuration
  MANAGEMENT_METRICS_EXPORT_PROMETHEUS_ENABLED: "true"
  MANAGEMENT_METRICS_DISTRIBUTION_PERCENTILES_HISTOGRAM_HTTP_SERVER_REQUESTS: "true"
  MANAGEMENT_METRICS_TAGS_APPLICATION: "dms-service"
  MANAGEMENT_METRICS_TAGS_ENVIRONMENT: "kubernetes"
  
  # OpenTelemetry Configuration
  OTEL_SERVICE_NAME: "dms-svc"
  OTEL_SERVICE_VERSION: "1.0.0"
  OTEL_RESOURCE_ATTRIBUTES: "service.name=dms-svc,service.version=1.0.0,deployment.environment=kubernetes"
  
  # Tracing Configuration
  OTEL_TRACES_EXPORTER: "otlp"
  OTEL_EXPORTER_OTLP_ENDPOINT: "http://jaeger-collector:14250"
  OTEL_TRACES_SAMPLER: "traceidratio"
  OTEL_TRACES_SAMPLER_ARG: "0.1"
  
  # Metrics Configuration
  OTEL_METRICS_EXPORTER: "prometheus"
  OTEL_METRIC_EXPORT_INTERVAL: "30s"
  
  # Logs Configuration
  OTEL_LOGS_EXPORTER: "otlp"
  
  # Logging Configuration
  LOGGING_LEVEL_ROOT: "INFO"
  LOGGING_LEVEL_COM_ASCENTBUSINESS_DMS: "INFO"
  LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_SECURITY: "WARN"
  
  # Log Files Configuration
  LOGGING_FILE_NAME: "/app/logs/dms-application.log"
  LOGGING_LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE: "100MB"
  LOGGING_LOGBACK_ROLLINGPOLICY_MAX_HISTORY: "30"
  LOGGING_LOGBACK_ROLLINGPOLICY_TOTAL_SIZE_CAP: "1GB"
  
  # Security Configuration
  DMS_SECURITY_CORS_ALLOWED_ORIGINS: "*"
  DMS_SECURITY_CORS_ALLOWED_METHODS: "GET,POST,PUT,DELETE,OPTIONS"
  DMS_SECURITY_CORS_ALLOWED_HEADERS: "*"
  DMS_SECURITY_CORS_ALLOW_CREDENTIALS: "true"
  
  # Rate Limiting Configuration
  DMS_SECURITY_RATE_LIMIT_ENABLED: "true"
  DMS_SECURITY_RATE_LIMIT_REQUESTS_PER_MINUTE: "100"
  DMS_SECURITY_RATE_LIMIT_BURST_CAPACITY: "200"
  
  # Security Headers Configuration
  DMS_SECURITY_HEADERS_FRAME_OPTIONS: "DENY"
  DMS_SECURITY_HEADERS_CONTENT_TYPE_OPTIONS: "nosniff"
  DMS_SECURITY_HEADERS_XSS_PROTECTION: "1; mode=block"
  DMS_SECURITY_HEADERS_REFERRER_POLICY: "strict-origin-when-cross-origin"
  DMS_SECURITY_HEADERS_HSTS_ENABLED: "true"
  DMS_SECURITY_HEADERS_HSTS_MAX_AGE: "31536000"
  DMS_SECURITY_HEADERS_HSTS_INCLUDE_SUBDOMAINS: "true"
  
  # API Versioning Configuration
  DMS_API_VERSION_CURRENT: "1.0"
  DMS_API_VERSION_SUPPORTED: "1.0,1.1"
  DMS_API_VERSION_DEPRECATION_WARNING: "true"
  DMS_API_VERSION_STRICT_MODE: "false"
  
  # Performance Configuration
  SPRING_JPA_PROPERTIES_HIBERNATE_JDBC_BATCH_SIZE: "25"
  SPRING_JPA_PROPERTIES_HIBERNATE_ORDER_INSERTS: "true"
  SPRING_JPA_PROPERTIES_HIBERNATE_ORDER_UPDATES: "true"
  SPRING_JPA_PROPERTIES_HIBERNATE_JDBC_BATCH_VERSIONED_DATA: "true"
  
  # Environment
  ENVIRONMENT: "kubernetes"
  LOG_LEVEL: "INFO"
