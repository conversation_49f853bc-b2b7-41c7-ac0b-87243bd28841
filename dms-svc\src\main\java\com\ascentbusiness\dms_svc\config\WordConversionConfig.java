package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for Word to PDF conversion functionality.
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dms.word-conversion")
public class WordConversionConfig {

    /**
     * Maximum file size for Word conversion in bytes.
     * Default: 52428800 (50MB)
     */
    private long maxFileSize = 52428800L;

    /**
     * Conversion timeout in seconds.
     * Default: 300 (5 minutes)
     */
    private int timeoutSeconds = 300;

    /**
     * Default virus scanner type for Word conversion.
     * Default: MOCK
     */
    private VirusScannerType virusScanner = VirusScannerType.MOCK;

    /**
     * Whether Word conversion feature is enabled.
     * Default: true
     */
    private boolean enabled = true;

    /**
     * Temporary directory for conversion operations.
     * If empty, uses system temp directory.
     */
    private String tempDirectory = "";

    /**
     * Hours after which temporary files are cleaned up.
     * Default: 24 hours
     */
    private int cleanupAfterHours = 24;

    /**
     * Get formatted file size string for display.
     * 
     * @return formatted file size (e.g., "50.0 MB", "1.5 GB")
     */
    public String getMaxFileSizeFormatted() {
        if (maxFileSize < 1024) {
            return maxFileSize + " bytes";
        } else if (maxFileSize < 1024 * 1024) {
            return String.format("%.1f KB", maxFileSize / 1024.0);
        } else if (maxFileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", maxFileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", maxFileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
