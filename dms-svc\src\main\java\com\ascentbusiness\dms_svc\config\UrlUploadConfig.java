/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Configuration properties for URL-based document uploads.
 * 
 * <p>This configuration class manages settings for downloading files from URLs
 * and uploading them to the document management system. It includes security
 * settings, timeout configurations, and content validation options.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Configuration
@ConfigurationProperties(prefix = "dms.upload.url")
@Data
public class UrlUploadConfig {
    
    /**
     * Whether URL uploads are enabled.
     */
    private boolean enabled = true;
    
    /**
     * Timeout for URL downloads in milliseconds.
     */
    private int timeoutMs = 30000;
    
    /**
     * Maximum file size for URL downloads in bytes.
     */
    private long maxFileSize = 104857600L; // 100MB
    
    /**
     * Maximum number of redirects to follow.
     */
    private int maxRedirects = 5;
    
    /**
     * User agent string for HTTP requests.
     */
    private String userAgent = "DMS-Service/1.0";
    
    /**
     * Comma-separated list of allowed domains.
     * Empty means all domains are allowed.
     */
    private String allowedDomains = "";
    
    /**
     * Whether to validate SSL certificates.
     */
    private boolean validateSsl = true;
    
    /**
     * Whether to allow downloads from private IP addresses.
     */
    private boolean allowPrivateIps = false;
    
    /**
     * Comma-separated list of blocked ports.
     */
    private String blockedPorts = "22,23,25,53,135,139,445,1433,1521,3306,3389,5432,6379";
    
    /**
     * Comma-separated list of allowed content types.
     * Empty means all content types are allowed.
     */
    private String allowedContentTypes = "";
    
    /**
     * Comma-separated list of blocked content types.
     */
    private String blockedContentTypes = "text/html,application/javascript,text/javascript";
    
    // Derived properties
    
    /**
     * Get the set of allowed domains.
     * 
     * @return set of allowed domains, empty if all domains are allowed
     */
    public Set<String> getAllowedDomainsSet() {
        if (allowedDomains == null || allowedDomains.trim().isEmpty()) {
            return Set.of();
        }
        return Arrays.stream(allowedDomains.split(","))
                .map(String::trim)
                .map(String::toLowerCase)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
    }
    
    /**
     * Get the set of blocked ports.
     * 
     * @return set of blocked port numbers
     */
    public Set<Integer> getBlockedPortsSet() {
        if (blockedPorts == null || blockedPorts.trim().isEmpty()) {
            return Set.of();
        }
        return Arrays.stream(blockedPorts.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Integer::parseInt)
                .collect(Collectors.toSet());
    }
    
    /**
     * Get the set of allowed content types.
     * 
     * @return set of allowed content types, empty if all types are allowed
     */
    public Set<String> getAllowedContentTypesSet() {
        if (allowedContentTypes == null || allowedContentTypes.trim().isEmpty()) {
            return Set.of();
        }
        return Arrays.stream(allowedContentTypes.split(","))
                .map(String::trim)
                .map(String::toLowerCase)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
    }
    
    /**
     * Get the set of blocked content types.
     * 
     * @return set of blocked content types
     */
    public Set<String> getBlockedContentTypesSet() {
        if (blockedContentTypes == null || blockedContentTypes.trim().isEmpty()) {
            return Set.of();
        }
        return Arrays.stream(blockedContentTypes.split(","))
                .map(String::trim)
                .map(String::toLowerCase)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
    }
    
    /**
     * Check if a domain is allowed.
     * 
     * @param domain the domain to check
     * @return true if the domain is allowed
     */
    public boolean isDomainAllowed(String domain) {
        if (domain == null) {
            return false;
        }
        
        Set<String> allowedSet = getAllowedDomainsSet();
        if (allowedSet.isEmpty()) {
            return true; // All domains allowed
        }
        
        String normalizedDomain = domain.toLowerCase();
        return allowedSet.contains(normalizedDomain) || 
               allowedSet.stream().anyMatch(allowed -> normalizedDomain.endsWith("." + allowed));
    }
    
    /**
     * Check if a port is blocked.
     * 
     * @param port the port to check
     * @return true if the port is blocked
     */
    public boolean isPortBlocked(int port) {
        return getBlockedPortsSet().contains(port);
    }
    
    /**
     * Check if a content type is allowed.
     * 
     * @param contentType the content type to check
     * @return true if the content type is allowed
     */
    public boolean isContentTypeAllowed(String contentType) {
        if (contentType == null) {
            return true;
        }
        
        String normalizedType = contentType.toLowerCase();
        
        // Check blocked types first
        Set<String> blockedSet = getBlockedContentTypesSet();
        if (blockedSet.stream().anyMatch(blocked -> normalizedType.startsWith(blocked))) {
            return false;
        }
        
        // Check allowed types
        Set<String> allowedSet = getAllowedContentTypesSet();
        if (allowedSet.isEmpty()) {
            return true; // All types allowed
        }
        
        return allowedSet.stream().anyMatch(allowed -> normalizedType.startsWith(allowed));
    }
}