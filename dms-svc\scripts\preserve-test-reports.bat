@echo off
R<PERSON> Script to preserve test reports when running individual tests
REM This script backs up existing surefire-report.html before running tests
REM and restores it if it gets deleted

setlocal enabledelayedexpansion

set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."
set "SITE_DIR=%PROJECT_ROOT%\target\site"
set "REPORT_FILE=%SITE_DIR%\surefire-report.html"
set "BACKUP_FILE=%SITE_DIR%\surefire-report.html.backup"
set "TEMP_BACKUP_DIR=%TEMP%\dms-test-reports-backup"

REM Create temp backup directory
if not exist "%TEMP_BACKUP_DIR%" mkdir "%TEMP_BACKUP_DIR%"

REM Function to backup existing reports
:backup_reports
if exist "%REPORT_FILE%" (
    echo Backing up existing test report...
    copy "%REPORT_FILE%" "%BACKUP_FILE%" >nul 2>&1
    if exist "%SITE_DIR%\css" (
        xcopy "%SITE_DIR%\css" "%TEMP_BACKUP_DIR%\css\" /E /I /Q >nul 2>&1
    )
    if exist "%SITE_DIR%\images" (
        xcopy "%SITE_DIR%\images" "%TEMP_BACKUP_DIR%\images\" /E /I /Q >nul 2>&1
    )
    echo ✓ Test report backed up
)
goto :eof

REM Function to restore reports if they were deleted
:restore_reports
if not exist "%REPORT_FILE%" (
    if exist "%BACKUP_FILE%" (
        echo Restoring test report...
        copy "%BACKUP_FILE%" "%REPORT_FILE%" >nul 2>&1
        if exist "%TEMP_BACKUP_DIR%\css" (
            xcopy "%TEMP_BACKUP_DIR%\css" "%SITE_DIR%\css\" /E /I /Q >nul 2>&1
        )
        if exist "%TEMP_BACKUP_DIR%\images" (
            xcopy "%TEMP_BACKUP_DIR%\images" "%SITE_DIR%\images\" /E /I /Q >nul 2>&1
        )
        echo ✓ Test report restored
    )
)
goto :eof

REM Function to regenerate report from existing XML files
:regenerate_report
if exist "%PROJECT_ROOT%\target\surefire-reports\TEST-*.xml" (
    echo Regenerating HTML test report from XML results...
    powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%generate-simple-report.ps1"
    if %ERRORLEVEL% EQU 0 (
        echo ✓ HTML test report regenerated successfully
    ) else (
        echo ⚠ Failed to regenerate HTML test report
    )
) else (
    echo ⚠ No XML test results found to regenerate report
)
goto :eof

REM Main execution based on command line argument
if "%1"=="backup" (
    call :backup_reports
) else if "%1"=="restore" (
    call :restore_reports
) else if "%1"=="regenerate" (
    call :regenerate_report
) else if "%1"=="cleanup" (
    REM Clean up backup files
    if exist "%BACKUP_FILE%" del "%BACKUP_FILE%" >nul 2>&1
    if exist "%TEMP_BACKUP_DIR%" rmdir /s /q "%TEMP_BACKUP_DIR%" >nul 2>&1
    echo ✓ Backup files cleaned up
) else (
    echo Usage: preserve-test-reports.bat [backup^|restore^|regenerate^|cleanup]
    echo.
    echo Commands:
    echo   backup     - Backup existing test reports
    echo   restore    - Restore backed up test reports
    echo   regenerate - Regenerate HTML report from XML results
    echo   cleanup    - Clean up backup files
    echo.
    echo This script helps preserve the surefire-report.html when running individual tests
)

endlocal
