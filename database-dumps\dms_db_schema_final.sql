﻿-- MySQL dump 10.13  Distrib 8.0.43, for Linux (x86_64)
--
-- Host: localhost    Database: dms_db
-- ------------------------------------------------------
-- Server version	8.0.43

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Temporary view structure for view `api_deprecation_summary`
--

DROP TABLE IF EXISTS `api_deprecation_summary`;
/*!50001 DROP VIEW IF EXISTS `api_deprecation_summary`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `api_deprecation_summary` AS SELECT 
 1 AS `version`,
 1 AS `element_type`,
 1 AS `element_name`,
 1 AS `deprecated_since`,
 1 AS `removed_in`,
 1 AS `replacement_element`,
 1 AS `affected_users`,
 1 AS `total_warnings`,
 1 AS `latest_warning`,
 1 AS `end_of_life_date`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `api_deprecation_warnings`
--

DROP TABLE IF EXISTS `api_deprecation_warnings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `api_deprecation_warnings` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `version` varchar(20) NOT NULL,
  `element_type` enum('FIELD','TYPE','OPERATION','ARGUMENT') NOT NULL,
  `element_name` varchar(255) NOT NULL,
  `deprecated_since` varchar(20) DEFAULT NULL,
  `removed_in` varchar(20) DEFAULT NULL,
  `replacement_element` varchar(255) DEFAULT NULL,
  `warning_message` text,
  `user_id` varchar(100) DEFAULT NULL,
  `warning_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `warning_count` bigint DEFAULT '1',
  `last_warning_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_api_deprecation_warnings_version` (`version`),
  KEY `idx_api_deprecation_warnings_element` (`element_type`,`element_name`),
  KEY `idx_api_deprecation_warnings_user_id` (`user_id`),
  KEY `idx_api_deprecation_warnings_timestamp` (`warning_timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Deprecation warning tracking for API evolution management';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `api_version_usage`
--

DROP TABLE IF EXISTS `api_version_usage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `api_version_usage` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `version` varchar(20) NOT NULL COMMENT 'API version used for the request',
  `user_id` varchar(100) DEFAULT NULL,
  `client_ip` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `operation_name` varchar(255) DEFAULT NULL,
  `operation_type` enum('QUERY','MUTATION','SUBSCRIPTION') NOT NULL COMMENT 'GraphQL operation type',
  `request_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `response_time_ms` bigint DEFAULT NULL,
  `success` tinyint(1) DEFAULT '1',
  `error_message` text,
  `deprecation_warning` tinyint(1) DEFAULT '0' COMMENT 'Whether a deprecation warning was issued',
  `correlation_id` varchar(100) DEFAULT NULL COMMENT 'Request correlation ID for tracing',
  PRIMARY KEY (`id`),
  KEY `idx_api_version_usage_version` (`version`),
  KEY `idx_api_version_usage_user_id` (`user_id`),
  KEY `idx_api_version_usage_timestamp` (`request_timestamp`),
  KEY `idx_api_version_usage_operation` (`operation_name`,`operation_type`),
  KEY `idx_api_version_usage_correlation_id` (`correlation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='API version usage tracking for analytics and monitoring';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Temporary view structure for view `api_version_usage_stats`
--

DROP TABLE IF EXISTS `api_version_usage_stats`;
/*!50001 DROP VIEW IF EXISTS `api_version_usage_stats`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `api_version_usage_stats` AS SELECT 
 1 AS `version`,
 1 AS `usage_date`,
 1 AS `total_requests`,
 1 AS `unique_users`,
 1 AS `avg_response_time`,
 1 AS `successful_requests`,
 1 AS `failed_requests`,
 1 AS `deprecation_warnings`,
 1 AS `unique_operations`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `api_versions`
--

DROP TABLE IF EXISTS `api_versions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `api_versions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `version` varchar(20) NOT NULL COMMENT 'Semantic version number (e.g., 1.0, 1.1)',
  `release_date` date NOT NULL,
  `is_current` tinyint(1) DEFAULT '0' COMMENT 'Indicates if this is the current API version',
  `is_supported` tinyint(1) DEFAULT '1' COMMENT 'Indicates if this version is still supported',
  `is_deprecated` tinyint(1) DEFAULT '0' COMMENT 'Indicates if this version is deprecated',
  `deprecation_date` date DEFAULT NULL,
  `end_of_life_date` date DEFAULT NULL,
  `description` text,
  `breaking_changes` text COMMENT 'Description of breaking changes in this version',
  `migration_notes` text COMMENT 'Notes for migrating to/from this version',
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `version` (`version`),
  KEY `idx_api_versions_version` (`version`),
  KEY `idx_api_versions_is_current` (`is_current`),
  KEY `idx_api_versions_is_supported` (`is_supported`),
  KEY `idx_api_versions_is_deprecated` (`is_deprecated`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='API version registry with lifecycle information';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `async_processing_jobs`
--

DROP TABLE IF EXISTS `async_processing_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `async_processing_jobs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `completed_at` datetime(6) DEFAULT NULL,
  `correlation_id` varchar(255) DEFAULT NULL,
  `error_message` text,
  `estimated_time_remaining` int DEFAULT NULL,
  `file_name` varchar(500) NOT NULL,
  `file_size` bigint NOT NULL,
  `job_id` varchar(255) NOT NULL,
  `processing_strategy` enum('ASYNC','CHUNKED','DIRECT') NOT NULL,
  `progress` decimal(5,2) DEFAULT NULL,
  `started_at` datetime(6) DEFAULT NULL,
  `status` enum('COMPLETED','FAILED','PROCESSING','QUEUED') NOT NULL,
  `document_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UKo437pqgaffrdgtac40b0jyrh` (`job_id`),
  KEY `FKfi8bpu5yhng77gh9h5x2mcp9j` (`document_id`),
  CONSTRAINT `FKfi8bpu5yhng77gh9h5x2mcp9j` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `audit_chain_metadata`
--

DROP TABLE IF EXISTS `audit_chain_metadata`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audit_chain_metadata` (
  `is_active` bit(1) DEFAULT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `current_sequence` bigint NOT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_block_timestamp` datetime(6) DEFAULT NULL,
  `total_entries` bigint DEFAULT NULL,
  `hash_algorithm` varchar(50) NOT NULL,
  `signature_algorithm` varchar(50) NOT NULL,
  `signing_key_fingerprint` varchar(64) DEFAULT NULL,
  `chain_id` varchar(100) NOT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `genesis_hash` varchar(128) NOT NULL,
  `last_block_hash` varchar(128) DEFAULT NULL,
  `chain_name` varchar(255) NOT NULL,
  `description` text,
  `verification_key` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK170phppoltjrx3p1f7g074ws` (`chain_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `audit_encryption_keys`
--

DROP TABLE IF EXISTS `audit_encryption_keys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audit_encryption_keys` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `key_id` varchar(100) NOT NULL,
  `key_data` text NOT NULL,
  `algorithm` varchar(50) NOT NULL DEFAULT 'AES-256-GCM',
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `rotation_date` timestamp NULL DEFAULT NULL,
  `usage_count` bigint DEFAULT '0',
  `last_used_date` timestamp NULL DEFAULT NULL,
  `created_by` varchar(100) DEFAULT 'SYSTEM',
  `notes` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_id` (`key_id`),
  KEY `idx_audit_encryption_keys_key_id` (`key_id`),
  KEY `idx_audit_encryption_keys_is_active` (`is_active`),
  KEY `idx_audit_encryption_keys_created_date` (`created_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Audit encryption key management table for key rotation and lifecycle';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `audit_encryption_metrics`
--

DROP TABLE IF EXISTS `audit_encryption_metrics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audit_encryption_metrics` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `metric_date` date NOT NULL,
  `total_audit_logs` bigint DEFAULT '0',
  `encrypted_audit_logs` bigint DEFAULT '0',
  `encryption_success_rate` decimal(5,2) DEFAULT '0.00',
  `decryption_success_rate` decimal(5,2) DEFAULT '0.00',
  `key_rotations` bigint DEFAULT '0',
  `encryption_errors` bigint DEFAULT '0',
  `decryption_errors` bigint DEFAULT '0',
  `performance_avg_encrypt_ms` decimal(10,3) DEFAULT '0.000',
  `performance_avg_decrypt_ms` decimal(10,3) DEFAULT '0.000',
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_audit_encryption_metrics_date` (`metric_date`),
  KEY `idx_audit_encryption_metrics_created_date` (`created_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Audit encryption metrics and monitoring data';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Temporary view structure for view `audit_encryption_status`
--

DROP TABLE IF EXISTS `audit_encryption_status`;
/*!50001 DROP VIEW IF EXISTS `audit_encryption_status`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `audit_encryption_status` AS SELECT 
 1 AS `audit_date`,
 1 AS `total_logs`,
 1 AS `encrypted_logs`,
 1 AS `encryption_percentage`,
 1 AS `unique_keys_used`,
 1 AS `event_type`,
 1 AS `event_category`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `audit_exports`
--

DROP TABLE IF EXISTS `audit_exports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audit_exports` (
  `download_count` int DEFAULT NULL,
  `is_archived` bit(1) DEFAULT NULL,
  `retention_period_days` int DEFAULT NULL,
  `archive_date` datetime(6) DEFAULT NULL,
  `completion_date` datetime(6) DEFAULT NULL,
  `compliance_framework_id` bigint DEFAULT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `end_date` datetime(6) DEFAULT NULL,
  `file_size_bytes` bigint DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_download_date` datetime(6) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `request_date` datetime(6) DEFAULT NULL,
  `signature_timestamp` datetime(6) DEFAULT NULL,
  `start_date` datetime(6) DEFAULT NULL,
  `total_records` bigint DEFAULT NULL,
  `export_format` varchar(20) NOT NULL,
  `export_status` varchar(20) DEFAULT NULL,
  `export_type` varchar(50) NOT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `export_id` varchar(100) NOT NULL,
  `export_reason` varchar(100) DEFAULT NULL,
  `requested_by` varchar(100) NOT NULL,
  `file_hash` varchar(128) DEFAULT NULL,
  `file_path` varchar(500) DEFAULT NULL,
  `digital_signature` text,
  `error_message` text,
  `filter_criteria` json DEFAULT NULL,
  `regulatory_requirement` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UKg1ligusfkdwcbfv13h09wxyk4` (`export_id`),
  KEY `FKi1mhw8ttg290hdwc54ye0f1rc` (`compliance_framework_id`),
  CONSTRAINT `FKi1mhw8ttg290hdwc54ye0f1rc` FOREIGN KEY (`compliance_framework_id`) REFERENCES `compliance_frameworks` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `audit_logs`
--

DROP TABLE IF EXISTS `audit_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audit_logs` (
  `export_count` int DEFAULT NULL,
  `is_archived` bit(1) DEFAULT NULL,
  `is_tampered` bit(1) DEFAULT NULL,
  `retention_period_days` int DEFAULT NULL,
  `archive_date` datetime(6) DEFAULT NULL,
  `chain_sequence` bigint DEFAULT NULL,
  `compliance_framework_id` bigint DEFAULT NULL,
  `document_id` bigint DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_export_date` datetime(6) DEFAULT NULL,
  `signature_timestamp` datetime(6) DEFAULT NULL,
  `tamper_detection_date` datetime(6) DEFAULT NULL,
  `timestamp` datetime(6) NOT NULL,
  `business_impact` varchar(20) DEFAULT NULL,
  `risk_level` varchar(20) DEFAULT NULL,
  `verification_status` varchar(20) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `data_subject_category` varchar(50) DEFAULT NULL,
  `geographic_region` varchar(50) DEFAULT NULL,
  `signature_algorithm` varchar(50) DEFAULT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `request_id` varchar(100) DEFAULT NULL,
  `session_id` varchar(100) DEFAULT NULL,
  `user_id` varchar(100) NOT NULL,
  `hash_value` varchar(128) DEFAULT NULL,
  `previous_hash` varchar(128) DEFAULT NULL,
  `after_state` json DEFAULT NULL,
  `before_state` json DEFAULT NULL,
  `client_info` json DEFAULT NULL,
  `details` text,
  `digital_signature` text,
  `regulation_reference` varchar(255) DEFAULT NULL,
  `technical_details` json DEFAULT NULL,
  `user_agent` text,
  `action` enum('COMPLIANCE_AUDIT_COMPLETED','COMPLIANCE_AUDIT_STARTED','COMPLIANCE_CLASSIFICATION_ASSIGNED','COMPLIANCE_CLASSIFICATION_CREATED','COMPLIANCE_CLASSIFICATION_DELETED','COMPLIANCE_CLASSIFICATION_REMOVED','COMPLIANCE_CLASSIFICATION_UPDATED','COMPLIANCE_FRAMEWORK_ACTIVATED','COMPLIANCE_FRAMEWORK_CREATED','COMPLIANCE_FRAMEWORK_DEACTIVATED','COMPLIANCE_FRAMEWORK_DELETED','COMPLIANCE_FRAMEWORK_UPDATED','CONVERSION_COMPLETED','CONVERSION_FAILED','CONVERSION_FILE_DOWNLOADED','CONVERSION_INITIATED','CONVERSION_VIRUS_SCAN_COMPLETED','CONVERSION_VIRUS_SCAN_STARTED','CREATE','CROSS_BORDER_TRANSFER_APPROVED','CROSS_BORDER_TRANSFER_BLOCKED','DATA_SUBJECT_ACCESS_GRANTED','DATA_SUBJECT_CONSENT_GRANTED','DATA_SUBJECT_CONSENT_WITHDRAWN','DATA_SUBJECT_DATA_DELETED','DATA_SUBJECT_DATA_EXPORTED','DATA_SUBJECT_RIGHTS_REQUEST','DELETE','DISPOSITION_APPROVED','DISPOSITION_REJECTED','DISPOSITION_REVIEW_REQUIRED','DOCUMENT_ARCHIVED','DOCUMENT_CLASSIFIED','DOCUMENT_COMPLIANCE_VALIDATED','DOCUMENT_COMPLIANCE_VIOLATION_DETECTED','DOCUMENT_COMPLIANCE_VIOLATION_RESOLVED','DOCUMENT_DECLASSIFIED','DOCUMENT_DECRYPTED','DOCUMENT_DISPOSED','DOCUMENT_ENCRYPTED','DOCUMENT_RECLASSIFIED','DOWNLOAD','ENCRYPTION_KEY_ROTATED','GEOGRAPHIC_RESTRICTION_APPLIED','GEOGRAPHIC_RESTRICTION_REMOVED','LEGAL_HOLD_APPLIED','LEGAL_HOLD_RELEASED','MIGRATION_COMPLETED','MIGRATION_FAILED','MIGRATION_FILE_CLEANUP','MIGRATION_FILE_PROCESSED','MIGRATION_FILE_VERIFIED','MIGRATION_SECURITY_CHECK','MIGRATION_STARTED','MIGRATION_VALIDATION_FAILED','PERMISSION_CHECKED','PERMISSION_EXPIRED','PERMISSION_GRANTED','PERMISSION_REVOKED','RATE_LIMIT_EXCEEDED','READ','REGULATORY_NOTIFICATION_SENT','REGULATORY_REPORT_GENERATED','RETENTION_EXPIRY_CALCULATED','RETENTION_POLICY_ASSIGNED','RETENTION_POLICY_CREATED','RETENTION_POLICY_DELETED','RETENTION_POLICY_UNASSIGNED','RETENTION_POLICY_UPDATED','RETENTION_PROCESSING_COMPLETED','RETENTION_PROCESSING_FAILED','RETENTION_PROCESSING_STARTED','SECURITY_VIOLATION','TOKEN_VALIDATION_FAILED','UPDATE','UPLOAD','VERSION_CREATE','VIRUS_DETECTED','VIRUS_SCANNER_UNAVAILABLE','VIRUS_SCAN_COMPLETED','VIRUS_SCAN_FAILED','VIRUS_SCAN_INITIATED','VIRUS_SCAN_TIMEOUT') NOT NULL,
  `event_category` enum('ACCESS_CONTROL','ADMINISTRATIVE','AUDIT_COMPLIANCE','AUTHENTICATION','BUSINESS_PROCESS','COMPLIANCE','CONSENT_MANAGEMENT','DATA_GOVERNANCE','DATA_MIGRATION','DATA_SUBJECT_RIGHTS','DOCUMENT_LIFECYCLE','GENERAL','INCIDENT','LEGAL_COMPLIANCE','PRIVACY','QUALITY_ASSURANCE','REGULATORY_REPORTING','RETENTION','RISK_MANAGEMENT','SECURITY','SYSTEM_MAINTENANCE','VIOLATION') DEFAULT NULL,
  `event_type` enum('ACCESS_CONTROL_EVENT','AUDIT_EVENT','AUTHENTICATION_EVENT','CLASSIFICATION_EVENT','COMPLIANCE_EVENT','CONFIGURATION_EVENT','CONSENT_EVENT','DATA_PROTECTION_EVENT','DATA_SUBJECT_EVENT','DISPOSITION_EVENT','DOCUMENT_OPERATION','LEGAL_HOLD_EVENT','MIGRATION_EVENT','REGULATORY_EVENT','RETENTION_EVENT','SECURITY_EVENT','SYSTEM_EVENT','VIOLATION_EVENT') DEFAULT NULL,
  `is_encrypted` tinyint(1) DEFAULT '0' COMMENT 'Indicates if sensitive fields are encrypted',
  `encryption_key_id` varchar(100) DEFAULT NULL COMMENT 'ID of the encryption key used',
  `encryption_iv` varchar(200) DEFAULT NULL COMMENT 'Initialization vector for GCM encryption',
  `encrypted_details` text COMMENT 'Encrypted version of details field',
  `encrypted_technical_details` text COMMENT 'Encrypted version of technical_details field',
  `encrypted_before_state` text COMMENT 'Encrypted version of before_state field',
  `encrypted_after_state` text COMMENT 'Encrypted version of after_state field',
  `encrypted_client_info` text COMMENT 'Encrypted version of client_info field',
  `success` bit(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKd68ggaet4dvmk182mqlcsri9b` (`document_id`),
  KEY `idx_audit_logs_is_encrypted` (`is_encrypted`),
  KEY `idx_audit_logs_encryption_key_id` (`encryption_key_id`),
  CONSTRAINT `FKd68ggaet4dvmk182mqlcsri9b` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=509 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Enhanced audit logs table with configurable encryption support for sensitive data';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `audit_summary_reports`
--

DROP TABLE IF EXISTS `audit_summary_reports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audit_summary_reports` (
  `is_archived` bit(1) DEFAULT NULL,
  `is_published` bit(1) DEFAULT NULL,
  `retention_period_days` int DEFAULT NULL,
  `archive_date` datetime(6) DEFAULT NULL,
  `compliance_events` bigint DEFAULT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `generated_date` datetime(6) DEFAULT NULL,
  `high_risk_events` bigint DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `period_end` datetime(6) NOT NULL,
  `period_start` datetime(6) NOT NULL,
  `publication_date` datetime(6) DEFAULT NULL,
  `report_file_size` bigint DEFAULT NULL,
  `security_events` bigint DEFAULT NULL,
  `total_events` bigint DEFAULT NULL,
  `violation_events` bigint DEFAULT NULL,
  `report_period` varchar(20) NOT NULL,
  `report_type` varchar(50) NOT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `generated_by` varchar(100) DEFAULT NULL,
  `report_id` varchar(100) NOT NULL,
  `report_hash` varchar(128) DEFAULT NULL,
  `report_file_path` varchar(500) DEFAULT NULL,
  `digital_signature` text,
  `summary_data` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK12uii95aakjnfna1kt01n4ep0` (`report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `audit_verification_log`
--

DROP TABLE IF EXISTS `audit_verification_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audit_verification_log` (
  `chain_integrity_valid` bit(1) DEFAULT NULL,
  `hash_match` bit(1) DEFAULT NULL,
  `signature_valid` bit(1) DEFAULT NULL,
  `audit_log_id` bigint DEFAULT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `verification_date` datetime(6) DEFAULT NULL,
  `overall_status` varchar(20) DEFAULT NULL,
  `verification_method` varchar(50) DEFAULT NULL,
  `verification_type` varchar(50) NOT NULL,
  `chain_id` varchar(100) DEFAULT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `verification_id` varchar(100) NOT NULL,
  `verified_by` varchar(100) DEFAULT NULL,
  `actual_hash` varchar(128) DEFAULT NULL,
  `expected_hash` varchar(128) DEFAULT NULL,
  `anomalies_detected` json DEFAULT NULL,
  `verification_details` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UKajm8u2gdu8edm4teccruijcoo` (`verification_id`),
  KEY `FK6opxdqpmt5qepe2coec18mp10` (`chain_id`),
  KEY `FKqp0tcsqd8ntyx7h575rtnd54a` (`audit_log_id`),
  CONSTRAINT `FK6opxdqpmt5qepe2coec18mp10` FOREIGN KEY (`chain_id`) REFERENCES `audit_chain_metadata` (`chain_id`),
  CONSTRAINT `FKqp0tcsqd8ntyx7h575rtnd54a` FOREIGN KEY (`audit_log_id`) REFERENCES `audit_logs` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5511 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bulk_share_items`
--

DROP TABLE IF EXISTS `bulk_share_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bulk_share_items` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `document_id` bigint NOT NULL,
  `error_message` varchar(1000) DEFAULT NULL,
  `is_role` bit(1) NOT NULL,
  `is_successful` bit(1) NOT NULL,
  `recipient_id` varchar(100) NOT NULL,
  `share_link_id` varchar(36) DEFAULT NULL,
  `operation_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_bulk_item_operation` (`operation_id`),
  KEY `idx_bulk_item_document` (`document_id`),
  KEY `idx_bulk_item_recipient` (`recipient_id`),
  KEY `idx_bulk_item_success` (`is_successful`),
  KEY `idx_bulk_item_role` (`is_role`),
  KEY `idx_bulk_item_share_link` (`share_link_id`),
  CONSTRAINT `FK23dvtp7phqrkcp3k2obvlib1f` FOREIGN KEY (`operation_id`) REFERENCES `bulk_share_operations` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bulk_share_operations`
--

DROP TABLE IF EXISTS `bulk_share_operations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bulk_share_operations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `completed_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `created_by_user_id` varchar(100) NOT NULL,
  `expires_at` datetime(6) NOT NULL,
  `failure_count` int NOT NULL,
  `is_completed` bit(1) NOT NULL,
  `notes` varchar(1000) DEFAULT NULL,
  `operation_id` varchar(36) NOT NULL,
  `permission` enum('ADMIN','DELETE','READ','WRITE') NOT NULL,
  `success_count` int NOT NULL,
  `total_documents` int NOT NULL,
  `total_recipients` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK6fu8ewjd0b44wld9m1lodpy74` (`operation_id`),
  KEY `idx_bulk_operation_id` (`operation_id`),
  KEY `idx_bulk_created_by` (`created_by_user_id`),
  KEY `idx_bulk_created_at` (`created_at`),
  KEY `idx_bulk_completed` (`is_completed`),
  KEY `idx_bulk_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `chunked_upload_chunks`
--

DROP TABLE IF EXISTS `chunked_upload_chunks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `chunked_upload_chunks` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `checksum` varchar(255) DEFAULT NULL,
  `chunk_number` int NOT NULL,
  `chunk_size` bigint NOT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `temp_file_path` varchar(1000) DEFAULT NULL,
  `uploaded_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKsbgmcj8un1epbkfac8nh696b7` (`session_id`),
  CONSTRAINT `FKsbgmcj8un1epbkfac8nh696b7` FOREIGN KEY (`session_id`) REFERENCES `chunked_upload_sessions` (`session_id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `chunked_upload_sessions`
--

DROP TABLE IF EXISTS `chunked_upload_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `chunked_upload_sessions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `chunk_size` int NOT NULL,
  `completed_at` datetime(6) DEFAULT NULL,
  `correlation_id` varchar(255) DEFAULT NULL,
  `error_message` text,
  `expires_at` datetime(6) NOT NULL,
  `file_name` varchar(500) NOT NULL,
  `last_activity_at` datetime(6) DEFAULT NULL,
  `progress` decimal(5,2) DEFAULT NULL,
  `received_bytes` bigint NOT NULL,
  `received_chunks` int NOT NULL,
  `session_id` varchar(255) NOT NULL,
  `status` varchar(50) NOT NULL,
  `temp_directory` varchar(1000) DEFAULT NULL,
  `total_chunks` int NOT NULL,
  `total_size` bigint NOT NULL,
  `document_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UKeoq8bby5w00vwsnpkr89dpt51` (`session_id`),
  KEY `FK65knfki2yoq8jribr76hip3mb` (`document_id`),
  CONSTRAINT `FK65knfki2yoq8jribr76hip3mb` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `classification_allowed_regions`
--

DROP TABLE IF EXISTS `classification_allowed_regions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `classification_allowed_regions` (
  `classification_id` bigint NOT NULL,
  `region` varchar(50) NOT NULL,
  PRIMARY KEY (`classification_id`,`region`),
  CONSTRAINT `classification_allowed_regions_ibfk_1` FOREIGN KEY (`classification_id`) REFERENCES `compliance_classifications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `classification_data_subjects`
--

DROP TABLE IF EXISTS `classification_data_subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `classification_data_subjects` (
  `classification_id` bigint NOT NULL,
  `data_subject_category` enum('ANONYMOUS','BENEFICIARY','CONTRACTOR','CUSTOMER','EMPLOYEE','MINOR','OTHER','PATIENT','PROSPECT','PSEUDONYMIZED','PUBLIC_FIGURE','SHAREHOLDER','STUDENT','VENDOR','VISITOR') DEFAULT NULL,
  KEY `FK8hfn70p7hqynxygpfhy3vtc2m` (`classification_id`),
  CONSTRAINT `FK8hfn70p7hqynxygpfhy3vtc2m` FOREIGN KEY (`classification_id`) REFERENCES `compliance_classifications` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `classification_regions`
--

DROP TABLE IF EXISTS `classification_regions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `classification_regions` (
  `classification_id` bigint NOT NULL,
  `region` enum('AU','BR','CA','CN','EU','GLOBAL','IN','JP','KR','MX','RESTRICTED','RU','SG','UK','US','ZA') DEFAULT NULL,
  KEY `FKc871dl382eyamr701ww3necs2` (`classification_id`),
  CONSTRAINT `FKc871dl382eyamr701ww3necs2` FOREIGN KEY (`classification_id`) REFERENCES `compliance_classifications` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `compliance_classifications`
--

DROP TABLE IF EXISTS `compliance_classifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `compliance_classifications` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL,
  `description` text,
  `classification_level` varchar(50) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `effective_date` timestamp NULL DEFAULT NULL,
  `expiry_date` timestamp NULL DEFAULT NULL,
  `color_code` varchar(7) DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `requires_approval` tinyint(1) DEFAULT '0',
  `approval_role` varchar(100) DEFAULT NULL,
  `requires_encryption` tinyint(1) DEFAULT '0',
  `requires_audit_logging` tinyint(1) DEFAULT '0',
  `max_retention_days` int DEFAULT NULL,
  `access_restrictions` text,
  `handling_instructions` text,
  `disposal_instructions` text,
  `priority` int DEFAULT '0',
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `UKmk7mkmh2jvq0ousabowmaybu0` (`name`),
  KEY `idx_classification_level` (`classification_level`),
  KEY `idx_classification_active` (`is_active`),
  KEY `idx_classification_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Stores compliance classification levels and requirements';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `compliance_framework_regions`
--

DROP TABLE IF EXISTS `compliance_framework_regions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `compliance_framework_regions` (
  `framework_id` bigint NOT NULL,
  `region` enum('AU','BR','CA','CN','EU','GLOBAL','IN','JP','KR','MX','RESTRICTED','RU','SG','UK','US','ZA') DEFAULT NULL,
  KEY `FKahphlk2fjqbcce4rr974v3hsx` (`framework_id`),
  CONSTRAINT `FKahphlk2fjqbcce4rr974v3hsx` FOREIGN KEY (`framework_id`) REFERENCES `compliance_frameworks` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `compliance_frameworks`
--

DROP TABLE IF EXISTS `compliance_frameworks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `compliance_frameworks` (
  `is_active` bit(1) NOT NULL,
  `max_retention_days` int DEFAULT NULL,
  `priority` int NOT NULL,
  `requires_audit_trail` bit(1) NOT NULL,
  `requires_consent_management` bit(1) NOT NULL,
  `requires_data_localization` bit(1) NOT NULL,
  `requires_data_subject_rights` bit(1) NOT NULL,
  `requires_encryption` bit(1) NOT NULL,
  `created_date` datetime(6) NOT NULL,
  `effective_date` datetime(6) DEFAULT NULL,
  `expiry_date` datetime(6) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_modified_date` datetime(6) NOT NULL,
  `version` varchar(50) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `authority` varchar(200) DEFAULT NULL,
  `name` varchar(200) NOT NULL,
  `website_url` varchar(500) DEFAULT NULL,
  `description` text,
  `implementation_notes` text,
  `notification_requirements` text,
  `penalty_information` text,
  `framework_type` enum('CCPA','COBIT','COSO','CUSTOM','FERPA','FISMA','GDPR','GLBA','HIPAA','ISO_27001','LGPD','NIST','PCI_DSS','PIPEDA','SOX') NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `compliance_violations`
--

DROP TABLE IF EXISTS `compliance_violations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `compliance_violations` (
  `actual_fine_amount` decimal(38,2) DEFAULT NULL,
  `is_resolved` bit(1) NOT NULL,
  `potential_fine_amount` decimal(38,2) DEFAULT NULL,
  `recurrence_count` int NOT NULL,
  `requires_notification` bit(1) NOT NULL,
  `requires_regulatory_report` bit(1) NOT NULL,
  `created_date` datetime(6) NOT NULL,
  `document_compliance_mapping_id` bigint NOT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_modified_date` datetime(6) NOT NULL,
  `notification_sent_date` datetime(6) DEFAULT NULL,
  `previous_violation_id` bigint DEFAULT NULL,
  `regulatory_report_sent_date` datetime(6) DEFAULT NULL,
  `remediation_deadline` datetime(6) DEFAULT NULL,
  `resolved_date` datetime(6) DEFAULT NULL,
  `violation_date` datetime(6) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `detected_by` varchar(100) DEFAULT NULL,
  `detection_method` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `resolved_by` varchar(100) DEFAULT NULL,
  `user_id` varchar(100) DEFAULT NULL,
  `violation_type` varchar(100) NOT NULL,
  `action_attempted` varchar(200) DEFAULT NULL,
  `control_failed` varchar(200) DEFAULT NULL,
  `notification_recipient` varchar(200) DEFAULT NULL,
  `regulation_violated` varchar(200) DEFAULT NULL,
  `regulatory_authority` varchar(200) DEFAULT NULL,
  `business_impact` text,
  `description` text NOT NULL,
  `lessons_learned` text,
  `remediation_action` text,
  `resolution_notes` text,
  `user_agent` text,
  `severity` enum('CRITICAL','HIGH','LOW','MEDIUM') NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKeg4i4mmhk2m8617o4yg9biexn` (`document_compliance_mapping_id`),
  CONSTRAINT `FKeg4i4mmhk2m8617o4yg9biexn` FOREIGN KEY (`document_compliance_mapping_id`) REFERENCES `document_compliance_mappings` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `database_configurations`
--

DROP TABLE IF EXISTS `database_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `database_configurations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `additional_properties` text,
  `auto_commit` bit(1) NOT NULL,
  `configuration_name` varchar(100) NOT NULL,
  `connection_test_message` text,
  `connection_test_result` varchar(50) DEFAULT NULL,
  `connection_timeout` int NOT NULL,
  `database_name` varchar(100) NOT NULL,
  `database_type` enum('H2','MARIADB','MYSQL','ORACLE','POSTGRESQL','SQLITE','SQL_SERVER') NOT NULL,
  `description` text,
  `host` varchar(255) DEFAULT NULL,
  `idle_timeout` int NOT NULL,
  `is_active` bit(1) NOT NULL,
  `is_default` bit(1) NOT NULL,
  `isolation_level` varchar(50) DEFAULT NULL,
  `jdbc_url` varchar(1000) NOT NULL,
  `last_connection_test` datetime(6) DEFAULT NULL,
  `max_lifetime` int NOT NULL,
  `max_pool_size` int NOT NULL,
  `min_pool_size` int NOT NULL,
  `password` varchar(255) NOT NULL,
  `port` int DEFAULT NULL,
  `read_only` bit(1) NOT NULL,
  `schema_name` varchar(100) DEFAULT NULL,
  `test_on_borrow` bit(1) NOT NULL,
  `username` varchar(100) NOT NULL,
  `validation_query` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UKdpans96hianricj8y8s7txk0a` (`configuration_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `databasechangelog`
--

DROP TABLE IF EXISTS `databasechangelog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `databasechangelog` (
  `ID` varchar(255) NOT NULL,
  `AUTHOR` varchar(255) NOT NULL,
  `FILENAME` varchar(255) NOT NULL,
  `DATEEXECUTED` datetime NOT NULL,
  `ORDEREXECUTED` int NOT NULL,
  `EXECTYPE` varchar(10) NOT NULL,
  `MD5SUM` varchar(35) DEFAULT NULL,
  `DESCRIPTION` varchar(255) DEFAULT NULL,
  `COMMENTS` varchar(255) DEFAULT NULL,
  `TAG` varchar(255) DEFAULT NULL,
  `LIQUIBASE` varchar(20) DEFAULT NULL,
  `CONTEXTS` varchar(255) DEFAULT NULL,
  `LABELS` varchar(255) DEFAULT NULL,
  `DEPLOYMENT_ID` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `databasechangeloglock`
--

DROP TABLE IF EXISTS `databasechangeloglock`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `databasechangeloglock` (
  `ID` int NOT NULL,
  `LOCKED` tinyint NOT NULL,
  `LOCKGRANTED` datetime DEFAULT NULL,
  `LOCKEDBY` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_access_regions`
--

DROP TABLE IF EXISTS `document_access_regions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_access_regions` (
  `mapping_id` bigint NOT NULL,
  `region` enum('AU','BR','CA','CN','EU','GLOBAL','IN','JP','KR','MX','RESTRICTED','RU','SG','UK','US','ZA') DEFAULT NULL,
  KEY `FK9ghnunfavcnpehpqvgc7xbq96` (`mapping_id`),
  CONSTRAINT `FK9ghnunfavcnpehpqvgc7xbq96` FOREIGN KEY (`mapping_id`) REFERENCES `document_compliance_mappings` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_classification_metadata`
--

DROP TABLE IF EXISTS `document_classification_metadata`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_classification_metadata` (
  `created_date` datetime(6) NOT NULL,
  `document_id` bigint NOT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_modified_date` datetime(6) NOT NULL,
  `language` varchar(10) DEFAULT NULL,
  `confidentiality_level` varchar(50) DEFAULT NULL,
  `business_unit` varchar(100) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `document_type` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `module` varchar(100) DEFAULT NULL,
  `region_location` varchar(100) DEFAULT NULL,
  `sub_module` varchar(100) DEFAULT NULL,
  `tags_keywords` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK8tp9sdo4p9g1hbxjeuna5oex3` (`document_id`),
  CONSTRAINT `FK8tp9sdo4p9g1hbxjeuna5oex3` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=86 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_compliance_data_subjects`
--

DROP TABLE IF EXISTS `document_compliance_data_subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_compliance_data_subjects` (
  `mapping_id` bigint NOT NULL,
  `data_subject_category` varchar(50) NOT NULL,
  PRIMARY KEY (`mapping_id`,`data_subject_category`),
  CONSTRAINT `document_compliance_data_subjects_ibfk_1` FOREIGN KEY (`mapping_id`) REFERENCES `document_compliance_mappings` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_compliance_mappings`
--

DROP TABLE IF EXISTS `document_compliance_mappings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_compliance_mappings` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `document_id` bigint NOT NULL,
  `compliance_classification_id` bigint NOT NULL,
  `storage_region` varchar(50) DEFAULT NULL,
  `classification_reason` text,
  `classified_by` varchar(100) DEFAULT NULL,
  `classification_date` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `compliance_status` varchar(50) DEFAULT NULL,
  `compliance_notes` text,
  `violation_count` int DEFAULT '0',
  `last_validation_date` timestamp NULL DEFAULT NULL,
  `next_review_date` timestamp NULL DEFAULT NULL,
  `access_log_retention_days` int DEFAULT NULL,
  `anonymization_method` varchar(200) DEFAULT NULL,
  `encryption_algorithm` varchar(100) DEFAULT NULL,
  `encryption_status` varchar(50) DEFAULT NULL,
  `is_anonymized` tinyint(1) DEFAULT '0',
  `is_pseudonymized` tinyint(1) DEFAULT '0',
  `requires_consent` tinyint(1) DEFAULT '0',
  `consent_obtained` tinyint(1) DEFAULT '0',
  `consent_date` timestamp NULL DEFAULT NULL,
  `consent_expiry_date` timestamp NULL DEFAULT NULL,
  `data_subject_rights_applicable` tinyint(1) DEFAULT '0',
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `consent_reference` varchar(200) DEFAULT NULL COMMENT 'Reference to consent record',
  `declassification_date` timestamp NULL DEFAULT NULL COMMENT 'Date when classification expires or is removed',
  `review_date` timestamp NULL DEFAULT NULL COMMENT 'Date when classification should be reviewed',
  `special_handling_instructions` text COMMENT 'Special instructions for handling this document',
  `last_compliance_check` timestamp NULL DEFAULT NULL COMMENT 'Date of last compliance check',
  `last_violation_date` timestamp NULL DEFAULT NULL COMMENT 'Date of last compliance violation',
  PRIMARY KEY (`id`),
  KEY `idx_document_compliance_document` (`document_id`),
  KEY `idx_document_compliance_classification` (`compliance_classification_id`),
  KEY `idx_document_compliance_active` (`is_active`),
  KEY `idx_document_compliance_status` (`compliance_status`),
  CONSTRAINT `document_compliance_mappings_ibfk_1` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE CASCADE,
  CONSTRAINT `document_compliance_mappings_ibfk_2` FOREIGN KEY (`compliance_classification_id`) REFERENCES `compliance_classifications` (`id`),
  CONSTRAINT `FKnvwhkpxvo5j50bl59v5fj82xn` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`),
  CONSTRAINT `FKtgpbt8kk68pn6gd9nglfervxc` FOREIGN KEY (`compliance_classification_id`) REFERENCES `compliance_classifications` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Maps documents to compliance classifications with specific requirements';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_compliance_metadata`
--

DROP TABLE IF EXISTS `document_compliance_metadata`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_compliance_metadata` (
  `created_date` datetime(6) NOT NULL,
  `document_id` bigint NOT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_modified_date` datetime(6) NOT NULL,
  `compliance_standard` varchar(100) DEFAULT NULL,
  `control_id` varchar(100) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `policy_id` varchar(100) DEFAULT NULL,
  `third_party_id` varchar(500) DEFAULT NULL COMMENT 'Third party identifier (PII encrypted if enabled)',
  `audit_relevance` varchar(200) DEFAULT NULL,
  `linked_risks_controls` text,
  PRIMARY KEY (`id`),
  KEY `FKjub51185kr1je2mqs0va1sns6` (`document_id`),
  CONSTRAINT `FKjub51185kr1je2mqs0va1sns6` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=86 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_data_subject_categories`
--

DROP TABLE IF EXISTS `document_data_subject_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_data_subject_categories` (
  `document_id` bigint NOT NULL,
  `data_subject_category` enum('ANONYMOUS','BENEFICIARY','CONTRACTOR','CUSTOMER','EMPLOYEE','MINOR','OTHER','PATIENT','PROSPECT','PSEUDONYMIZED','PUBLIC_FIGURE','SHAREHOLDER','STUDENT','VENDOR','VISITOR') DEFAULT NULL,
  KEY `FKgqtigddrsieg9pq729s5wuh9e` (`document_id`),
  CONSTRAINT `FKgqtigddrsieg9pq729s5wuh9e` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_data_subjects`
--

DROP TABLE IF EXISTS `document_data_subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_data_subjects` (
  `mapping_id` bigint NOT NULL,
  `data_subject_category` enum('ANONYMOUS','BENEFICIARY','CONTRACTOR','CUSTOMER','EMPLOYEE','MINOR','OTHER','PATIENT','PROSPECT','PSEUDONYMIZED','PUBLIC_FIGURE','SHAREHOLDER','STUDENT','VENDOR','VISITOR') DEFAULT NULL,
  KEY `FK9p4fq2dpve8vhckj8bafvijea` (`mapping_id`),
  CONSTRAINT `FK9p4fq2dpve8vhckj8bafvijea` FOREIGN KEY (`mapping_id`) REFERENCES `document_compliance_mappings` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_geographic_restrictions`
--

DROP TABLE IF EXISTS `document_geographic_restrictions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_geographic_restrictions` (
  `document_id` bigint NOT NULL,
  `region` enum('AU','BR','CA','CN','EU','GLOBAL','IN','JP','KR','MX','RESTRICTED','RU','SG','UK','US','ZA') DEFAULT NULL,
  KEY `FK2kvxyt4st3pf0r8jiqyo9pbhb` (`document_id`),
  CONSTRAINT `FK2kvxyt4st3pf0r8jiqyo9pbhb` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_ownership_metadata`
--

DROP TABLE IF EXISTS `document_ownership_metadata`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_ownership_metadata` (
  `archived` bit(1) DEFAULT NULL,
  `approval_date` datetime(6) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `document_id` bigint NOT NULL,
  `expiry_date` datetime(6) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_modified_date` datetime(6) NOT NULL,
  `renewal_reminder` datetime(6) DEFAULT NULL,
  `retention_period` varchar(50) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `approver` varchar(500) DEFAULT NULL COMMENT 'Document approver (PII encrypted if enabled)',
  `created_by` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `owner` varchar(500) DEFAULT NULL COMMENT 'Document owner (PII encrypted if enabled)',
  PRIMARY KEY (`id`),
  KEY `FKl3urw765uj3tnhu2lk4a0mhlw` (`document_id`),
  CONSTRAINT `FKl3urw765uj3tnhu2lk4a0mhlw` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=86 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_permissions`
--

DROP TABLE IF EXISTS `document_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_permissions` (
  `is_active` bit(1) NOT NULL,
  `created_date` datetime(6) NOT NULL,
  `document_id` bigint NOT NULL,
  `expires_at` datetime(6) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_modified_date` datetime(6) NOT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `granted_by` varchar(100) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `role_name` varchar(100) DEFAULT NULL,
  `user_id` varchar(100) DEFAULT NULL,
  `notes` text,
  `permission_type` enum('ADMIN','DELETE','READ','WRITE') NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK7n2j2k70ihw3dt54pajek4ejk` (`document_id`),
  CONSTRAINT `FK7n2j2k70ihw3dt54pajek4ejk` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_share_links`
--

DROP TABLE IF EXISTS `document_share_links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_share_links` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) NOT NULL,
  `created_by_user_id` varchar(100) NOT NULL,
  `expires_at` datetime(6) NOT NULL,
  `is_active` bit(1) NOT NULL,
  `link_id` varchar(36) NOT NULL,
  `max_uses` int DEFAULT NULL,
  `notes` varchar(1000) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `permission` enum('ADMIN','DELETE','READ','WRITE') NOT NULL,
  `target_role_name` varchar(100) DEFAULT NULL,
  `target_user_id` varchar(100) DEFAULT NULL,
  `use_count` int NOT NULL,
  `document_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK236w1d8q44iebkb24d0lws5nr` (`link_id`),
  KEY `idx_share_link_id` (`link_id`),
  KEY `idx_share_document_id` (`document_id`),
  KEY `idx_share_created_by` (`created_by_user_id`),
  KEY `idx_share_expires_at` (`expires_at`),
  KEY `idx_share_active` (`is_active`),
  KEY `idx_share_target_user` (`target_user_id`),
  KEY `idx_share_target_role` (`target_role_name`),
  CONSTRAINT `FK3lyc0puwsogb1bk7t4aqb2i93` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_templates`
--

DROP TABLE IF EXISTS `document_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_templates` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `access_level` enum('DEPARTMENT','PRIVATE','PUBLIC','RESTRICTED','SYSTEM') NOT NULL,
  `approval_status` enum('APPROVED','DRAFT','PENDING_APPROVAL','PUBLISHED','REJECTED') NOT NULL,
  `approved_by` varchar(255) DEFAULT NULL,
  `approved_date` datetime(6) DEFAULT NULL,
  `category` varchar(100) NOT NULL,
  `configuration_json` json DEFAULT NULL,
  `default_values` json DEFAULT NULL,
  `description` text,
  `field_definitions` json DEFAULT NULL,
  `file_size` bigint DEFAULT NULL,
  `is_active` bit(1) NOT NULL,
  `is_public` bit(1) NOT NULL,
  `is_system_template` bit(1) NOT NULL,
  `last_used_date` datetime(6) DEFAULT NULL,
  `mime_type` varchar(100) NOT NULL,
  `name` varchar(255) NOT NULL,
  `owner_department` varchar(255) DEFAULT NULL,
  `owner_user_id` varchar(255) NOT NULL,
  `published_date` datetime(6) DEFAULT NULL,
  `template_content` longblob,
  `template_format` varchar(50) NOT NULL,
  `template_type` enum('DOCUMENT','FORM','STRUCTURED') NOT NULL,
  `usage_count` int DEFAULT NULL,
  `validation_rules` json DEFAULT NULL,
  `version` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document_versions`
--

DROP TABLE IF EXISTS `document_versions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `document_versions` (
  `is_current` bit(1) NOT NULL,
  `version_number` int NOT NULL,
  `created_date` datetime(6) NOT NULL,
  `document_id` bigint NOT NULL,
  `file_size` bigint NOT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_modified_date` datetime(6) NOT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `checksum` varchar(255) DEFAULT NULL,
  `file_name` varchar(255) NOT NULL,
  `mime_type` varchar(255) NOT NULL,
  `storage_path` varchar(255) NOT NULL,
  `version_notes` varchar(255) DEFAULT NULL,
  `file_content` longblob,
  `status` enum('ACTIVE','DELETED','HISTORICAL') NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKi6p7dgv96b8s8ivf84hqo9pt` (`document_id`),
  CONSTRAINT `FKi6p7dgv96b8s8ivf84hqo9pt` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=93 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `documents`
--

DROP TABLE IF EXISTS `documents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `documents` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `original_file_name` varchar(255) NOT NULL,
  `file_size` bigint NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `storage_provider` enum('LOCAL','S3','SHAREPOINT') NOT NULL,
  `storage_path` varchar(500) NOT NULL,
  `status` enum('ACTIVE','DELETED','HISTORICAL') NOT NULL,
  `version` int NOT NULL DEFAULT '1',
  `tags` json DEFAULT NULL,
  `file_content` longblob,
  `creator_user_id` varchar(100) NOT NULL,
  `parent_document_id` bigint DEFAULT NULL,
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `creator_roles` json DEFAULT NULL COMMENT 'Roles of the user who created the document',
  `creator_permissions` json DEFAULT NULL COMMENT 'Permissions of the user who created the document',
  `last_modifier_roles` json DEFAULT NULL COMMENT 'Roles of the user who last modified the document',
  `last_modifier_permissions` json DEFAULT NULL COMMENT 'Permissions of the user who last modified the document',
  `retention_policy_id` bigint DEFAULT NULL,
  `retention_expiry_date` timestamp NULL DEFAULT NULL,
  `legal_hold_status` enum('ACTIVE','NONE','PENDING_REVIEW','RELEASED') DEFAULT NULL,
  `disposition_status` enum('ACTIVE','DISPOSED','ELIGIBLE','ON_HOLD','PENDING','REVIEW_REQUIRED','SUSPENDED') DEFAULT NULL,
  `legal_hold_reason` text,
  `legal_hold_applied_date` timestamp NULL DEFAULT NULL,
  `legal_hold_applied_by` varchar(100) DEFAULT NULL,
  `disposition_review_date` timestamp NULL DEFAULT NULL,
  `disposition_notes` text,
  `compliance_classification_level` varchar(50) DEFAULT NULL,
  `storage_region` varchar(50) DEFAULT NULL,
  `requires_consent` tinyint(1) NOT NULL DEFAULT '0',
  `consent_reference` varchar(1000) DEFAULT NULL COMMENT 'Consent reference (PII encrypted if enabled)',
  `is_anonymized` tinyint(1) NOT NULL DEFAULT '0',
  `is_pseudonymized` tinyint(1) NOT NULL DEFAULT '0',
  `encryption_status` varchar(50) DEFAULT NULL,
  `compliance_notes` text,
  `template_field_values` json DEFAULT NULL,
  `workflow_completion_date` datetime(6) DEFAULT NULL,
  `workflow_status` enum('CANCELLED','COMPLETED','FAILED','IN_PROGRESS','NONE','PENDING','SUSPENDED') DEFAULT NULL,
  `current_workflow_instance_id` bigint DEFAULT NULL,
  `source_template_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_documents_creator_user_id` (`creator_user_id`),
  KEY `idx_documents_status` (`status`),
  KEY `idx_documents_storage_provider` (`storage_provider`),
  KEY `idx_documents_created_date` (`created_date`),
  KEY `idx_documents_parent_document_id` (`parent_document_id`),
  KEY `idx_documents_name` (`name`),
  KEY `idx_documents_created_by` (`created_by`),
  KEY `idx_documents_last_modified_by` (`last_modified_by`),
  KEY `idx_documents_retention_policy` (`retention_policy_id`),
  KEY `idx_documents_retention_expiry` (`retention_expiry_date`),
  KEY `idx_documents_legal_hold_status` (`legal_hold_status`),
  KEY `idx_documents_disposition_status` (`disposition_status`),
  KEY `idx_documents_retention_eligible` (`retention_expiry_date`,`legal_hold_status`,`disposition_status`),
  KEY `idx_documents_compliance_classification` (`compliance_classification_level`),
  KEY `idx_documents_storage_region` (`storage_region`),
  KEY `idx_documents_requires_consent` (`requires_consent`),
  KEY `idx_documents_is_anonymized` (`is_anonymized`),
  KEY `idx_documents_is_pseudonymized` (`is_pseudonymized`),
  KEY `idx_documents_encryption_status` (`encryption_status`),
  KEY `FKav8txcdfdunanoelx5uh0j7gy` (`current_workflow_instance_id`),
  KEY `FK67x5medxt37pqe66e1yi9o2wm` (`source_template_id`),
  CONSTRAINT `documents_ibfk_1` FOREIGN KEY (`parent_document_id`) REFERENCES `documents` (`id`),
  CONSTRAINT `FK60dm4aap7dopqi9s25b69lhin` FOREIGN KEY (`parent_document_id`) REFERENCES `documents` (`id`),
  CONSTRAINT `FK67x5medxt37pqe66e1yi9o2wm` FOREIGN KEY (`source_template_id`) REFERENCES `document_templates` (`id`),
  CONSTRAINT `fk_documents_retention_policy` FOREIGN KEY (`retention_policy_id`) REFERENCES `retention_policies` (`id`) ON DELETE SET NULL,
  CONSTRAINT `FKav8txcdfdunanoelx5uh0j7gy` FOREIGN KEY (`current_workflow_instance_id`) REFERENCES `workflow_instances` (`id`),
  CONSTRAINT `FKi4pu7bxl05ti3nf4nqjbh6se7` FOREIGN KEY (`retention_policy_id`) REFERENCES `retention_policies` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Enhanced documents table with compliance, retention, and legal hold capabilities';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `event_processing_log`
--

DROP TABLE IF EXISTS `event_processing_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `event_processing_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `error_message` text,
  `error_stack_trace` text,
  `processing_duration_ms` int DEFAULT NULL,
  `processing_stage` varchar(50) NOT NULL,
  `processing_status` varchar(50) NOT NULL,
  `processing_timestamp` datetime(6) NOT NULL,
  `processor_name` varchar(100) DEFAULT NULL,
  `event_subscription_id` bigint DEFAULT NULL,
  `system_event_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK61dje5pk1u50c4aeellmgjp6a` (`event_subscription_id`),
  KEY `FKhma4larcafhawhh2flvk4sm9t` (`system_event_id`),
  CONSTRAINT `FK61dje5pk1u50c4aeellmgjp6a` FOREIGN KEY (`event_subscription_id`) REFERENCES `event_subscriptions` (`id`),
  CONSTRAINT `FKhma4larcafhawhh2flvk4sm9t` FOREIGN KEY (`system_event_id`) REFERENCES `system_events` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `event_subscriptions`
--

DROP TABLE IF EXISTS `event_subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `event_subscriptions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `batch_size` int DEFAULT NULL,
  `batch_timeout_seconds` int DEFAULT NULL,
  `delivery_mode` varchar(50) NOT NULL,
  `description` text,
  `event_filters` json DEFAULT NULL,
  `event_types` json NOT NULL,
  `events_processed` int DEFAULT NULL,
  `is_active` bit(1) NOT NULL,
  `is_paused` bit(1) NOT NULL,
  `last_processed_date` datetime(6) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `schedule_cron` varchar(100) DEFAULT NULL,
  `schedule_timezone` varchar(50) DEFAULT NULL,
  `subscriber_config` json NOT NULL,
  `subscriber_type` enum('EMAIL','INTERNAL','QUEUE','SMS','WEBHOOK') NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `event_templates`
--

DROP TABLE IF EXISTS `event_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `event_templates` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `available_variables` json DEFAULT NULL,
  `description` text,
  `event_type` enum('API_RATE_LIMIT_EXCEEDED','AUDIT_LOG_CREATED','COMPLIANCE_VIOLATION','DOCUMENT_CREATED','DOCUMENT_DELETED','DOCUMENT_DOWNLOADED','DOCUMENT_METADATA_UPDATED','DOCUMENT_SHARED','DOCUMENT_UPDATED','DOCUMENT_VERSION_CREATED','DOCUMENT_VIEWED','EXTERNAL_SYSTEM_SYNC','SECURITY_VIOLATION','SYSTEM_ERROR','SYSTEM_SHUTDOWN','SYSTEM_STARTUP','TEMPLATE_CREATED','TEMPLATE_DELETED','TEMPLATE_PUBLISHED','TEMPLATE_UPDATED','TEMPLATE_USED','USER_LOGIN','USER_LOGOUT','USER_PERMISSION_CHANGED','WEBHOOK_DELIVERY_FAILED','WORKFLOW_CANCELLED','WORKFLOW_COMPLETED','WORKFLOW_DELEGATED','WORKFLOW_ESCALATED','WORKFLOW_FAILED','WORKFLOW_STARTED','WORKFLOW_TASK_ASSIGNED','WORKFLOW_TASK_COMPLETED') NOT NULL,
  `is_active` bit(1) NOT NULL,
  `is_system_template` bit(1) NOT NULL,
  `last_used_date` datetime(6) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `required_variables` json DEFAULT NULL,
  `template_content` text NOT NULL,
  `template_format` varchar(50) NOT NULL,
  `usage_count` int DEFAULT NULL,
  `version` varchar(50) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK3c2is268rhqes1pnvbnl82vbk` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `permission_inheritance_rules`
--

DROP TABLE IF EXISTS `permission_inheritance_rules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permission_inheritance_rules` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(100) NOT NULL,
  `inherit_read` tinyint(1) NOT NULL DEFAULT '1',
  `inherit_write` tinyint(1) NOT NULL DEFAULT '0',
  `inherit_delete` tinyint(1) NOT NULL DEFAULT '0',
  `inherit_admin` tinyint(1) NOT NULL DEFAULT '0',
  `allow_override` tinyint(1) NOT NULL DEFAULT '1',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `rule_name` (`rule_name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `permission_limits`
--

DROP TABLE IF EXISTS `permission_limits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permission_limits` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` varchar(100) DEFAULT NULL,
  `role_name` varchar(100) DEFAULT NULL,
  `permission_type` enum('READ','WRITE','DELETE','ADMIN') NOT NULL,
  `max_documents` int NOT NULL DEFAULT '100',
  `current_count` int NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_permission_limits_user_id` (`user_id`),
  KEY `idx_permission_limits_role_name` (`role_name`),
  KEY `idx_permission_limits_permission_type` (`permission_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pii_encryption_keys`
--

DROP TABLE IF EXISTS `pii_encryption_keys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pii_encryption_keys` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `key_id` varchar(100) NOT NULL,
  `key_data` text NOT NULL,
  `algorithm` varchar(50) NOT NULL DEFAULT 'AES-256-GCM',
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `rotation_date` timestamp NULL DEFAULT NULL,
  `usage_count` bigint DEFAULT '0',
  `last_used_date` timestamp NULL DEFAULT NULL,
  `created_by` varchar(100) DEFAULT 'SYSTEM',
  `notes` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_id` (`key_id`),
  KEY `idx_pii_encryption_keys_key_id` (`key_id`),
  KEY `idx_pii_encryption_keys_is_active` (`is_active`),
  KEY `idx_pii_encryption_keys_created_date` (`created_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='PII encryption key management table for field-level encryption';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pii_encryption_metrics`
--

DROP TABLE IF EXISTS `pii_encryption_metrics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pii_encryption_metrics` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `metric_date` date NOT NULL,
  `total_pii_fields` bigint DEFAULT '0',
  `encrypted_pii_fields` bigint DEFAULT '0',
  `encryption_success_rate` decimal(5,2) DEFAULT '0.00',
  `decryption_success_rate` decimal(5,2) DEFAULT '0.00',
  `key_rotations` bigint DEFAULT '0',
  `encryption_errors` bigint DEFAULT '0',
  `decryption_errors` bigint DEFAULT '0',
  `auto_detected_pii` bigint DEFAULT '0',
  `performance_avg_encrypt_ms` decimal(10,3) DEFAULT '0.000',
  `performance_avg_decrypt_ms` decimal(10,3) DEFAULT '0.000',
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_pii_encryption_metrics_date` (`metric_date`),
  KEY `idx_pii_encryption_metrics_created_date` (`created_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='PII encryption metrics and monitoring data';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Temporary view structure for view `pii_encryption_status`
--

DROP TABLE IF EXISTS `pii_encryption_status`;
/*!50001 DROP VIEW IF EXISTS `pii_encryption_status`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `pii_encryption_status` AS SELECT 
 1 AS `encryption_date`,
 1 AS `table_name`,
 1 AS `field_name`,
 1 AS `total_records`,
 1 AS `encrypted_records`,
 1 AS `encryption_percentage`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `regulation_mappings`
--

DROP TABLE IF EXISTS `regulation_mappings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `regulation_mappings` (
  `is_active` bit(1) NOT NULL,
  `is_mandatory` bit(1) NOT NULL,
  `priority` int NOT NULL,
  `review_frequency_months` int DEFAULT NULL,
  `compliance_framework_id` bigint NOT NULL,
  `created_date` datetime(6) NOT NULL,
  `document_id` bigint NOT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_modified_date` datetime(6) NOT NULL,
  `last_review_date` datetime(6) DEFAULT NULL,
  `next_review_date` datetime(6) DEFAULT NULL,
  `remediation_deadline` datetime(6) DEFAULT NULL,
  `risk_level` varchar(20) DEFAULT NULL,
  `compliance_status` varchar(50) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `regulation_reference` varchar(200) DEFAULT NULL,
  `responsible_party` varchar(200) DEFAULT NULL,
  `compliance_notes` text,
  `compliance_requirement` text,
  `control_objective` text,
  `evidence_requirements` text,
  `remediation_plan` text,
  `testing_procedures` text,
  PRIMARY KEY (`id`),
  KEY `FKmef0s3pyx3h2l0jxnasjmgbg5` (`compliance_framework_id`),
  KEY `FK6cp5mvejilmvlsdjyf70ou0np` (`document_id`),
  CONSTRAINT `FK6cp5mvejilmvlsdjyf70ou0np` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`),
  CONSTRAINT `FKmef0s3pyx3h2l0jxnasjmgbg5` FOREIGN KEY (`compliance_framework_id`) REFERENCES `compliance_frameworks` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `retention_policies`
--

DROP TABLE IF EXISTS `retention_policies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `retention_policies` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `scope` varchar(100) DEFAULT NULL,
  `retention_period` int NOT NULL,
  `retention_period_unit` enum('DAYS','MONTHS','YEARS') NOT NULL,
  `disposition_action` enum('ARCHIVE','DELETE','EXTEND','REVIEW','TRANSFER') NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `allow_legal_hold` tinyint(1) NOT NULL DEFAULT '1',
  `auto_apply` tinyint(1) NOT NULL DEFAULT '0',
  `priority` int NOT NULL DEFAULT '0',
  `trigger_event` varchar(50) DEFAULT NULL,
  `business_justification` text,
  `legal_basis` text,
  `review_frequency_months` int DEFAULT NULL,
  `notification_before_days` int DEFAULT NULL,
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `UKqvpsrmy11iavu30vp3a3xjae5` (`name`),
  KEY `idx_retention_policies_name` (`name`),
  KEY `idx_retention_policies_scope` (`scope`),
  KEY `idx_retention_policies_active` (`is_active`),
  KEY `idx_retention_policies_priority` (`priority`),
  KEY `idx_retention_policies_auto_apply` (`auto_apply`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `retention_policy_assignments`
--

DROP TABLE IF EXISTS `retention_policy_assignments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `retention_policy_assignments` (
  `is_active` bit(1) NOT NULL,
  `priority` int NOT NULL,
  `created_date` datetime(6) NOT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_modified_date` datetime(6) NOT NULL,
  `retention_policy_id` bigint NOT NULL,
  `assignment_type` varchar(50) NOT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `assignment_value` varchar(255) NOT NULL,
  `conditions` json DEFAULT NULL,
  `description` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UKi6608db05v0x7bkp07k4qeqmj` (`retention_policy_id`,`assignment_type`,`assignment_value`),
  CONSTRAINT `FK2qt7jwqdmydnlrj978tg6x5sf` FOREIGN KEY (`retention_policy_id`) REFERENCES `retention_policies` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `security_config`
--

DROP TABLE IF EXISTS `security_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `security_config` (
  `is_active` bit(1) NOT NULL,
  `created_date` datetime(6) NOT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_modified_date` datetime(6) NOT NULL,
  `config_key` varchar(100) NOT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `config_value` text NOT NULL,
  `description` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK87bhi76tmua74io8e7fi2ob0` (`config_key`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `security_violations`
--

DROP TABLE IF EXISTS `security_violations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `security_violations` (
  `is_resolved` bit(1) NOT NULL,
  `created_date` datetime(6) NOT NULL,
  `document_id` bigint DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_modified_date` datetime(6) NOT NULL,
  `resolved_date` datetime(6) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `attempted_action` varchar(100) DEFAULT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `resolved_by` varchar(100) DEFAULT NULL,
  `user_id` varchar(100) NOT NULL,
  `user_agent` text,
  `violation_details` text,
  `severity` enum('CRITICAL','HIGH','LOW','MEDIUM') NOT NULL,
  `violation_type` enum('INVALID_ACCESS','MIGRATION_FILE_ACCESS_DENIED','MIGRATION_INVALID_PARAMETERS','MIGRATION_PATH_TRAVERSAL','MIGRATION_RATE_LIMIT_EXCEEDED','MIGRATION_UNAUTHORIZED_ACCESS','PERMISSION_DENIED','PRIVILEGE_ESCALATION','RATE_LIMIT_EXCEEDED','TOKEN_EXPIRED') NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK7hub6t4j9k67c3lrjfmd5nqa0` (`document_id`),
  CONSTRAINT `FK7hub6t4j9k67c3lrjfmd5nqa0` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `storage_configurations`
--

DROP TABLE IF EXISTS `storage_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `storage_configurations` (
  `health_check_enabled` bit(1) NOT NULL,
  `is_active` bit(1) NOT NULL,
  `is_default` bit(1) NOT NULL,
  `priority` int NOT NULL,
  `created_date` datetime(6) NOT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `last_health_check` datetime(6) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `configuration_json` json NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `health_status` varchar(255) DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `provider_type` enum('LOCAL','S3','SHAREPOINT') NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UKcf096acx0j5k8r1017o38fw5n` (`provider_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `system_events`
--

DROP TABLE IF EXISTS `system_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_events` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `actor_type` varchar(50) DEFAULT NULL,
  `actor_user_id` varchar(255) DEFAULT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `event_category` enum('COMPLIANCE','DOCUMENT','INTEGRATION','SECURITY','SYSTEM','TEMPLATE','USER','WORKFLOW') NOT NULL,
  `event_data` json DEFAULT NULL,
  `event_metadata` json DEFAULT NULL,
  `event_name` varchar(255) NOT NULL,
  `event_timestamp` datetime(6) NOT NULL,
  `event_type` enum('API_RATE_LIMIT_EXCEEDED','AUDIT_LOG_CREATED','COMPLIANCE_VIOLATION','DOCUMENT_CREATED','DOCUMENT_DELETED','DOCUMENT_DOWNLOADED','DOCUMENT_METADATA_UPDATED','DOCUMENT_SHARED','DOCUMENT_UPDATED','DOCUMENT_VERSION_CREATED','DOCUMENT_VIEWED','EXTERNAL_SYSTEM_SYNC','SECURITY_VIOLATION','SYSTEM_ERROR','SYSTEM_SHUTDOWN','SYSTEM_STARTUP','TEMPLATE_CREATED','TEMPLATE_DELETED','TEMPLATE_PUBLISHED','TEMPLATE_UPDATED','TEMPLATE_USED','USER_LOGIN','USER_LOGOUT','USER_PERMISSION_CHANGED','WEBHOOK_DELIVERY_FAILED','WORKFLOW_CANCELLED','WORKFLOW_COMPLETED','WORKFLOW_DELEGATED','WORKFLOW_ESCALATED','WORKFLOW_FAILED','WORKFLOW_STARTED','WORKFLOW_TASK_ASSIGNED','WORKFLOW_TASK_COMPLETED') NOT NULL,
  `processing_status` varchar(50) NOT NULL,
  `request_id` varchar(100) DEFAULT NULL,
  `session_id` varchar(100) DEFAULT NULL,
  `source_entity_id` bigint DEFAULT NULL,
  `source_entity_type` varchar(50) DEFAULT NULL,
  `webhook_delivery_count` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `template_categories`
--

DROP TABLE IF EXISTS `template_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `template_categories` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `access_level` enum('DEPARTMENT','PRIVATE','PUBLIC','RESTRICTED','SYSTEM') NOT NULL,
  `color_code` varchar(7) DEFAULT NULL,
  `department_restrictions` json DEFAULT NULL,
  `description` text,
  `icon` varchar(100) DEFAULT NULL,
  `is_active` bit(1) NOT NULL,
  `name` varchar(255) NOT NULL,
  `sort_order` int DEFAULT NULL,
  `parent_category_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UKeh1yk73ntihgmpy4fa27eifl4` (`name`),
  KEY `FK56c7uhodex0onq6gvoh42slc8` (`parent_category_id`),
  CONSTRAINT `FK56c7uhodex0onq6gvoh42slc8` FOREIGN KEY (`parent_category_id`) REFERENCES `template_categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `template_fields`
--

DROP TABLE IF EXISTS `template_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `template_fields` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `allowed_values` json DEFAULT NULL,
  `calculation_formula` text,
  `conditional_logic` json DEFAULT NULL,
  `default_value` text,
  `field_label` varchar(255) NOT NULL,
  `field_name` varchar(255) NOT NULL,
  `field_order` int NOT NULL,
  `field_type` enum('BOOLEAN','CALCULATED','CURRENCY','DATE','DATETIME','EMAIL','FILE','MULTISELECT','NUMBER','PERCENTAGE','PHONE','SELECT','SIGNATURE','TEXT','TEXTAREA','URL') NOT NULL,
  `help_text` text,
  `is_hidden` bit(1) NOT NULL,
  `is_readonly` bit(1) NOT NULL,
  `is_required` bit(1) NOT NULL,
  `max_length` int DEFAULT NULL,
  `max_value` decimal(19,4) DEFAULT NULL,
  `min_length` int DEFAULT NULL,
  `min_value` decimal(19,4) DEFAULT NULL,
  `placeholder_text` varchar(255) DEFAULT NULL,
  `validation_pattern` varchar(500) DEFAULT NULL,
  `template_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKs64wjugjewotjbj4glsin1yq2` (`template_id`),
  CONSTRAINT `FKs64wjugjewotjbj4glsin1yq2` FOREIGN KEY (`template_id`) REFERENCES `document_templates` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `template_permissions`
--

DROP TABLE IF EXISTS `template_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `template_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `assignee_type` varchar(50) NOT NULL,
  `assignee_value` varchar(255) NOT NULL,
  `expires_date` datetime(6) DEFAULT NULL,
  `granted_by` varchar(255) NOT NULL,
  `granted_date` datetime(6) NOT NULL,
  `is_active` bit(1) NOT NULL,
  `permission_type` varchar(50) NOT NULL,
  `template_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKbpv7vpxwloqhtty77b74dpvyc` (`template_id`),
  CONSTRAINT `FKbpv7vpxwloqhtty77b74dpvyc` FOREIGN KEY (`template_id`) REFERENCES `document_templates` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `template_usage_history`
--

DROP TABLE IF EXISTS `template_usage_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `template_usage_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `error_message` text,
  `field_values` json DEFAULT NULL,
  `generation_time_ms` int DEFAULT NULL,
  `session_id` varchar(100) DEFAULT NULL,
  `success` bit(1) NOT NULL,
  `usage_date` datetime(6) NOT NULL,
  `usage_type` varchar(50) NOT NULL,
  `used_by_department` varchar(255) DEFAULT NULL,
  `used_by_user_id` varchar(255) NOT NULL,
  `document_id` bigint DEFAULT NULL,
  `template_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK12vmury7sue2h9u8jenmyd3yn` (`document_id`),
  KEY `FKibcq36j1qbtif5725owwd4tg5` (`template_id`),
  CONSTRAINT `FK12vmury7sue2h9u8jenmyd3yn` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`),
  CONSTRAINT `FKibcq36j1qbtif5725owwd4tg5` FOREIGN KEY (`template_id`) REFERENCES `document_templates` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `template_versions`
--

DROP TABLE IF EXISTS `template_versions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `template_versions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `change_reason` varchar(255) DEFAULT NULL,
  `change_summary` text,
  `configuration_json` json DEFAULT NULL,
  `default_values` json DEFAULT NULL,
  `field_definitions` json DEFAULT NULL,
  `file_size` bigint DEFAULT NULL,
  `is_current` bit(1) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `template_content` longblob,
  `template_format` varchar(50) NOT NULL,
  `validation_rules` json DEFAULT NULL,
  `version_name` varchar(255) DEFAULT NULL,
  `version_number` int NOT NULL,
  `template_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKqsgt06wxbii1knr8t4y7a2r9e` (`template_id`),
  CONSTRAINT `FKqsgt06wxbii1knr8t4y7a2r9e` FOREIGN KEY (`template_id`) REFERENCES `document_templates` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `webhook_deliveries`
--

DROP TABLE IF EXISTS `webhook_deliveries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `webhook_deliveries` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `attempted_date` datetime(6) DEFAULT NULL,
  `completed_date` datetime(6) DEFAULT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `delivery_attempt` int NOT NULL,
  `delivery_status` enum('CANCELLED','FAILED','PENDING','RETRYING','SUCCESS') NOT NULL,
  `duration_ms` int DEFAULT NULL,
  `error_code` varchar(50) DEFAULT NULL,
  `error_message` text,
  `http_status_code` int DEFAULT NULL,
  `request_headers` json DEFAULT NULL,
  `request_payload` text,
  `response_body` text,
  `response_headers` json DEFAULT NULL,
  `retry_after_seconds` int DEFAULT NULL,
  `scheduled_date` datetime(6) NOT NULL,
  `system_event_id` bigint NOT NULL,
  `webhook_endpoint_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKjoh6su26r4n8ytnt2wv3d46b7` (`system_event_id`),
  KEY `FK1pgpkw2ue309l4t0vf9hlr3tf` (`webhook_endpoint_id`),
  CONSTRAINT `FK1pgpkw2ue309l4t0vf9hlr3tf` FOREIGN KEY (`webhook_endpoint_id`) REFERENCES `webhook_endpoints` (`id`),
  CONSTRAINT `FKjoh6su26r4n8ytnt2wv3d46b7` FOREIGN KEY (`system_event_id`) REFERENCES `system_events` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `webhook_endpoints`
--

DROP TABLE IF EXISTS `webhook_endpoints`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `webhook_endpoints` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `auth_config` json DEFAULT NULL,
  `auth_type` enum('API_KEY','BASIC','BEARER','CUSTOM','NONE') DEFAULT NULL,
  `content_type` varchar(100) NOT NULL,
  `custom_headers` json DEFAULT NULL,
  `description` text,
  `event_filters` json DEFAULT NULL,
  `event_types` json DEFAULT NULL,
  `exponential_backoff` bit(1) NOT NULL,
  `failure_count` int DEFAULT NULL,
  `http_method` varchar(10) NOT NULL,
  `is_active` bit(1) NOT NULL,
  `is_verified` bit(1) NOT NULL,
  `last_failure_date` datetime(6) DEFAULT NULL,
  `last_failure_reason` text,
  `last_success_date` datetime(6) DEFAULT NULL,
  `max_retries` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `payload_template` text,
  `rate_limit_per_hour` int DEFAULT NULL,
  `rate_limit_per_minute` int DEFAULT NULL,
  `retry_delay_seconds` int NOT NULL,
  `secret_key` varchar(255) DEFAULT NULL,
  `success_count` int DEFAULT NULL,
  `timeout_seconds` int NOT NULL,
  `url` varchar(2000) NOT NULL,
  `verification_token` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `workflow_definitions`
--

DROP TABLE IF EXISTS `workflow_definitions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_definitions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `approval_type` enum('CONDITIONAL','PARALLEL','SEQUENTIAL') NOT NULL,
  `auto_start` bit(1) NOT NULL,
  `classification_requirements` json DEFAULT NULL,
  `configuration_json` json DEFAULT NULL,
  `department_restrictions` json DEFAULT NULL,
  `description` text,
  `document_types` json DEFAULT NULL,
  `escalation_enabled` bit(1) NOT NULL,
  `escalation_hours` int DEFAULT NULL,
  `is_active` bit(1) NOT NULL,
  `is_default` bit(1) NOT NULL,
  `name` varchar(255) NOT NULL,
  `timeout_hours` int DEFAULT NULL,
  `trigger_conditions` json DEFAULT NULL,
  `version` varchar(50) NOT NULL,
  `workflow_type` enum('CHANGE_MANAGEMENT','COMPLIANCE_REVIEW','CUSTOM','DOCUMENT_APPROVAL','DOCUMENT_REVIEW','LEGAL_REVIEW','PUBLICATION_APPROVAL') NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `workflow_history`
--

DROP TABLE IF EXISTS `workflow_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `action_taken` varchar(50) DEFAULT NULL,
  `actor_role` varchar(255) DEFAULT NULL,
  `actor_user_id` varchar(255) DEFAULT NULL,
  `comments` text,
  `correlation_id` varchar(100) DEFAULT NULL,
  `event_description` text,
  `event_timestamp` datetime(6) NOT NULL,
  `event_type` varchar(50) NOT NULL,
  `new_status` varchar(50) DEFAULT NULL,
  `old_status` varchar(50) DEFAULT NULL,
  `on_behalf_of_user_id` varchar(255) DEFAULT NULL,
  `stage_name` varchar(255) DEFAULT NULL,
  `task_name` varchar(255) DEFAULT NULL,
  `workflow_instance_id` bigint NOT NULL,
  `workflow_task_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKsnmaf56d8cnw0uhu57fj82ltj` (`workflow_instance_id`),
  KEY `FK3o38idsvffydaro5ikfyvsk1y` (`workflow_task_id`),
  CONSTRAINT `FK3o38idsvffydaro5ikfyvsk1y` FOREIGN KEY (`workflow_task_id`) REFERENCES `workflow_tasks` (`id`),
  CONSTRAINT `FKsnmaf56d8cnw0uhu57fj82ltj` FOREIGN KEY (`workflow_instance_id`) REFERENCES `workflow_instances` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `workflow_instances`
--

DROP TABLE IF EXISTS `workflow_instances`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_instances` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `completed_date` datetime(6) DEFAULT NULL,
  `completion_reason` varchar(255) DEFAULT NULL,
  `context_data` json DEFAULT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `due_date` datetime(6) DEFAULT NULL,
  `initiator_user_id` varchar(255) NOT NULL,
  `instance_name` varchar(255) DEFAULT NULL,
  `priority` varchar(20) NOT NULL,
  `started_date` datetime(6) NOT NULL,
  `status` enum('CANCELLED','COMPLETED','FAILED','IN_PROGRESS','NONE','PENDING','SUSPENDED') NOT NULL,
  `current_stage_id` bigint DEFAULT NULL,
  `document_id` bigint NOT NULL,
  `parent_workflow_instance_id` bigint DEFAULT NULL,
  `workflow_definition_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKb3gqcfsykq9aylr9qeuls0bsj` (`current_stage_id`),
  KEY `FK3wshukft2ja76o51ireb6fb0a` (`document_id`),
  KEY `FK683r9gbach3g3v8oen37lmij7` (`parent_workflow_instance_id`),
  KEY `FKgxis3gb74jdrjo82d89nu2i4e` (`workflow_definition_id`),
  CONSTRAINT `FK3wshukft2ja76o51ireb6fb0a` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`),
  CONSTRAINT `FK683r9gbach3g3v8oen37lmij7` FOREIGN KEY (`parent_workflow_instance_id`) REFERENCES `workflow_instances` (`id`),
  CONSTRAINT `FKb3gqcfsykq9aylr9qeuls0bsj` FOREIGN KEY (`current_stage_id`) REFERENCES `workflow_stages` (`id`),
  CONSTRAINT `FKgxis3gb74jdrjo82d89nu2i4e` FOREIGN KEY (`workflow_definition_id`) REFERENCES `workflow_definitions` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `workflow_notifications`
--

DROP TABLE IF EXISTS `workflow_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_notifications` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `delivered_date` datetime(6) DEFAULT NULL,
  `failure_count` int NOT NULL,
  `failure_reason` varchar(255) DEFAULT NULL,
  `is_read` bit(1) NOT NULL,
  `last_failure_date` datetime(6) DEFAULT NULL,
  `max_retries` int DEFAULT NULL,
  `message` text,
  `notification_data` json DEFAULT NULL,
  `notification_type` varchar(50) NOT NULL,
  `priority` varchar(20) DEFAULT NULL,
  `read_date` datetime(6) DEFAULT NULL,
  `recipient_email` varchar(255) DEFAULT NULL,
  `recipient_user_id` varchar(255) NOT NULL,
  `retry_count` int DEFAULT NULL,
  `scheduled_date` datetime(6) DEFAULT NULL,
  `sent_date` datetime(6) DEFAULT NULL,
  `status` varchar(50) NOT NULL,
  `subject` varchar(500) NOT NULL,
  `workflow_instance_id` bigint NOT NULL,
  `workflow_task_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKd5v57stttfaudatoddcl06qs5` (`workflow_instance_id`),
  KEY `FKh3587evcmwtyx4mlru24eguxr` (`workflow_task_id`),
  CONSTRAINT `FKd5v57stttfaudatoddcl06qs5` FOREIGN KEY (`workflow_instance_id`) REFERENCES `workflow_instances` (`id`),
  CONSTRAINT `FKh3587evcmwtyx4mlru24eguxr` FOREIGN KEY (`workflow_task_id`) REFERENCES `workflow_tasks` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `workflow_stages`
--

DROP TABLE IF EXISTS `workflow_stages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_stages` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `approval_percentage_required` decimal(5,2) DEFAULT NULL,
  `assignee_type` varchar(50) NOT NULL,
  `assignee_values` json DEFAULT NULL,
  `auto_approve_conditions` json DEFAULT NULL,
  `entry_conditions` json DEFAULT NULL,
  `escalation_assignees` json DEFAULT NULL,
  `escalation_enabled` bit(1) NOT NULL,
  `escalation_hours` int DEFAULT NULL,
  `exit_conditions` json DEFAULT NULL,
  `fallback_assignees` json DEFAULT NULL,
  `is_parallel` bit(1) NOT NULL,
  `is_required` bit(1) NOT NULL,
  `min_approvals_required` int DEFAULT NULL,
  `stage_name` varchar(255) NOT NULL,
  `stage_order` int NOT NULL,
  `stage_type` varchar(50) NOT NULL,
  `timeout_hours` int DEFAULT NULL,
  `workflow_definition_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKqtbfpi7wvnn07mgg6x52toapg` (`workflow_definition_id`),
  CONSTRAINT `FKqtbfpi7wvnn07mgg6x52toapg` FOREIGN KEY (`workflow_definition_id`) REFERENCES `workflow_definitions` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `workflow_tasks`
--

DROP TABLE IF EXISTS `workflow_tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_tasks` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `action_taken` enum('APPROVE','CANCEL','DELEGATE','ESCALATE','REJECT','REQUEST_CHANGES','RESUME','SUSPEND') DEFAULT NULL,
  `assigned_date` datetime(6) DEFAULT NULL,
  `assigned_to_department` varchar(255) DEFAULT NULL,
  `assigned_to_role` varchar(255) DEFAULT NULL,
  `assigned_to_user_id` varchar(255) DEFAULT NULL,
  `attachments` json DEFAULT NULL,
  `comments` text,
  `completed_by_user_id` varchar(255) DEFAULT NULL,
  `completed_date` datetime(6) DEFAULT NULL,
  `correlation_id` varchar(100) DEFAULT NULL,
  `delegated_date` datetime(6) DEFAULT NULL,
  `delegated_to_user_id` varchar(255) DEFAULT NULL,
  `due_date` datetime(6) DEFAULT NULL,
  `escalated_date` datetime(6) DEFAULT NULL,
  `escalated_to_user_id` varchar(255) DEFAULT NULL,
  `escalation_reason` varchar(255) DEFAULT NULL,
  `priority` varchar(20) NOT NULL,
  `status` enum('CANCELLED','COMPLETED','DELEGATED','ESCALATED','IN_PROGRESS','PENDING','SKIPPED') NOT NULL,
  `task_name` varchar(255) NOT NULL,
  `workflow_instance_id` bigint NOT NULL,
  `workflow_stage_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK9x1738pq4mxeg9y96hrm83g0u` (`workflow_instance_id`),
  KEY `FKb6a9pk5r9dfflghqpn0lh1hh9` (`workflow_stage_id`),
  CONSTRAINT `FK9x1738pq4mxeg9y96hrm83g0u` FOREIGN KEY (`workflow_instance_id`) REFERENCES `workflow_instances` (`id`),
  CONSTRAINT `FKb6a9pk5r9dfflghqpn0lh1hh9` FOREIGN KEY (`workflow_stage_id`) REFERENCES `workflow_stages` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `workflow_templates`
--

DROP TABLE IF EXISTS `workflow_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_templates` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `category` varchar(100) NOT NULL,
  `description` text,
  `is_active` bit(1) NOT NULL,
  `is_system_template` bit(1) NOT NULL,
  `last_used_date` datetime(6) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `template_json` json NOT NULL,
  `usage_count` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK3x5b8hw4a1lr19udcmts607dn` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `workflow_transitions`
--

DROP TABLE IF EXISTS `workflow_transitions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_transitions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime(6) NOT NULL,
  `last_modified_by` varchar(100) DEFAULT NULL,
  `last_modified_date` datetime(6) NOT NULL,
  `conditions` json DEFAULT NULL,
  `is_default` bit(1) NOT NULL,
  `requires_comment` bit(1) NOT NULL,
  `transition_name` varchar(255) NOT NULL,
  `transition_type` varchar(50) NOT NULL,
  `from_stage_id` bigint DEFAULT NULL,
  `to_stage_id` bigint DEFAULT NULL,
  `workflow_definition_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FKbg71yny0d8ncwdyuv6ajklnii` (`from_stage_id`),
  KEY `FKq2gomba2qajobg2iueg77qfew` (`to_stage_id`),
  KEY `FKen18ujmln0gown8tivmcbrvup` (`workflow_definition_id`),
  CONSTRAINT `FKbg71yny0d8ncwdyuv6ajklnii` FOREIGN KEY (`from_stage_id`) REFERENCES `workflow_stages` (`id`),
  CONSTRAINT `FKen18ujmln0gown8tivmcbrvup` FOREIGN KEY (`workflow_definition_id`) REFERENCES `workflow_definitions` (`id`),
  CONSTRAINT `FKq2gomba2qajobg2iueg77qfew` FOREIGN KEY (`to_stage_id`) REFERENCES `workflow_stages` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping routines for database 'dms_db'
--

--
-- Final view structure for view `api_deprecation_summary`
--

/*!50001 DROP VIEW IF EXISTS `api_deprecation_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `api_deprecation_summary` AS select `adw`.`version` AS `version`,`adw`.`element_type` AS `element_type`,`adw`.`element_name` AS `element_name`,`adw`.`deprecated_since` AS `deprecated_since`,`adw`.`removed_in` AS `removed_in`,`adw`.`replacement_element` AS `replacement_element`,count(distinct `adw`.`user_id`) AS `affected_users`,sum(`adw`.`warning_count`) AS `total_warnings`,max(`adw`.`last_warning_timestamp`) AS `latest_warning`,`av`.`end_of_life_date` AS `end_of_life_date` from (`api_deprecation_warnings` `adw` left join `api_versions` `av` on((`adw`.`version` = `av`.`version`))) where (`adw`.`warning_timestamp` >= (now() - interval 30 day)) group by `adw`.`version`,`adw`.`element_type`,`adw`.`element_name` order by `total_warnings` desc,`latest_warning` desc */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `api_version_usage_stats`
--

/*!50001 DROP VIEW IF EXISTS `api_version_usage_stats`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `api_version_usage_stats` AS select `api_version_usage`.`version` AS `version`,cast(`api_version_usage`.`request_timestamp` as date) AS `usage_date`,count(0) AS `total_requests`,count(distinct `api_version_usage`.`user_id`) AS `unique_users`,avg(`api_version_usage`.`response_time_ms`) AS `avg_response_time`,sum((case when (`api_version_usage`.`success` = true) then 1 else 0 end)) AS `successful_requests`,sum((case when (`api_version_usage`.`success` = false) then 1 else 0 end)) AS `failed_requests`,sum((case when (`api_version_usage`.`deprecation_warning` = true) then 1 else 0 end)) AS `deprecation_warnings`,count(distinct `api_version_usage`.`operation_name`) AS `unique_operations` from `api_version_usage` where (`api_version_usage`.`request_timestamp` >= (now() - interval 30 day)) group by `api_version_usage`.`version`,cast(`api_version_usage`.`request_timestamp` as date) order by `usage_date` desc,`api_version_usage`.`version` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `audit_encryption_status`
--

/*!50001 DROP VIEW IF EXISTS `audit_encryption_status`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `audit_encryption_status` AS select cast(`al`.`timestamp` as date) AS `audit_date`,count(0) AS `total_logs`,sum((case when (`al`.`is_encrypted` = true) then 1 else 0 end)) AS `encrypted_logs`,round(((sum((case when (`al`.`is_encrypted` = true) then 1 else 0 end)) * 100.0) / count(0)),2) AS `encryption_percentage`,count(distinct `al`.`encryption_key_id`) AS `unique_keys_used`,`al`.`event_type` AS `event_type`,`al`.`event_category` AS `event_category` from `audit_logs` `al` where (`al`.`timestamp` >= (now() - interval 30 day)) group by cast(`al`.`timestamp` as date),`al`.`event_type`,`al`.`event_category` order by `audit_date` desc,`al`.`event_type` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `pii_encryption_status`
--

/*!50001 DROP VIEW IF EXISTS `pii_encryption_status`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `pii_encryption_status` AS select cast(`document_ownership_metadata`.`created_date` as date) AS `encryption_date`,'document_ownership_metadata' AS `table_name`,'owner' AS `field_name`,count(0) AS `total_records`,sum((case when (`document_ownership_metadata`.`owner` like '{%encrypted%') then 1 else 0 end)) AS `encrypted_records`,round(((sum((case when (`document_ownership_metadata`.`owner` like '{%encrypted%') then 1 else 0 end)) * 100.0) / count(0)),2) AS `encryption_percentage` from `document_ownership_metadata` where (`document_ownership_metadata`.`created_date` >= (now() - interval 30 day)) group by cast(`document_ownership_metadata`.`created_date` as date) union all select cast(`document_ownership_metadata`.`created_date` as date) AS `encryption_date`,'document_ownership_metadata' AS `table_name`,'approver' AS `field_name`,count(0) AS `total_records`,sum((case when (`document_ownership_metadata`.`approver` like '{%encrypted%') then 1 else 0 end)) AS `encrypted_records`,round(((sum((case when (`document_ownership_metadata`.`approver` like '{%encrypted%') then 1 else 0 end)) * 100.0) / count(0)),2) AS `encryption_percentage` from `document_ownership_metadata` where (`document_ownership_metadata`.`created_date` >= (now() - interval 30 day)) group by cast(`document_ownership_metadata`.`created_date` as date) union all select cast(`documents`.`created_date` as date) AS `encryption_date`,'documents' AS `table_name`,'consent_reference' AS `field_name`,count(0) AS `total_records`,sum((case when (`documents`.`consent_reference` like '{%encrypted%') then 1 else 0 end)) AS `encrypted_records`,round(((sum((case when (`documents`.`consent_reference` like '{%encrypted%') then 1 else 0 end)) * 100.0) / count(0)),2) AS `encryption_percentage` from `documents` where ((`documents`.`created_date` >= (now() - interval 30 day)) and (`documents`.`consent_reference` is not null)) group by cast(`documents`.`created_date` as date) order by `encryption_date` desc,`table_name`,`field_name` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-27 15:22:10
