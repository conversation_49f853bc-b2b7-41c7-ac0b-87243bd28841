# GraphQL mutation for testing large file upload with UploadDocumentFromPathEx
# Update the variables section with your actual file path and JWT token

mutation UploadDocumentFromPathEx($input: UploadDocumentFromPathExInput!) {
  uploadDocumentFromPathEx(input: $input) {
    id
    name
    originalFileName
    fileSize
    status
    processingStrategy
    processingStatus
    processingJobId
    storageProvider
    storagePath
    createdDate
    creatorUserId
  }
}

# Variables (update these values):
{
  "input": {
    "sourceFilePath": "C:/path/to/your/61MB/file.pdf",
    "name": "Test Large File Upload - 61MB",
    "description": "Testing large file upload with chunked processing strategy",
    "storageProvider": "LOCAL",
    "overrideFile": true,
    "keywords": ["test", "large-file", "chunked-upload"]
  }
}

# Headers (add your JWT token):
{
  "Authorization": "Bearer your-jwt-token-here"
}