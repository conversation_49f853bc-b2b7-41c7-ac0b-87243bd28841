# Emergency Procedures Guide

This guide provides step-by-step procedures for handling critical incidents and emergency situations in the DMS system.

## Table of Contents

1. [Incident Classification](#incident-classification)
2. [Service Down Procedures](#service-down-procedures)
3. [Data Corruption Recovery](#data-corruption-recovery)
4. [Security Breach Response](#security-breach-response)
5. [Performance Degradation](#performance-degradation)
6. [Database Emergencies](#database-emergencies)
7. [Storage Failures](#storage-failures)
8. [Communication Procedures](#communication-procedures)

## Incident Classification

### Severity Levels

**Critical (P1) - Service Down**
- Complete service unavailability
- Data loss or corruption
- Security breach with data exposure
- **Response Time**: Immediate (< 15 minutes)
- **Escalation**: Immediate to on-call engineer

**High (P2) - Major Impact**
- Significant performance degradation (> 50% slower)
- Partial service unavailability
- Authentication/authorization failures
- **Response Time**: < 1 hour
- **Escalation**: Within 30 minutes

**Medium (P3) - Moderate Impact**
- Minor performance issues
- Non-critical feature failures
- Configuration issues
- **Response Time**: < 4 hours
- **Escalation**: Next business day

**Low (P4) - Minor Impact**
- Cosmetic issues
- Documentation problems
- Enhancement requests
- **Response Time**: < 24 hours
- **Escalation**: As needed

## Service Down Procedures

### Immediate Response (0-15 minutes)

**1. Verify Service Status**
```bash
# Check service health
curl -f http://localhost:9092/actuator/health || echo "Service DOWN"

# Check process status
ps aux | grep java | grep dms-service

# Check port availability
netstat -tulpn | grep :9092
```

**2. Quick Restart Attempt**
```bash
# Stop service
pkill -f dms-service

# Start service
cd /app/dms-service
java -jar dms-service.jar > logs/startup.log 2>&1 &

# Monitor startup
tail -f logs/startup.log
```

**3. Check Dependencies**
```bash
# Database connectivity
mysql -u dms_user -p dms_db -e "SELECT 1;"

# Redis connectivity
redis-cli ping

# Elasticsearch connectivity
curl -f http://localhost:9200/_health
```

### Detailed Investigation (15-60 minutes)

**1. Analyze Logs**
```bash
# Check for errors in last 30 minutes
grep -A 5 -B 5 "ERROR\|FATAL" logs/dms-service.log | tail -100

# Check for OutOfMemory errors
grep "OutOfMemoryError" logs/dms-service.log

# Check for database connection issues
grep "Connection" logs/dms-service.log | tail -20
```

**2. System Resource Check**
```bash
# Check disk space
df -h

# Check memory usage
free -h
ps aux --sort=-%mem | head -10

# Check CPU usage
top -n 1 -b | head -20
```

**3. Network Connectivity**
```bash
# Check database connection
telnet database-host 3306

# Check Redis connection
telnet redis-host 6379

# Check external services
curl -I https://s3.amazonaws.com
```

### Recovery Actions

**1. Service Recovery**
```bash
# Clean restart with fresh logs
pkill -f dms-service
rm -f logs/dms-service.log
java -Xmx2g -Xms1g -jar dms-service.jar > logs/startup.log 2>&1 &

# Monitor health endpoint
while ! curl -f http://localhost:9092/actuator/health; do
    echo "Waiting for service..."
    sleep 5
done
echo "Service is UP"
```

**2. Database Recovery**
```bash
# Restart MySQL if needed
sudo systemctl restart mysql

# Check database integrity
mysql -u root -p -e "CHECK TABLE dms_db.documents;"

# Repair tables if needed
mysql -u root -p -e "REPAIR TABLE dms_db.documents;"
```

**3. Cache Recovery**
```bash
# Restart Redis if needed
sudo systemctl restart redis

# Clear cache if corrupted
redis-cli FLUSHDB
```

## Data Corruption Recovery

### Immediate Actions (0-30 minutes)

**1. Stop All Write Operations**
```bash
# Put service in read-only mode
curl -X POST http://localhost:9092/actuator/maintenance/readonly

# Stop background jobs
curl -X POST http://localhost:9092/actuator/maintenance/stop-jobs
```

**2. Assess Corruption Scope**
```sql
-- Check table integrity
CHECK TABLE documents;
CHECK TABLE document_permissions;
CHECK TABLE audit_logs;

-- Look for inconsistencies
SELECT COUNT(*) FROM documents WHERE parent_document_id IS NOT NULL 
AND parent_document_id NOT IN (SELECT id FROM documents);

-- Check for orphaned records
SELECT COUNT(*) FROM document_permissions dp 
WHERE dp.document_id NOT IN (SELECT id FROM documents);
```

**3. Create Emergency Backup**
```bash
# Create immediate backup
mysqldump -u root -p --single-transaction --routines --triggers dms_db > emergency_backup_$(date +%Y%m%d_%H%M%S).sql

# Backup file storage
tar -czf file_storage_backup_$(date +%Y%m%d_%H%M%S).tar.gz /app/storage/
```

### Recovery Process (30 minutes - 4 hours)

**1. Restore from Latest Backup**
```bash
# Find latest backup
ls -la /backups/ | grep dms_db | tail -5

# Restore database
mysql -u root -p dms_db < /backups/latest_backup.sql

# Restore file storage
tar -xzf /backups/latest_file_storage.tar.gz -C /app/
```

**2. Data Validation**
```sql
-- Validate data integrity
SELECT 
    (SELECT COUNT(*) FROM documents) as total_documents,
    (SELECT COUNT(*) FROM documents WHERE status = 'ACTIVE') as active_documents,
    (SELECT COUNT(*) FROM document_permissions) as total_permissions,
    (SELECT COUNT(*) FROM audit_logs) as total_audit_logs;

-- Check for recent data
SELECT MAX(created_date) as latest_document FROM documents;
SELECT MAX(timestamp) as latest_audit FROM audit_logs;
```

**3. Incremental Recovery**
```bash
# Apply transaction logs if available
mysql -u root -p dms_db < /backups/transaction_log_after_backup.sql

# Reindex Elasticsearch
curl -X POST http://localhost:9092/api/admin/reindex
```

## Security Breach Response

### Immediate Response (0-15 minutes)

**1. Isolate the System**
```bash
# Block external access
sudo iptables -A INPUT -p tcp --dport 9092 -j DROP

# Stop the service
pkill -f dms-service

# Preserve evidence
cp -r logs/ /secure/incident_$(date +%Y%m%d_%H%M%S)/
```

**2. Assess the Breach**
```sql
-- Check for suspicious activities
SELECT * FROM security_violations 
WHERE timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY severity DESC, timestamp DESC;

-- Check for unauthorized access
SELECT DISTINCT user_id, ip_address, COUNT(*) as attempts
FROM audit_logs 
WHERE action = 'LOGIN' AND timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY user_id, ip_address
HAVING attempts > 10;

-- Check for data access patterns
SELECT user_id, COUNT(*) as document_accesses
FROM audit_logs 
WHERE action IN ('VIEW', 'DOWNLOAD') AND timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY user_id
ORDER BY document_accesses DESC;
```

**3. Secure the Environment**
```bash
# Change all system passwords
mysql -u root -p -e "ALTER USER 'dms_user'@'localhost' IDENTIFIED BY 'new_secure_password';"

# Rotate JWT secrets
sed -i 's/jwt.secret=.*/jwt.secret=new_random_secret_key/' application.properties

# Revoke all active sessions
redis-cli FLUSHDB
```

### Investigation and Recovery (1-24 hours)

**1. Forensic Analysis**
```bash
# Analyze access logs
grep "UNAUTHORIZED\|VIOLATION\|FAILED" logs/dms-service.log > security_analysis.log

# Check file system for unauthorized changes
find /app -type f -mtime -1 -ls > recent_file_changes.log

# Network analysis
netstat -tulpn > network_connections.log
```

**2. Notify Stakeholders**
```bash
# Send security incident notification
curl -X POST https://alerts.company.com/api/incidents \
  -H "Content-Type: application/json" \
  -d '{
    "severity": "CRITICAL",
    "title": "DMS Security Breach Detected",
    "description": "Unauthorized access detected in DMS system",
    "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"
  }'
```

**3. Implement Additional Security**
```bash
# Enable additional logging
echo "logging.level.com.ascentbusiness.dms_svc.security=TRACE" >> application.properties

# Implement IP whitelisting
echo "security.allowed-ips=***********/24,10.0.0.0/8" >> application.properties

# Enable audit encryption
echo "audit.encryption.enabled=true" >> application.properties
```

## Performance Degradation

### Immediate Triage (0-30 minutes)

**1. Identify Performance Bottleneck**
```bash
# Check system resources
top -n 1 -b | head -20
iostat -x 1 5
free -h

# Check application metrics
curl http://localhost:9092/actuator/metrics/jvm.memory.used
curl http://localhost:9092/actuator/metrics/http.server.requests
```

**2. Quick Performance Fixes**
```bash
# Restart service with more memory
pkill -f dms-service
java -Xmx4g -Xms2g -jar dms-service.jar &

# Clear caches
redis-cli FLUSHDB

# Force garbage collection
jcmd $(pgrep java) GC.run
```

**3. Database Quick Fixes**
```sql
-- Kill long-running queries
SHOW PROCESSLIST;
KILL [PROCESS_ID];

-- Optimize tables
OPTIMIZE TABLE documents;
OPTIMIZE TABLE audit_logs;

-- Update statistics
ANALYZE TABLE documents;
```

### Detailed Performance Recovery (30 minutes - 4 hours)

**1. Scale Resources**
```bash
# Add more application instances
for i in {2..4}; do
    java -Xmx2g -Dserver.port=909$i -jar dms-service.jar &
done

# Update load balancer configuration
# (Implementation depends on your load balancer)
```

**2. Database Optimization**
```sql
-- Add emergency indexes
CREATE INDEX idx_emergency_documents_status_date ON documents(status, created_date);
CREATE INDEX idx_emergency_audit_timestamp ON audit_logs(timestamp);

-- Increase buffer pool
SET GLOBAL innodb_buffer_pool_size = 4294967296;  -- 4GB
```

**3. Cache Optimization**
```bash
# Increase Redis memory
redis-cli CONFIG SET maxmemory 2gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru

# Warm up critical caches
curl -X POST http://localhost:9092/api/admin/cache/warmup
```

## Database Emergencies

### Database Corruption
```bash
# Stop all applications
pkill -f dms-service

# Check and repair tables
mysql -u root -p -e "CHECK TABLE dms_db.documents;"
mysql -u root -p -e "REPAIR TABLE dms_db.documents;"

# Restore from backup if repair fails
mysql -u root -p dms_db < /backups/latest_backup.sql
```

### Database Connection Exhaustion
```sql
-- Check current connections
SHOW PROCESSLIST;
SELECT COUNT(*) as connection_count FROM information_schema.processlist;

-- Kill idle connections
SELECT CONCAT('KILL ', id, ';') FROM information_schema.processlist 
WHERE command = 'Sleep' AND time > 300;

-- Increase connection limit temporarily
SET GLOBAL max_connections = 500;
```

### Disk Space Emergency
```bash
# Check disk usage
df -h

# Clean up old logs
find /var/log -name "*.log" -mtime +7 -delete
find /app/logs -name "*.log.*" -mtime +3 -delete

# Archive old audit logs
mysqldump -u root -p --where="timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY)" dms_db audit_logs > old_audit_logs.sql
mysql -u root -p -e "DELETE FROM dms_db.audit_logs WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY);"
```

## Storage Failures

### Local Storage Failure
```bash
# Check disk health
smartctl -a /dev/sda

# Mount backup storage
mount /dev/sdb1 /app/storage_backup

# Redirect storage to backup location
sed -i 's|storage.local.path=.*|storage.local.path=/app/storage_backup|' application.properties
```

### S3 Storage Failure
```bash
# Switch to backup storage provider
sed -i 's|storage.provider=S3|storage.provider=LOCAL|' application.properties

# Test connectivity to alternative S3 region
aws s3 ls s3://backup-bucket-name --region us-west-2
```

## Communication Procedures

### Incident Communication Template

**Initial Notification (within 15 minutes):**
```
INCIDENT ALERT - DMS System
Severity: [P1/P2/P3/P4]
Status: INVESTIGATING
Impact: [Description of user impact]
Start Time: [Timestamp]
Estimated Resolution: [Time estimate]
Next Update: [Time for next update]
Incident Commander: [Name]
```

**Status Updates (every 30 minutes for P1, hourly for P2):**
```
INCIDENT UPDATE - DMS System
Severity: [P1/P2/P3/P4]
Status: [INVESTIGATING/IDENTIFIED/FIXING/MONITORING]
Progress: [What has been done]
Next Steps: [What will be done next]
ETA: [Updated time estimate]
Next Update: [Time for next update]
```

**Resolution Notification:**
```
INCIDENT RESOLVED - DMS System
Resolution Time: [Timestamp]
Root Cause: [Brief description]
Resolution: [What was done to fix it]
Prevention: [Steps to prevent recurrence]
Post-Mortem: [Link to detailed analysis]
```

### Escalation Contacts

**Primary On-Call:** [Phone/Email]
**Secondary On-Call:** [Phone/Email]
**Engineering Manager:** [Phone/Email]
**Database Administrator:** [Phone/Email]
**Security Team:** [Phone/Email]
**Executive Escalation:** [Phone/Email]

---

**Last Updated**: December 2024  
**Version**: 1.0.0

**REMEMBER**: In any emergency, prioritize data integrity and user safety over system availability. When in doubt, escalate immediately.
