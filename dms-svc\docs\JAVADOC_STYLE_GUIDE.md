# DMS JavaDoc Style Guide

**Last Updated:** July 23, 2024  
**Version:** 1.0  
**Generated:** 2024-07-23T11:30:00+05:30

## Table of Contents

1. [General Guidelines](#general-guidelines)
2. [Class Documentation](#class-documentation)
3. [Method Documentation](#method-documentation)
4. [Field Documentation](#field-documentation)
5. [Tags and Annotations](#tags-and-annotations)
6. [Examples](#examples)
7. [Best Practices](#best-practices)
8. [Common Mistakes](#common-mistakes)

## General Guidelines

### Purpose
JavaDoc comments serve as the primary source of API documentation for the DMS project. They should be clear, concise, and provide sufficient information for developers to understand and use the code effectively.

### Basic Structure
```java
/**
 * Brief description of the class, method, or field.
 * 
 * <p>More detailed description if needed. Use paragraph tags
 * for multiple paragraphs.
 * 
 * @param paramName description of parameter
 * @return description of return value
 * @throws ExceptionType description of when this exception is thrown
 * @since version when this was added
 * <AUTHOR> name
 * @version current version
 */
```

### Writing Style
- Use third person singular present tense ("Returns the user ID" not "Return the user ID")
- Start with a capital letter and end with a period
- Be concise but complete
- Use active voice when possible
- Avoid implementation details unless necessary for usage

## Class Documentation

### Required Elements
Every public class must have JavaDoc documentation including:
- Brief description of the class purpose
- Detailed description of functionality (if complex)
- Usage examples (for complex classes)
- Author information
- Version information
- Since tag (when applicable)

### Class Documentation Template
```java
/**
 * Service class for managing document operations in the DMS.
 * 
 * <p>This service provides comprehensive document management functionality
 * including upload, download, search, and metadata management. It integrates
 * with various storage providers and handles different file types.
 * 
 * <p>Example usage:
 * <pre>{@code
 * DocumentService service = new DocumentService();
 * Document doc = service.uploadDocument(file, metadata);
 * Optional<Document> retrieved = service.getDocument(doc.getId());
 * }</pre>
 * 
 * <AUTHOR> Development Team
 * @version 1.2.0
 * @since 1.0.0
 */
public class DocumentService {
    // Class implementation
}
```

### Interface Documentation
```java
/**
 * Repository interface for document entity operations.
 * 
 * <p>Extends Spring Data JPA repository to provide standard CRUD operations
 * and custom query methods for document management.
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
public interface DocumentRepository extends JpaRepository<Document, Long> {
    // Interface methods
}
```

## Method Documentation

### Required Elements
All public methods must include:
- Brief description of what the method does
- @param tags for all parameters
- @return tag (unless void)
- @throws tags for all checked exceptions and important runtime exceptions

### Method Documentation Template
```java
/**
 * Uploads a document to the specified storage provider.
 * 
 * <p>This method handles file validation, virus scanning, and metadata
 * extraction before storing the document. The upload process is
 * asynchronous for large files.
 * 
 * @param file the multipart file to upload, must not be null
 * @param metadata document metadata including title and description
 * @param storageProvider the target storage provider, defaults to primary if null
 * @return the created document entity with generated ID and metadata
 * @throws IllegalArgumentException if file is null or empty
 * @throws VirusDetectedException if virus is detected in the file
 * @throws StorageException if storage operation fails
 * @throws MaxFileSizeExceededException if file exceeds size limits
 * @since 1.0.0
 */
public Document uploadDocument(MultipartFile file, 
                              DocumentMetadata metadata, 
                              StorageProvider storageProvider) {
    // Method implementation
}
```

### Constructor Documentation
```java
/**
 * Creates a new document service with the specified configuration.
 * 
 * @param storageService the storage service for file operations
 * @param virusScanner the virus scanning service
 * @param config the file processing configuration
 * @throws IllegalArgumentException if any parameter is null
 */
public DocumentService(StorageService storageService,
                      VirusScanner virusScanner,
                      FileProcessingConfig config) {
    // Constructor implementation
}
```

### Getter/Setter Documentation
```java
/**
 * Returns the document's unique identifier.
 * 
 * @return the document ID, or null if not persisted yet
 */
public Long getId() {
    return id;
}

/**
 * Sets the document's title.
 * 
 * @param title the document title, must not be null or empty
 * @throws IllegalArgumentException if title is null or empty
 */
public void setTitle(String title) {
    // Setter implementation
}
```

## Field Documentation

### Public Constants
```java
/**
 * Maximum allowed file size for direct upload (10MB).
 * 
 * <p>Files larger than this threshold will require chunked upload
 * or async processing.
 */
public static final long MAX_DIRECT_UPLOAD_SIZE = 10 * 1024 * 1024;

/**
 * Default chunk size for large file uploads (5MB).
 */
public static final int DEFAULT_CHUNK_SIZE = 5 * 1024 * 1024;
```

### Configuration Properties
```java
/**
 * The base directory for temporary file storage during processing.
 * 
 * <p>This directory is used for chunked uploads and async processing.
 * It should have sufficient space and appropriate permissions.
 */
@Value("${dms.temp.directory:./temp}")
private String tempDirectory;
```

## Tags and Annotations

### Standard JavaDoc Tags

#### @param
```java
/**
 * @param userId the unique identifier of the user, must be positive
 * @param includeDeleted whether to include soft-deleted documents
 */
```

#### @return
```java
/**
 * @return list of documents owned by the user, never null but may be empty
 * @return true if the operation was successful, false otherwise
 * @return the created document with generated ID and timestamps
 */
```

#### @throws
```java
/**
 * @throws IllegalArgumentException if userId is null or negative
 * @throws DocumentNotFoundException if no document exists with the given ID
 * @throws SecurityException if user lacks permission to access the document
 */
```

#### @since
```java
/**
 * @since 1.0.0
 * @since 1.2.0 Added support for multiple storage providers
 */
```

#### @deprecated
```java
/**
 * @deprecated since 1.3.0, use {@link #uploadDocumentAsync(MultipartFile)} instead.
 *             This method will be removed in version 2.0.0.
 */
```

#### @see
```java
/**
 * @see DocumentRepository#findByUserId(Long)
 * @see <a href="https://docs.dms.com/api">DMS API Documentation</a>
 * @see #uploadDocument(MultipartFile, DocumentMetadata)
 */
```

#### <AUTHOR>
/**
 * <AUTHOR> Doe
 * <AUTHOR> Development Team
 */
```

#### @version
```java
/**
 * @version 1.2.0
 */
```

### Custom Tags
```java
/**
 * @apiNote This method is thread-safe and can be called concurrently
 * @implNote Uses optimistic locking to handle concurrent modifications
 * @implSpec The implementation must validate all input parameters
 */
```

## Examples

### Service Class Example
```java
/**
 * Service for managing document search operations.
 * 
 * <p>Provides full-text search capabilities using Elasticsearch integration.
 * Supports advanced search features including filters, sorting, and faceting.
 * 
 * <p>Example usage:
 * <pre>{@code
 * SearchService searchService = new SearchService();
 * SearchRequest request = SearchRequest.builder()
 *     .query("financial report")
 *     .filters(Map.of("type", "PDF"))
 *     .build();
 * SearchResult result = searchService.search(request);
 * }</pre>
 * 
 * <AUTHOR> Team
 * @version 2.1.0
 * @since 1.1.0
 */
@Service
public class SearchService {

    /**
     * Performs a full-text search across all indexed documents.
     * 
     * <p>The search uses Elasticsearch's multi-match query with fuzzy matching
     * enabled. Results are ranked by relevance score and filtered based on
     * user permissions.
     * 
     * @param request the search request containing query and filters
     * @return search results with documents and metadata, never null
     * @throws IllegalArgumentException if request is null or invalid
     * @throws SearchException if the search operation fails
     * @see SearchRequest
     * @see SearchResult
     * @since 1.1.0
     */
    public SearchResult search(SearchRequest request) {
        // Implementation
    }

    /**
     * Indexes a document for search operations.
     * 
     * @param document the document to index, must not be null
     * @throws IndexingException if the indexing operation fails
     * @since 1.1.0
     */
    public void indexDocument(Document document) {
        // Implementation
    }
}
```

### Entity Class Example
```java
/**
 * Entity representing a document in the DMS.
 * 
 * <p>Contains document metadata, file information, and audit fields.
 * Supports versioning and soft deletion.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Entity
@Table(name = "documents")
public class Document {

    /**
     * The unique identifier for this document.
     * 
     * <p>Generated automatically when the document is first persisted.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * The display name of the document.
     * 
     * <p>This is typically the original filename but can be modified
     * by users. Must not be null or empty.
     */
    @Column(nullable = false)
    private String name;

    /**
     * Returns the document's unique identifier.
     * 
     * @return the document ID, or null if not yet persisted
     */
    public Long getId() {
        return id;
    }

    /**
     * Sets the document's display name.
     * 
     * @param name the document name, must not be null or empty
     * @throws IllegalArgumentException if name is null or empty
     */
    public void setName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Document name cannot be null or empty");
        }
        this.name = name;
    }
}
```

## Best Practices

### 1. Be Consistent
- Use the same terminology throughout the codebase
- Follow the same format for similar types of documentation
- Use consistent parameter descriptions

### 2. Focus on the Contract
- Document what the method does, not how it does it
- Describe preconditions and postconditions
- Specify valid parameter ranges and return value constraints

### 3. Use Code Examples
- Provide examples for complex APIs
- Show typical usage patterns
- Include error handling examples when relevant

### 4. Link Related Elements
- Use @see tags to reference related methods/classes
- Link to external documentation when helpful
- Cross-reference overloaded methods

### 5. Keep It Current
- Update documentation when code changes
- Remove outdated information
- Use @deprecated for obsolete methods

### 6. Consider Your Audience
- Write for developers who will use your code
- Assume basic Java knowledge but explain domain concepts
- Provide context for business logic

## Common Mistakes

### 1. Empty or Minimal Comments
```java
// BAD
/**
 * Gets the user.
 */
public User getUser() { }

// GOOD
/**
 * Retrieves the currently authenticated user from the security context.
 * 
 * @return the current user, or null if no user is authenticated
 * @throws SecurityException if security context is not available
 */
public User getCurrentUser() { }
```

### 2. Implementation Details
```java
// BAD
/**
 * Uses HashMap to store user data and iterates through entries.
 */
public List<User> getActiveUsers() { }

// GOOD
/**
 * Returns all users with active status.
 * 
 * @return list of active users, ordered by last login date
 */
public List<User> getActiveUsers() { }
```

### 3. Missing Parameter Descriptions
```java
// BAD
/**
 * Updates user information.
 * 
 * @param user
 * @param updateFields
 */
public void updateUser(User user, Set<String> updateFields) { }

// GOOD
/**
 * Updates specific fields of a user entity.
 * 
 * @param user the user to update, must not be null
 * @param updateFields set of field names to update, empty set updates nothing
 * @throws IllegalArgumentException if user is null
 */
public void updateUser(User user, Set<String> updateFields) { }
```

### 4. Inconsistent Formatting
```java
// BAD
/**
 * gets the document by id
 * @param id document id
 * @return Document or null
 */

// GOOD
/**
 * Retrieves a document by its unique identifier.
 * 
 * @param id the document ID, must be positive
 * @return the document if found, null otherwise
 */
```

### 5. Outdated Information
```java
// BAD - Method signature changed but JavaDoc wasn't updated
/**
 * Uploads a file to the default storage.
 * 
 * @param file the file to upload
 * @return the uploaded document
 */
public Document uploadFile(MultipartFile file, StorageProvider provider) { }

// GOOD
/**
 * Uploads a file to the specified storage provider.
 * 
 * @param file the file to upload, must not be null
 * @param provider the target storage provider
 * @return the uploaded document with generated metadata
 */
public Document uploadFile(MultipartFile file, StorageProvider provider) { }
```

---

## Conclusion

Good JavaDoc documentation is essential for maintaining a professional, usable codebase. Follow these guidelines to ensure your documentation is clear, complete, and helpful to other developers.

Remember: JavaDoc is not just comments—it's the API contract that other developers rely on. Take the time to write it well.