# Docker Deployment Troubleshooting Guide

**Last Updated:** July 23, 2024
**Version:** 1.2
**Generated:** 2024-07-23T11:40:00+05:30

## Common Deployment Issues and Solutions

### 1. Port Configuration Mismatch

**Issue:** Application fails to start or is unreachable after Docker deployment.

**Common Symptoms:**
- Connection refused errors
- Health check failures
- Service unreachable from host

**Root Cause:** Port mismatch between application configuration and Docker setup.

**Solution:**
Ensure ports are consistent across all configuration files:

**Application Configuration (`application.properties`):**
```properties
server.port=9093
```

**Docker Compose (`docker-compose.yml`):**
```yaml
services:
  dms-app:
    ports:
      - "9093:9093"  # Host:Container
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9093/actuator/health"]
```

**Dockerfile:**
```dockerfile
EXPOSE 9093
HEALTHCHECK CMD curl -f http://localhost:9093/actuator/health || exit 1
```

### 2. OpenTelemetry Configuration Issues

**Issue:** Application fails to start with OpenTelemetry-related errors.

**Error Example:**
```
otel.metrics.exporter set to "prometheus" but opentelemetry-exporter-prometheus not found on classpath
```

**Solution:**
Use console exporter for metrics in Docker environment:

```yaml
environment:
  - OTEL_METRICS_EXPORTER=console
  - OTEL_TRACES_EXPORTER=zipkin
  - OTEL_LOGS_EXPORTER=console
```

### 3. Database Connection Issues

**Issue:** Application cannot connect to MySQL database.

**Common Errors:**
- Connection timeout
- Access denied
- Unknown database

**Solution:**
Verify database configuration in Docker Compose:

```yaml
environment:
  - SPRING_DATASOURCE_URL=*******************************************************************************************
  - SPRING_DATASOURCE_USERNAME=dms_user
  - SPRING_DATASOURCE_PASSWORD=dms_password
```

**Troubleshooting Steps:**
1. Check if MySQL container is running: `docker-compose ps`
2. Verify database initialization: `docker-compose logs mysql`
3. Test database connection: `docker-compose exec mysql mysql -u dms_user -p dms_db`

**MySQL Diagnostic Commands:**
```sql
-- Check database status
SHOW DATABASES;
SELECT DATABASE();

-- Verify table structure
SHOW TABLES;
SELECT COUNT(*) FROM documents;

-- Check user permissions
SHOW GRANTS FOR 'dms_user'@'%';
SELECT User, Host FROM mysql.user WHERE User = 'dms_user';

-- Monitor database performance
SHOW PROCESSLIST;
SELECT * FROM INFORMATION_SCHEMA.PROCESSLIST WHERE COMMAND != 'Sleep';

-- Check database size and usage
SELECT
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables
WHERE table_schema = 'dms_db'
GROUP BY table_schema;
```

### 4. Redis Connection Issues

**Issue:** Application cannot connect to Redis cache.

**Solution:**
Ensure Redis configuration matches Docker setup:

```yaml
environment:
  - SPRING_REDIS_HOST=redis
  - SPRING_REDIS_PORT=6379
  - SPRING_REDIS_PASSWORD=redis_password
```

**Troubleshooting:**
```bash
# Test Redis connection
docker-compose exec redis redis-cli -a redis_password ping
```

### 5. Volume Mount Issues

**Issue:** File storage or logs not persisting.

**Solution:**
Verify volume mounts in Docker Compose:

```yaml
volumes:
  - dms_storage:/app/storage
  - dms_logs:/app/logs
```

**Troubleshooting:**
```bash
# Check volume status
docker volume ls
docker volume inspect dms_storage
```

### 6. Memory and Resource Issues

**Issue:** Application crashes or performs poorly.

**Solution:**
Configure resource limits:

```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 4G
    reservations:
      cpus: '1.0'
      memory: 2G
```

**JVM Configuration:**
```dockerfile
ENV JAVA_OPTS="-XX:+UseContainerSupport \
               -XX:MaxRAMPercentage=75.0 \
               -XX:+UseG1GC"
```

### 7. Network Connectivity Issues

**Issue:** Services cannot communicate with each other.

**Solution:**
Ensure all services are on the same network:

```yaml
networks:
  - dms-network

networks:
  dms-network:
    driver: bridge
```

**Troubleshooting:**
```bash
# Check network connectivity
docker-compose exec dms-app ping mysql
docker-compose exec dms-app ping redis
```

### 8. Environment Variable Issues

**Issue:** Configuration not being applied correctly.

**Solution:**
Verify environment variables are properly set:

```bash
# Check environment variables in container
docker-compose exec dms-app env | grep -E "(SPRING|OTEL|DMS)"
```

**Common Variables:**
```yaml
environment:
  - SPRING_PROFILES_ACTIVE=docker,local
  - DMS_STORAGE_PROVIDER=LOCAL
  - JWT_SECRET=your-secret-key
```

## Diagnostic Commands

### 1. Check Service Status
```bash
# View all services
docker-compose ps

# Check specific service logs
docker-compose logs dms-app
docker-compose logs mysql
docker-compose logs redis
```

### 2. Health Checks
```bash
# Application health
curl http://localhost:9093/actuator/health

# Detailed health information
curl http://localhost:9093/actuator/health | jq

# Metrics endpoint
curl http://localhost:9093/actuator/prometheus
```

### 3. Container Inspection
```bash
# Inspect container configuration
docker inspect dms-app

# Check resource usage
docker stats dms-app

# Execute commands in container
docker-compose exec dms-app bash
```

### 4. Network Debugging
```bash
# List networks
docker network ls

# Inspect network
docker network inspect dms_dms-network

# Test connectivity
docker-compose exec dms-app nslookup mysql
```

## Quick Fix Checklist

When deployment fails, check these items in order:

1. **Port Configuration**
   - [ ] Application port matches Docker configuration
   - [ ] Health check URLs use correct port
   - [ ] Dockerfile EXPOSE directive is correct

2. **Dependencies**
   - [ ] All required services are running
   - [ ] Database is initialized and accessible
   - [ ] Redis is running and accessible

3. **Environment Variables**
   - [ ] All required environment variables are set
   - [ ] Database credentials are correct
   - [ ] OpenTelemetry configuration is valid

4. **Resources**
   - [ ] Sufficient memory allocated
   - [ ] CPU limits are appropriate
   - [ ] Disk space is available

5. **Network**
   - [ ] All services are on the same network
   - [ ] Service names resolve correctly
   - [ ] Firewall rules allow communication

## Production Deployment Considerations

### 1. Security
- Use secrets management for sensitive data
- Enable TLS/SSL for external communication
- Implement proper authentication and authorization

### 2. Monitoring
- Configure log aggregation
- Set up metrics collection
- Implement alerting for critical issues

### 3. Backup and Recovery
- Regular database backups
- Volume backup strategies
- Disaster recovery procedures

### 4. Scaling
- Horizontal scaling configuration
- Load balancer setup
- Database connection pooling

## Related Documentation

- [OpenTelemetry Troubleshooting](./OPENTELEMETRY_TROUBLESHOOTING.md)
- [Comprehensive Deployment Guide](../deployment/COMPREHENSIVE_DEPLOYMENT_GUIDE.md)
- [Docker Compose Reference](https://docs.docker.com/compose/)
- [Spring Boot Docker Guide](https://spring.io/guides/gs/spring-boot-docker/)