# Test Updates for Chunked Upload Enhancement

## Overview

This document describes the test case updates required for the chunked upload enhancement that modified `uploadDocumentEx` to handle large files internally instead of throwing `FileTooLargeException`.

## Test Changes Made

### 1. New Integration Tests Added

#### Test: `shouldHandleLargeFilesWithInternalChunkedProcessing`
- **Purpose**: Verifies that large files are processed internally using chunked strategy instead of throwing exceptions
- **File Size**: `fileProcessingConfig.getAsyncProcessingThreshold() + 100KB` (designed to trigger CHUNKED strategy while staying within MySQL packet limits)
- **Expected Behavior**: 
  - Returns `ProcessingStrategy.CHUNKED`
  - Returns `ProcessingStatus.COMPLETED`
  - Creates a valid document with all metadata
  - No exceptions thrown

#### Test: `shouldHandleLargeFilesWithCustomChunkSize`
- **Purpose**: Verifies that custom chunk sizes are respected during internal chunked processing
- **File Size**: `fileProcessingConfig.getAsyncProcessingThreshold() + 200KB`
- **Custom Chunk Size**: 64KB
- **Expected Behavior**:
  - Uses custom chunk size for processing
  - Returns completed document with CHUNKED strategy
  - Processes successfully without errors

### 2. Test Environment Considerations

#### MySQL Packet Size Limitation
- **Issue**: Test environment MySQL has `max_allowed_packet` limit of 1MB
- **Solution**: Reduced test file sizes to stay within database limits while still triggering chunked processing
- **Impact**: Tests use smaller files (100-200KB over threshold) instead of multi-MB files

#### File Size Strategy
- **Before**: Used large files (1-2MB over threshold) that exceeded MySQL limits
- **After**: Use smaller files that still trigger CHUNKED strategy but fit within database constraints
- **Rationale**: The chunked processing logic is the same regardless of file size, so smaller test files are sufficient

### 3. Test Validation Points

Each test verifies:
1. **Processing Strategy**: Confirms `CHUNKED` strategy is used
2. **Processing Status**: Confirms `COMPLETED` status (not pending or failed)
3. **Document Creation**: Verifies document is created with valid ID
4. **Metadata Preservation**: Confirms name, description, filename, and MIME type are correct
5. **No Exceptions**: Ensures no `FileTooLargeException` or other errors are thrown

### 4. Backward Compatibility

#### Existing Tests
- **No Breaking Changes**: All existing tests continue to work
- **No Test Removal**: No existing test cases were removed or modified
- **Additive Changes**: Only new test cases were added

#### Test Coverage
- **Direct Strategy**: Existing tests cover small files (DIRECT processing)
- **Async Strategy**: Existing tests cover medium files (ASYNC processing)  
- **Chunked Strategy**: New tests cover large files (CHUNKED processing)

## Test Execution

### Running Individual Tests
```bash
# Run specific chunked upload test
mvn test -Dtest=ExtendedFileProcessingIntegrationTest#shouldHandleLargeFilesWithInternalChunkedProcessing

# Run custom chunk size test
mvn test -Dtest=ExtendedFileProcessingIntegrationTest#shouldHandleLargeFilesWithCustomChunkSize
```

### Running All Extended Processing Tests
```bash
mvn test -Dtest=ExtendedFileProcessingIntegrationTest
```

### Expected Results
- **Before Enhancement**: Tests would fail with `FileTooLargeException`
- **After Enhancement**: Tests pass with completed documents

## Database Configuration for Tests

### Current Test Setup
- Uses MySQL testcontainer with default configuration
- `max_allowed_packet` = 1MB (1,048,576 bytes)
- File content stored as BLOB in database

### Recommendations for Production Testing
For testing with larger files in production-like environments:

```sql
-- Increase MySQL packet size for larger file testing
SET GLOBAL max_allowed_packet = 16777216; -- 16MB
```

Or configure in MySQL configuration:
```ini
[mysqld]
max_allowed_packet = 16M
```

## Test Data Considerations

### File Size Calculations
```java
// Get processing thresholds from configuration
long directThreshold = fileProcessingConfig.getDirectProcessingThreshold();
long asyncThreshold = fileProcessingConfig.getAsyncProcessingThreshold();

// Test file sizes
long smallFile = directThreshold - 1024;           // DIRECT strategy
long mediumFile = asyncThreshold - 1024;           // ASYNC strategy  
long largeFile = asyncThreshold + 100 * 1024;      // CHUNKED strategy (test-safe size)
```

### Chunk Size Testing
```java
// Default chunk size
int defaultChunkSize = fileProcessingConfig.getOptimalChunkSize(fileSize);

// Custom chunk sizes for testing
int smallChunkSize = 64 * 1024;    // 64KB - more chunks, good for testing
int mediumChunkSize = 256 * 1024;  // 256KB - balanced
int largeChunkSize = 1024 * 1024;  // 1MB - fewer chunks
```

## Future Test Enhancements

### Potential Additional Tests
1. **Error Handling**: Test chunked upload failure scenarios
2. **Progress Tracking**: Verify chunk upload progress reporting
3. **Session Management**: Test session cleanup and expiration
4. **Concurrent Uploads**: Test multiple simultaneous chunked uploads
5. **Memory Usage**: Verify memory efficiency during chunked processing

### Performance Testing
Consider adding performance tests for:
- Large file processing time
- Memory usage during chunked uploads
- Database performance with large BLOBs
- Concurrent chunked upload handling

## Troubleshooting

### Common Test Issues

#### MySQL Packet Size Error
```
Packet for query is too large (X > 1,048,576)
```
**Solution**: Reduce test file size or increase MySQL `max_allowed_packet`

#### Chunked Upload Session Not Found
**Cause**: Session cleanup or transaction isolation issues
**Solution**: Ensure proper test transaction management and session persistence

#### Processing Strategy Mismatch
**Cause**: File size doesn't exceed expected thresholds
**Solution**: Verify `FileProcessingConfig` values in test environment

## Summary

The test updates successfully validate the enhanced chunked upload functionality while working within the constraints of the test environment. The new tests ensure that:

1. Large files are processed internally without exceptions
2. Custom chunk sizes are respected
3. Documents are created successfully with proper metadata
4. The enhancement maintains backward compatibility
5. All processing strategies (DIRECT, ASYNC, CHUNKED) work correctly

These tests provide confidence that the chunked upload enhancement works as intended and doesn't break existing functionality.