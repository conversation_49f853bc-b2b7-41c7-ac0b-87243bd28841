# Performance Issues Troubleshooting Guide

This guide helps diagnose and resolve performance issues in the DMS system.

## Table of Contents

1. [Performance Monitoring](#performance-monitoring)
2. [Response Time Issues](#response-time-issues)
3. [Memory Performance](#memory-performance)
4. [Database Performance](#database-performance)
5. [Cache Performance](#cache-performance)
6. [Storage Performance](#storage-performance)
7. [Network Performance](#network-performance)
8. [Optimization Strategies](#optimization-strategies)

## Performance Monitoring

### Key Performance Indicators (KPIs)

**Response Time Targets:**
- API endpoints: < 2 seconds (95th percentile)
- Document upload: < 10 seconds for files < 100MB
- Search queries: < 1 second
- Database queries: < 500ms average

**Throughput Targets:**
- Concurrent users: 100+ simultaneous users
- Document uploads: 50+ uploads per minute
- API requests: 1000+ requests per minute

### Monitoring Tools

**Built-in Metrics:**
```bash
# Application health
curl http://localhost:9092/actuator/health

# JVM metrics
curl http://localhost:9092/actuator/metrics/jvm.memory.used
curl http://localhost:9092/actuator/metrics/jvm.gc.pause

# HTTP metrics
curl http://localhost:9092/actuator/metrics/http.server.requests

# Database metrics
curl http://localhost:9092/actuator/metrics/hikaricp.connections.active
```

**Prometheus Metrics:**
```bash
# Export all metrics for Prometheus
curl http://localhost:9092/actuator/prometheus
```

**Custom Business Metrics:**
```bash
# Document operation metrics
curl http://localhost:9092/actuator/metrics/dms.documents.uploaded
curl http://localhost:9092/actuator/metrics/dms.documents.downloaded
curl http://localhost:9092/actuator/metrics/dms.search.queries
```

## Response Time Issues

### Symptoms
- API responses taking > 5 seconds
- Timeouts in client applications
- High response time variance
- User complaints about slow performance

### Diagnostic Steps

**1. Identify Slow Endpoints:**
```bash
# Check HTTP request metrics
curl http://localhost:9092/actuator/metrics/http.server.requests | grep -A 5 "max"

# Enable request logging
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
```

**2. Analyze Request Patterns:**
```bash
# Check for correlation patterns
grep "correlation-id" logs/dms-service.log | grep "duration" | sort -k5 -nr
```

**3. Profile Application:**
```bash
# Enable JFR profiling
java -XX:+FlightRecorder -XX:StartFlightRecording=duration=60s,filename=profile.jfr

# Use async profiler
java -jar async-profiler.jar -d 60 -f profile.html [PID]
```

### Common Causes and Solutions

**Database Query Performance:**
```sql
-- Find slow queries
SELECT query_time, sql_text FROM mysql.slow_log ORDER BY query_time DESC LIMIT 10;

-- Check for missing indexes
EXPLAIN SELECT * FROM documents WHERE name LIKE '%search%';

-- Add appropriate indexes
CREATE INDEX idx_documents_name ON documents(name);
CREATE INDEX idx_documents_creator_status ON documents(creator_user_id, status);
```

**N+1 Query Problems:**
```java
// Problem: N+1 queries for document permissions
@OneToMany(fetch = FetchType.LAZY)
private List<DocumentPermission> permissions;

// Solution: Use JOIN FETCH
@Query("SELECT d FROM Document d LEFT JOIN FETCH d.permissions WHERE d.id = :id")
Document findByIdWithPermissions(@Param("id") Long id);
```

**Large Result Sets:**
```java
// Problem: Loading all documents
List<Document> findAll();

// Solution: Use pagination
Page<Document> findAll(Pageable pageable);
```

## Memory Performance

### Memory Issues Symptoms
- OutOfMemoryError exceptions
- Frequent garbage collection
- High memory usage (> 80% of heap)
- Application becoming unresponsive

### Memory Analysis

**1. Monitor Memory Usage:**
```bash
# JVM memory metrics
curl http://localhost:9092/actuator/metrics/jvm.memory.used
curl http://localhost:9092/actuator/metrics/jvm.memory.max

# Garbage collection metrics
curl http://localhost:9092/actuator/metrics/jvm.gc.pause
curl http://localhost:9092/actuator/metrics/jvm.gc.memory.allocated
```

**2. Generate Heap Dump:**
```bash
# Generate heap dump
jcmd [PID] GC.run_finalization
jcmd [PID] VM.gc
jmap -dump:format=b,file=heapdump.hprof [PID]

# Analyze with Eclipse MAT or VisualVM
```

**3. Monitor Garbage Collection:**
```bash
# Enable GC logging
java -XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:gc.log

# Analyze GC logs
tail -f gc.log | grep "Full GC"
```

### Memory Optimization

**1. Tune JVM Parameters:**
```bash
# Increase heap size
java -Xmx4g -Xms2g -jar dms-service.jar

# Optimize GC
java -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -jar dms-service.jar

# Enable compressed OOPs
java -XX:+UseCompressedOops -jar dms-service.jar
```

**2. Fix Memory Leaks:**
```java
// Problem: Caching without eviction
private static final Map<String, Document> cache = new HashMap<>();

// Solution: Use bounded cache with TTL
@Cacheable(value = "documents", key = "#id")
@CacheEvict(value = "documents", allEntries = true, condition = "#result.size() > 1000")
```

**3. Optimize Data Structures:**
```java
// Problem: Loading full document content
@Lob
private byte[] fileContent;

// Solution: Lazy loading for large fields
@Lob
@Basic(fetch = FetchType.LAZY)
private byte[] fileContent;
```

## Database Performance

### Database Performance Issues
- Slow query execution
- High database CPU usage
- Connection pool exhaustion
- Lock contention

### Database Optimization

**1. Query Optimization:**
```sql
-- Analyze query execution plans
EXPLAIN FORMAT=JSON SELECT * FROM documents d 
JOIN document_permissions dp ON d.id = dp.document_id 
WHERE d.status = 'ACTIVE' AND dp.user_id = 'user123';

-- Add composite indexes
CREATE INDEX idx_documents_status_creator ON documents(status, creator_user_id);
CREATE INDEX idx_permissions_document_user ON document_permissions(document_id, user_id);
```

**2. Connection Pool Tuning:**
```properties
# Optimize connection pool
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000
```

**3. Database Monitoring Commands:**
```sql
-- Monitor active connections
SHOW PROCESSLIST;
SELECT COUNT(*) as active_connections FROM information_schema.processlist;

-- Check database performance metrics
SHOW GLOBAL STATUS LIKE 'Threads_connected';
SHOW GLOBAL STATUS LIKE 'Threads_running';
SHOW GLOBAL STATUS LIKE 'Queries';
SHOW GLOBAL STATUS LIKE 'Slow_queries';

-- Monitor table locks and deadlocks
SHOW ENGINE INNODB STATUS;
SELECT * FROM information_schema.innodb_locks;
SELECT * FROM information_schema.innodb_lock_waits;

-- Check buffer pool usage
SHOW GLOBAL STATUS LIKE 'Innodb_buffer_pool%';

-- Monitor query cache performance
SHOW GLOBAL STATUS LIKE 'Qcache%';

-- Check table sizes and row counts
SELECT
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
    ROUND((data_length / 1024 / 1024), 2) AS 'Data (MB)',
    ROUND((index_length / 1024 / 1024), 2) AS 'Index (MB)'
FROM information_schema.tables
WHERE table_schema = 'dms_db'
ORDER BY (data_length + index_length) DESC;

-- Monitor slow queries
SELECT
    query_time,
    lock_time,
    rows_sent,
    rows_examined,
    sql_text
FROM mysql.slow_log
ORDER BY query_time DESC
LIMIT 10;
```

**4. Database Configuration:**
```sql
-- MySQL optimization
SET GLOBAL innodb_buffer_pool_size = 2147483648;  -- 2GB
SET GLOBAL query_cache_size = 268435456;          -- 256MB
SET GLOBAL max_connections = 200;
SET GLOBAL innodb_log_file_size = 268435456;      -- 256MB
```

**5. Batch Operations:**
```java
// Problem: Individual inserts
for (Document doc : documents) {
    documentRepository.save(doc);
}

// Solution: Batch processing
@Transactional
public void saveDocuments(List<Document> documents) {
    for (int i = 0; i < documents.size(); i++) {
        documentRepository.save(documents.get(i));
        if (i % 50 == 0) {
            entityManager.flush();
            entityManager.clear();
        }
    }
}
```

## Cache Performance

### Cache Issues
- Cache misses causing database load
- Cache eviction too frequent
- Memory pressure from large caches
- Stale data in cache

### Cache Optimization

**1. Monitor Cache Performance:**
```bash
# Redis cache metrics
redis-cli info stats
redis-cli info memory

# Application cache metrics
curl http://localhost:9092/actuator/metrics/cache.gets
curl http://localhost:9092/actuator/metrics/cache.puts
curl http://localhost:9092/actuator/metrics/cache.evictions
```

**2. Optimize Cache Configuration:**
```properties
# Redis configuration
spring.cache.redis.time-to-live=3600000  # 1 hour
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true

# Cache sizes
spring.cache.caffeine.spec=maximumSize=1000,expireAfterWrite=30m
```

**3. Implement Smart Caching:**
```java
// Cache with conditional eviction
@Cacheable(value = "documents", key = "#id", 
           condition = "#result != null && #result.fileSize < 1048576")
public Document getDocumentById(Long id) {
    return documentRepository.findById(id);
}

// Cache invalidation on updates
@CacheEvict(value = "documents", key = "#document.id")
public Document updateDocument(Document document) {
    return documentRepository.save(document);
}
```

## Storage Performance

### Storage Issues
- Slow file uploads/downloads
- Storage provider timeouts
- Disk space issues
- Network latency to storage

### Storage Optimization

**1. Monitor Storage Performance:**
```bash
# Disk I/O monitoring
iostat -x 1 10
iotop -o

# Network monitoring for S3
ping s3.amazonaws.com
traceroute s3.amazonaws.com
```

**2. Optimize File Operations:**
```java
// Problem: Synchronous file operations
public void uploadFile(MultipartFile file) {
    storageService.store(file);  // Blocks thread
}

// Solution: Asynchronous processing
@Async
public CompletableFuture<Void> uploadFileAsync(MultipartFile file) {
    return CompletableFuture.runAsync(() -> storageService.store(file));
}
```

**3. Implement File Streaming:**
```java
// Problem: Loading entire file into memory
byte[] content = file.getBytes();

// Solution: Stream processing
try (InputStream inputStream = file.getInputStream()) {
    storageService.storeStream(inputStream, file.getSize());
}
```

## Network Performance

### Network Issues
- High latency between services
- Bandwidth limitations
- DNS resolution delays
- Connection timeouts

### Network Optimization

**1. Monitor Network Performance:**
```bash
# Network latency
ping database-server
ping redis-server
ping elasticsearch-server

# Bandwidth testing
iperf3 -c target-server

# DNS resolution
nslookup database-server
dig database-server
```

**2. Optimize Connection Settings:**
```properties
# HTTP client timeouts
spring.datasource.hikari.connection-timeout=30000
spring.redis.timeout=2000ms

# Keep-alive settings
server.tomcat.keep-alive-timeout=60000
server.tomcat.max-keep-alive-requests=100
```

**3. Use Connection Pooling:**
```java
// HTTP client with connection pooling
@Bean
public RestTemplate restTemplate() {
    HttpComponentsClientHttpRequestFactory factory = 
        new HttpComponentsClientHttpRequestFactory();
    factory.setConnectTimeout(5000);
    factory.setReadTimeout(10000);
    
    CloseableHttpClient httpClient = HttpClients.custom()
        .setMaxConnTotal(100)
        .setMaxConnPerRoute(20)
        .build();
    factory.setHttpClient(httpClient);
    
    return new RestTemplate(factory);
}
```

## Optimization Strategies

### Application-Level Optimizations

**1. Enable Compression:**
```properties
# GZIP compression
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain
server.compression.min-response-size=1024
```

**2. Optimize JSON Serialization:**
```java
// Use efficient JSON processing
@JsonIgnore
private byte[] fileContent;  // Exclude large fields from JSON

@JsonView(Views.Summary.class)
private String description;  // Use views for different response levels
```

**3. Implement Pagination:**
```java
// Always use pagination for large datasets
@GetMapping("/documents")
public Page<Document> getDocuments(
    @PageableDefault(size = 20, sort = "createdDate", direction = Sort.Direction.DESC) 
    Pageable pageable) {
    return documentService.findAll(pageable);
}
```

### Infrastructure Optimizations

**1. Load Balancing:**
```yaml
# nginx load balancer configuration
upstream dms_backend {
    server dms-app1:9092 weight=3;
    server dms-app2:9092 weight=3;
    server dms-app3:9092 weight=2;
}
```

**2. CDN for Static Content:**
```properties
# Serve static content from CDN
spring.web.resources.static-locations=https://cdn.example.com/static/
spring.web.resources.cache.cachecontrol.max-age=31536000
```

**3. Database Read Replicas:**
```properties
# Read/write splitting
spring.datasource.write.url=**********************************
spring.datasource.read.url=***********************************
```

---

**Last Updated**: December 2024  
**Version**: 1.0.0

For complex performance issues, consider engaging a performance specialist or using professional profiling tools.
