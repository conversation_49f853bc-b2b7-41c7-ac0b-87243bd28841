package com.ascentbusiness.dms_svc.config;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Tracer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for OpenTelemetry tracing
 */
@Configuration
public class TracingConfiguration {

    @Value("${spring.application.name:dms-svc}")
    private String applicationName;

    /**
     * Provides a Tracer bean for manual instrumentation
     */
    @Bean
    public Tracer tracer(OpenTelemetry openTelemetry) {
        return openTelemetry.getTracer(applicationName);
    }
}
