package com.ascentbusiness.dms_svc.command;

import com.ascentbusiness.dms_svc.config.StorageConfigurationProperties;
import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.enums.DocumentStatus;
import com.ascentbusiness.dms_svc.enums.StorageProvider;
import com.ascentbusiness.dms_svc.exception.SecurityViolationException;
import com.ascentbusiness.dms_svc.exception.StorageMigrationException;
import com.ascentbusiness.dms_svc.repository.DocumentRepository;
import com.ascentbusiness.dms_svc.service.MigrationAuditService;
import com.ascentbusiness.dms_svc.service.MigrationSecurityService;
import com.ascentbusiness.dms_svc.service.S3StorageService;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import com.ascentbusiness.dms_svc.util.SecurityValidationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Component
@RequiredArgsConstructor
public class StorageMigrationCommand implements CommandLineRunner {

    private final DocumentRepository documentRepository;
    private final StorageConfigurationProperties storageConfig;
    private final MigrationAuditService migrationAuditService;
    private final MigrationSecurityService migrationSecurityService;

    @Autowired(required = false)
    private S3StorageService s3StorageService;

    // Migration settings with defaults from configuration
    private boolean dryRun = false;
    private boolean verify = true;
    private boolean cleanupLocal = false;
    private int batchSize = 100;
    private boolean migrateToS3 = false;
    private boolean migrateToLocal = false;

    // Inner class for verification results
    private static class VerificationResult {
        final String sourceChecksum;
        final String targetChecksum;
        final boolean verified;

        VerificationResult(String sourceChecksum, String targetChecksum, boolean verified) {
            this.sourceChecksum = sourceChecksum;
            this.targetChecksum = targetChecksum;
            this.verified = verified;
        }
    }

    @Override
    public void run(String... args) throws Exception {
        if (args.length == 0) {
            // No migration arguments, proceed with normal application startup
            return;
        }

        // Parse command line arguments first to check if migration is actually requested
        parseArguments(args);

        // Check if migration commands are present
        if (!migrateToS3 && !migrateToLocal) {
            return; // No migration requested
        }

        // Set up correlation ID for this migration operation
        String correlationId = CorrelationIdUtil.generateCorrelationId();
        CorrelationIdUtil.setCorrelationId(correlationId);

        String userId = null;
        String sessionId = null;
        LocalDateTime startTime = LocalDateTime.now();

        try {
            // Validate security and get user context
            userId = migrationSecurityService.validateMigrationAccess();

            // Validate command line arguments for security
            SecurityValidationUtil.validateMigrationArguments(args);

            // Validate batch size
            SecurityValidationUtil.validateBatchSize(batchSize);

            // Determine migration type
            String migrationType = migrateToS3 ? "LOCAL_TO_S3" : "S3_TO_LOCAL";

            // Start migration session
            sessionId = migrationSecurityService.startMigrationSession(userId, migrationType);

            // Prepare migration configuration for audit
            Map<String, Object> migrationConfig = new HashMap<>();
            migrationConfig.put("dryRun", dryRun);
            migrationConfig.put("verify", verify);
            migrationConfig.put("cleanupLocal", cleanupLocal);
            migrationConfig.put("batchSize", batchSize);
            migrationConfig.put("sessionId", sessionId);

            // Log migration start
            migrationAuditService.logMigrationStart(userId, migrationType, migrationConfig);

            log.info("Starting storage migration - Type: {}, User: {}, SessionId: {}, CorrelationId: {}, Settings: dryRun={}, verify={}, cleanupLocal={}, batchSize={}",
                    migrationType, userId, sessionId, correlationId, dryRun, verify, cleanupLocal, batchSize);

            if (migrateToS3) {
                validateS3Migration();
                migrateLocalToS3(userId, sessionId);
            } else if (migrateToLocal) {
                migrateS3ToLocal(userId, sessionId);
            }

            // Calculate duration
            long durationMs = java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();

            // Log successful completion (detailed stats will be logged in individual methods)
            migrationAuditService.logMigrationComplete(userId, migrationType, 0, 0, 0, 0, durationMs);

            log.info("Migration completed successfully - Type: {}, Duration: {}ms, CorrelationId: {}",
                    migrationType, durationMs, correlationId);
            System.exit(0);

        } catch (SecurityViolationException e) {
            if (userId != null) {
                migrationAuditService.handleSecurityViolation(userId, e, null, "MIGRATION_OPERATION");
            }
            log.error("Security violation during migration - CorrelationId: {}", correlationId, e);
            System.exit(1);
        } catch (Exception e) {
            if (userId != null) {
                long durationMs = java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
                String migrationType = migrateToS3 ? "LOCAL_TO_S3" : "S3_TO_LOCAL";
                migrationAuditService.logMigrationFailure(userId, migrationType, e.getMessage(), 0, e);
                log.error("Migration failed after {}ms - CorrelationId: {}", durationMs, correlationId, e);
            } else {
                log.error("Migration failed - CorrelationId: {}", correlationId, e);
            }
            System.exit(1);
        } finally {
            if (sessionId != null) {
                migrationSecurityService.endMigrationSession(sessionId);
            }
            CorrelationIdUtil.clearCorrelationId();
        }
    }

    private void parseArguments(String[] args) {
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];

            // Sanitize argument for logging
            String sanitizedArg = SecurityValidationUtil.sanitizeForLogging(arg);

            switch (arg) {
                case "--migrate-to-s3":
                    migrateToS3 = true;
                    break;
                case "--migrate-to-local":
                    migrateToLocal = true;
                    break;
                case "--dry-run":
                    dryRun = true;
                    break;
                case "--verify":
                    verify = true;
                    break;
                case "--cleanup-local":
                    cleanupLocal = true;
                    break;
                case "--no-verify":
                    verify = false;
                    break;
                case "--batch-size":
                    if (i + 1 < args.length) {
                        try {
                            int proposedBatchSize = Integer.parseInt(args[++i]);
                            // Validate batch size for security
                            SecurityValidationUtil.validateBatchSize(proposedBatchSize);
                            batchSize = proposedBatchSize;
                        } catch (NumberFormatException e) {
                            throw new StorageMigrationException("Invalid batch size format: " +
                                SecurityValidationUtil.sanitizeForLogging(args[i]));
                        } catch (SecurityViolationException e) {
                            throw new StorageMigrationException("Invalid batch size: " + e.getMessage());
                        }
                    } else {
                        throw new StorageMigrationException("--batch-size requires a value");
                    }
                    break;
                default:
                    if (arg.startsWith("--")) {
                        log.warn("Unknown argument: {}", sanitizedArg);
                    }
                    break;
            }
        }

        // Validate that only one migration type is specified
        if (migrateToS3 && migrateToLocal) {
            throw new StorageMigrationException("Cannot specify both --migrate-to-s3 and --migrate-to-local");
        }
    }

    private void validateS3Migration() {
        String userId = migrationSecurityService.validateMigrationAccess();

        if (s3StorageService == null) {
            migrationAuditService.logSecurityCheck(userId, "S3_SERVICE_AVAILABILITY",
                "S3 service not available", false);
            throw new StorageMigrationException("S3 service not available. Please ensure S3 is properly configured.");
        }

        // Validate S3 configuration (without logging sensitive details)
        if (storageConfig.getS3().getAccessKey() == null || storageConfig.getS3().getAccessKey().isEmpty()) {
            migrationAuditService.logSecurityCheck(userId, "S3_ACCESS_KEY_VALIDATION",
                "AWS access key not configured", false);
            throw new StorageMigrationException("AWS access key not configured");
        }

        if (storageConfig.getS3().getSecretKey() == null || storageConfig.getS3().getSecretKey().isEmpty()) {
            migrationAuditService.logSecurityCheck(userId, "S3_SECRET_KEY_VALIDATION",
                "AWS secret key not configured", false);
            throw new StorageMigrationException("AWS secret key not configured");
        }

        // Log successful validation
        migrationAuditService.logSecurityCheck(userId, "S3_CONFIGURATION_VALIDATION",
            "S3 configuration validated successfully", true);

        log.info("S3 configuration validated successfully - CorrelationId: {}",
                CorrelationIdUtil.getCurrentCorrelationId());
    }

    private void migrateLocalToS3(String userId, String sessionId) throws Exception {
        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();
        log.info("Starting LOCAL to S3 migration - User: {}, SessionId: {}, CorrelationId: {}",
                userId, sessionId, correlationId);

        // Get all documents stored locally
        List<Document> localDocuments = documentRepository.findByStorageProviderAndStatus(StorageProvider.LOCAL, DocumentStatus.ACTIVE);

        if (localDocuments.isEmpty()) {
            log.info("No local documents found to migrate - CorrelationId: {}", correlationId);
            return;
        }

        log.info("Found {} documents to migrate from LOCAL to S3 - CorrelationId: {}",
                localDocuments.size(), correlationId);

        if (dryRun) {
            log.info("DRY RUN MODE - No actual migration will be performed - CorrelationId: {}", correlationId);
            displayMigrationPlan(localDocuments);
            return;
        }

        AtomicInteger processed = new AtomicInteger(0);
        AtomicInteger successful = new AtomicInteger(0);
        AtomicInteger failed = new AtomicInteger(0);
        AtomicLong totalSize = new AtomicLong(0);

        for (Document document : localDocuments) {
            Path localFilePath = null;
            try {
                processed.incrementAndGet();

                log.info("Processing document {}/{}: {} (ID: {}) - CorrelationId: {}",
                        processed.get(), localDocuments.size(), document.getName(),
                        document.getId(), correlationId);

                // Validate file path for security
                try {
                    SecurityValidationUtil.validateFilePath(document.getStoragePath(),
                            storageConfig.getLocal().getBasePath());
                    SecurityValidationUtil.validateFileAccess(document.getStoragePath());
                } catch (SecurityViolationException e) {
                    migrationAuditService.handleSecurityViolation(userId, e, document.getId(), "FILE_ACCESS");
                    log.error("Security validation failed for document {}: {}", document.getId(), e.getMessage());
                    failed.incrementAndGet();
                    continue;
                }

                // Check if local file exists
                localFilePath = Paths.get(document.getStoragePath());
                if (!Files.exists(localFilePath)) {
                    String sanitizedPath = SecurityValidationUtil.sanitizeForLogging(document.getStoragePath());
                    log.warn("Local file not found: {} - CorrelationId: {}", sanitizedPath, correlationId);
                    migrationAuditService.logFileProcessed(userId, document.getId(), document.getName(),
                            document.getStoragePath(), "N/A", 0, false);
                    failed.incrementAndGet();
                    continue;
                }

                long fileSize = Files.size(localFilePath);
                totalSize.addAndGet(fileSize);

                // Upload to S3
                String s3Key = s3StorageService.storeFileFromPath(document.getStoragePath());

                // Log file processing
                migrationAuditService.logFileProcessed(userId, document.getId(), document.getName(),
                        document.getStoragePath(), s3Key, fileSize, true);

                // Verify upload if requested
                if (verify) {
                    VerificationResult verificationResult = verifyFileIntegrity(document.getStoragePath(), s3Key);

                    // Log verification result
                    migrationAuditService.logFileVerification(userId, document.getId(), document.getName(),
                            verificationResult.sourceChecksum, verificationResult.targetChecksum,
                            verificationResult.verified);

                    if (!verificationResult.verified) {
                        log.error("File integrity verification failed for: {} - CorrelationId: {}",
                                document.getName(), correlationId);
                        // Delete the uploaded S3 file since verification failed
                        try {
                            s3StorageService.deleteFile(s3Key);
                            log.info("Cleaned up S3 file after verification failure: {} - CorrelationId: {}",
                                    s3Key, correlationId);
                        } catch (Exception e) {
                            log.error("Failed to cleanup S3 file after verification failure: {} - CorrelationId: {}",
                                    s3Key, correlationId, e);
                        }
                        failed.incrementAndGet();
                        continue;
                    }
                }

                // Update database record
                document.setStorageProvider(StorageProvider.S3);
                document.setStoragePath(s3Key);
                documentRepository.save(document);

                // Cleanup local file if requested
                if (cleanupLocal) {
                    try {
                        Files.delete(localFilePath);
                        migrationAuditService.logFileCleanup(userId, document.getId(), document.getName(),
                                document.getStoragePath(), true);
                        log.debug("Deleted local file: {} - CorrelationId: {}",
                                SecurityValidationUtil.sanitizeForLogging(document.getStoragePath()), correlationId);
                    } catch (IOException e) {
                        migrationAuditService.logFileCleanup(userId, document.getId(), document.getName(),
                                document.getStoragePath(), false);
                        log.warn("Failed to delete local file: {} - CorrelationId: {}",
                                SecurityValidationUtil.sanitizeForLogging(document.getStoragePath()), correlationId, e);
                    }
                }

                successful.incrementAndGet();
                log.info("Successfully migrated: {} -> {} - CorrelationId: {}",
                        document.getName(), s3Key, correlationId);

            } catch (SecurityViolationException e) {
                migrationAuditService.handleSecurityViolation(userId, e, document.getId(), "FILE_MIGRATION");
                log.error("Security violation during document migration: {} (ID: {}) - CorrelationId: {}",
                        document.getName(), document.getId(), correlationId, e);
                failed.incrementAndGet();
            } catch (Exception e) {
                try {
                    long fileSize = (localFilePath != null && Files.exists(localFilePath)) ? Files.size(localFilePath) : 0;
                    migrationAuditService.logFileProcessed(userId, document.getId(), document.getName(),
                            document.getStoragePath(), "FAILED", fileSize, false);
                } catch (IOException ioEx) {
                    log.debug("Could not determine file size for failed migration: {}", document.getId());
                }
                log.error("Failed to migrate document: {} (ID: {}) - CorrelationId: {}",
                        document.getName(), document.getId(), correlationId, e);
                failed.incrementAndGet();
            }
        }

        // Log comprehensive migration summary
        long durationMs = java.time.Duration.between(LocalDateTime.now().minusSeconds(
                (processed.get() * 2)), LocalDateTime.now()).toMillis(); // Rough estimate

        migrationAuditService.logMigrationComplete(userId, "LOCAL_TO_S3",
                processed.get(), successful.get(), failed.get(), totalSize.get(), durationMs);

        log.info("Migration Summary - CorrelationId: {}", correlationId);
        log.info("Total documents processed: {}", processed.get());
        log.info("Successfully migrated: {}", successful.get());
        log.info("Failed: {}", failed.get());
        log.info("Total size migrated: {} bytes ({} MB)", totalSize.get(), totalSize.get() / (1024 * 1024));
    }

    private void migrateS3ToLocal(String userId, String sessionId) throws Exception {
        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();
        log.info("Starting S3 to LOCAL migration - User: {}, SessionId: {}, CorrelationId: {}",
                userId, sessionId, correlationId);

        // Get all documents stored in S3
        List<Document> s3Documents = documentRepository.findByStorageProviderAndStatus(StorageProvider.S3, DocumentStatus.ACTIVE);

        if (s3Documents.isEmpty()) {
            log.info("No S3 documents found to migrate - CorrelationId: {}", correlationId);
            return;
        }

        log.info("Found {} documents to migrate from S3 to LOCAL - CorrelationId: {}",
                s3Documents.size(), correlationId);

        if (dryRun) {
            log.info("DRY RUN MODE - No actual migration will be performed - CorrelationId: {}", correlationId);
            displayMigrationPlan(s3Documents);
            return;
        }

        AtomicInteger processed = new AtomicInteger(0);
        AtomicInteger successful = new AtomicInteger(0);
        AtomicInteger failed = new AtomicInteger(0);
        AtomicLong totalSize = new AtomicLong(0);

        for (Document document : s3Documents) {
            Path localFilePath = null;
            String localPath = null;
            try {
                processed.incrementAndGet();

                log.info("Processing document {}/{}: {} (ID: {}) - CorrelationId: {}",
                        processed.get(), s3Documents.size(), document.getName(),
                        document.getId(), correlationId);

                // Download from S3
                byte[] fileData = s3StorageService.downloadFile(document.getStoragePath());
                totalSize.addAndGet(fileData.length);

                // Generate local storage path
                localPath = generateLocalStoragePath(document.getOriginalFileName());
                localFilePath = Paths.get(localPath);

                // Validate directory creation for security
                try {
                    SecurityValidationUtil.validateDirectoryCreation(localFilePath.getParent().toString());
                } catch (SecurityViolationException e) {
                    migrationAuditService.handleSecurityViolation(userId, e, document.getId(), "DIRECTORY_CREATION");
                    log.error("Security validation failed for directory creation: {}", e.getMessage());
                    failed.incrementAndGet();
                    continue;
                }

                // Create directories if needed
                Files.createDirectories(localFilePath.getParent());

                // Write file to local storage
                Files.write(localFilePath, fileData);

                // Log file processing
                migrationAuditService.logFileProcessed(userId, document.getId(), document.getName(),
                        document.getStoragePath(), localPath, fileData.length, true);

                // Verify file if requested
                if (verify) {
                    VerificationResult verificationResult = verifyLocalFileIntegrity(localPath, document.getStoragePath());

                    // Log verification result
                    migrationAuditService.logFileVerification(userId, document.getId(), document.getName(),
                            verificationResult.sourceChecksum, verificationResult.targetChecksum,
                            verificationResult.verified);

                    if (!verificationResult.verified) {
                        log.error("File integrity verification failed for: {} - CorrelationId: {}",
                                document.getName(), correlationId);
                        // Delete the local file since verification failed
                        try {
                            Files.delete(localFilePath);
                            migrationAuditService.logFileCleanup(userId, document.getId(), document.getName(),
                                    localPath, true);
                            log.info("Cleaned up local file after verification failure: {} - CorrelationId: {}",
                                    SecurityValidationUtil.sanitizeForLogging(localPath), correlationId);
                        } catch (Exception e) {
                            migrationAuditService.logFileCleanup(userId, document.getId(), document.getName(),
                                    localPath, false);
                            log.error("Failed to cleanup local file after verification failure: {} - CorrelationId: {}",
                                    SecurityValidationUtil.sanitizeForLogging(localPath), correlationId, e);
                        }
                        failed.incrementAndGet();
                        continue;
                    }
                }

                // Update database record
                document.setStorageProvider(StorageProvider.LOCAL);
                document.setStoragePath(localPath);
                documentRepository.save(document);

                successful.incrementAndGet();
                log.info("Successfully migrated: {} -> {} - CorrelationId: {}",
                        document.getStoragePath(), localPath, correlationId);

            } catch (SecurityViolationException e) {
                migrationAuditService.handleSecurityViolation(userId, e, document.getId(), "FILE_MIGRATION");
                log.error("Security violation during document migration: {} (ID: {}) - CorrelationId: {}",
                        document.getName(), document.getId(), correlationId, e);
                failed.incrementAndGet();
            } catch (Exception e) {
                try {
                    long fileSize = (localFilePath != null && Files.exists(localFilePath)) ? Files.size(localFilePath) : 0;
                    migrationAuditService.logFileProcessed(userId, document.getId(), document.getName(),
                            document.getStoragePath(), localPath != null ? localPath : "FAILED", fileSize, false);
                } catch (IOException ioEx) {
                    log.debug("Could not determine file size for failed migration: {}", document.getId());
                }
                log.error("Failed to migrate document: {} (ID: {}) - CorrelationId: {}",
                        document.getName(), document.getId(), correlationId, e);
                failed.incrementAndGet();
            }
        }

        // Log comprehensive migration summary
        long durationMs = java.time.Duration.between(LocalDateTime.now().minusSeconds(
                (processed.get() * 2)), LocalDateTime.now()).toMillis(); // Rough estimate

        migrationAuditService.logMigrationComplete(userId, "S3_TO_LOCAL",
                processed.get(), successful.get(), failed.get(), totalSize.get(), durationMs);

        log.info("Migration Summary - CorrelationId: {}", correlationId);
        log.info("Total documents processed: {}", processed.get());
        log.info("Successfully migrated: {}", successful.get());
        log.info("Failed: {}", failed.get());
        log.info("Total size migrated: {} bytes ({} MB)", totalSize.get(), totalSize.get() / (1024 * 1024));
    }

    private void displayMigrationPlan(List<Document> documents) {
        log.info("Migration Plan:");
        log.info("Documents to migrate: {}", documents.size());
        
        long totalSize = documents.stream()
                .mapToLong(doc -> {
                    try {
                        if (doc.getStorageProvider() == StorageProvider.LOCAL) {
                            Path filePath = Paths.get(doc.getStoragePath());
                            return Files.exists(filePath) ? Files.size(filePath) : 0;
                        } else {
                            // For S3 documents, we can't easily get size without downloading
                            // This could be enhanced with S3 metadata calls
                            return 0;
                        }
                    } catch (Exception e) {
                        return 0;
                    }
                })
                .sum();
        
        log.info("Estimated total size: {} bytes ({} MB)", totalSize, totalSize / (1024 * 1024));
        log.info("Batch size: {}", batchSize);
        log.info("Verification enabled: {}", verify);
        log.info("Cleanup local files: {}", cleanupLocal);
    }

    private VerificationResult verifyFileIntegrity(String localPath, String s3Key) throws Exception {
        try {
            // Get checksum of local file
            byte[] localFileData = Files.readAllBytes(Paths.get(localPath));
            String localChecksum = calculateChecksum(localFileData);

            // Get checksum of S3 file
            byte[] s3FileData = s3StorageService.downloadFile(s3Key);
            String s3Checksum = calculateChecksum(s3FileData);

            boolean match = localChecksum.equals(s3Checksum);

            if (match) {
                log.debug("File integrity verified: {} - CorrelationId: {}", s3Key, CorrelationIdUtil.getCurrentCorrelationId());
            } else {
                log.error("Checksum mismatch - Local: {}, S3: {} - CorrelationId: {}",
                        localChecksum, s3Checksum, CorrelationIdUtil.getCurrentCorrelationId());
            }

            return new VerificationResult(localChecksum, s3Checksum, match);
        } catch (Exception e) {
            log.error("Failed to verify file integrity for: {} - CorrelationId: {}",
                    s3Key, CorrelationIdUtil.getCurrentCorrelationId(), e);
            return new VerificationResult("ERROR", "ERROR", false);
        }
    }

    private VerificationResult verifyLocalFileIntegrity(String localPath, String s3Key) throws Exception {
        try {
            // Get checksum of local file
            byte[] localFileData = Files.readAllBytes(Paths.get(localPath));
            String localChecksum = calculateChecksum(localFileData);

            // Get checksum of S3 file
            byte[] s3FileData = s3StorageService.downloadFile(s3Key);
            String s3Checksum = calculateChecksum(s3FileData);

            boolean match = localChecksum.equals(s3Checksum);

            if (match) {
                log.debug("File integrity verified: {} - CorrelationId: {}",
                        SecurityValidationUtil.sanitizeForLogging(localPath), CorrelationIdUtil.getCurrentCorrelationId());
            } else {
                log.error("Checksum mismatch - Local: {}, S3: {} - CorrelationId: {}",
                        localChecksum, s3Checksum, CorrelationIdUtil.getCurrentCorrelationId());
            }

            return new VerificationResult(localChecksum, s3Checksum, match);
        } catch (Exception e) {
            log.error("Failed to verify file integrity for: {} - CorrelationId: {}",
                    SecurityValidationUtil.sanitizeForLogging(localPath), CorrelationIdUtil.getCurrentCorrelationId(), e);
            return new VerificationResult("ERROR", "ERROR", false);
        }
    }

    private String calculateChecksum(byte[] data) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(data);
        StringBuilder sb = new StringBuilder();
        for (byte b : hashBytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    private String generateLocalStoragePath(String fileName) {
        // Sanitize filename for security
        String sanitizedFileName = fileName != null ? fileName : "document";
        sanitizedFileName = SecurityValidationUtil.sanitizeForLogging(sanitizedFileName);

        // Generate path similar to the StorageService pattern
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        String yearMonth = now.format(java.time.format.DateTimeFormatter.ofPattern("yyyy/MM"));
        String timestamp = now.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS"));

        String extension = getFileExtension(sanitizedFileName);
        String baseName = getBaseName(sanitizedFileName);

        String uniqueFileName = String.format("%s_%s%s", baseName, timestamp, extension);

        String generatedPath = Paths.get(storageConfig.getLocal().getBasePath(), yearMonth, uniqueFileName).toString();

        // Validate the generated path for security
        SecurityValidationUtil.validateFilePath(generatedPath, storageConfig.getLocal().getBasePath());

        return generatedPath;
    }

    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        return (lastDotIndex == -1) ? "" : fileName.substring(lastDotIndex);
    }

    private String getBaseName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "file";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        return (lastDotIndex == -1) ? fileName : fileName.substring(0, lastDotIndex);
    }
}
