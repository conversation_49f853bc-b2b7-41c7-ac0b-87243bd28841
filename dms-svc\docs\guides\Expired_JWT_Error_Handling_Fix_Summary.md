# Expired JWT Token Error Handling Fix Summary

## Problem Identified
When providing an expired JWT token in GraphQL requests, the system was returning a generic `FORBIDDEN` error instead of the specific `INVALID_TOKEN` error with the message "JWT token has expired".

**Before Fix - Current Behavior:**
```json
{
  "errors": [
    {
      "message": "Authentication required. Please provide a valid JWT token.",
      "extensions": {
        "code": "FORBIDDEN",
        "type": "AUTHORIZATION_ERROR",
        "operation": "getDocumentById"
      }
    }
  ],
  "data": {"getDocumentById": null}
}
```

**Expected Behavior:**
```json
{
  "errors": [
    {
      "message": "JWT token has expired",
      "extensions": {
        "code": "INVALID_TOKEN",
        "type": "AUTHENTICATION_ERROR",
        "operation": "getDocumentById"
      }
    }
  ],
  "data": {"getDocumentById": null}
}
```

## Root Cause Analysis

### The Problem Flow:
1. **Expired JWT token provided** → Header: `Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************.FTDPiUWnR-xiT1HKezPrLijPzAs8kkSxkl0pod_jT78`
2. **JWT Authentication Filter** → `JwtAuthenticationFilter.doFilterInternal()`
3. **Token validation called** → `tokenProvider.validateToken(jwt)`
4. **TokenProvider throws** → `InvalidTokenException("JWT token has expired")`
5. **Filter catches exception** → `catch (Exception ex)` - silently logs and continues
6. **No authentication set** → SecurityContext remains empty
7. **GraphQL resolver** → `checkAuthentication()` sees no authentication
8. **Resolver throws** → `UnauthorizedException("Authentication required...")`
9. **GraphQL handler** → Returns `FORBIDDEN` code instead of `INVALID_TOKEN`

### The Issue:
The `JwtAuthenticationFilter` was catching the specific `InvalidTokenException` in a generic exception handler and continuing the request flow without propagating the JWT validation error to the GraphQL layer.

## Solution Implemented

### Task 1: Enhanced JWT Authentication Filter Exception Handling
**File:** `src/main/java/com/ascentbusiness/dms_svc/security/JwtAuthenticationFilter.java`

**Changes Made:**
1. **Added import** for `InvalidTokenException`
2. **Updated exception handling** in `doFilterInternal()` method:
   - Added specific catch block for `InvalidTokenException`
   - Store the JWT validation error in request attributes
   - Allow the request to continue so GraphQL layer can access the error

**Code Changes:**
```java
// Before:
} catch (Exception ex) {
    logger.error("Could not set user authentication in security context", ex);
}

// After:
} catch (InvalidTokenException ex) {
    // Re-throw InvalidTokenException to allow proper error handling at GraphQL layer
    logger.warn("JWT token validation failed: {}", ex.getMessage());
    // Store the exception in request attribute so it can be accessed by GraphQL resolver
    request.setAttribute("JWT_VALIDATION_ERROR", ex);
} catch (Exception ex) {
    logger.error("Could not set user authentication in security context", ex);
}
```

### Task 2: Enhanced DocumentResolver Authentication Check
**File:** `src/main/java/com/ascentbusiness/dms_svc/resolver/DocumentResolver.java`

**Changes Made:**
1. **Added imports** for JWT error handling classes and HTTP request access
2. **Updated checkAuthentication() method** to:
   - First check for JWT validation errors stored in request attributes
   - Re-throw the specific `InvalidTokenException` if found
   - Fall back to normal authentication checks if no JWT errors

**Code Changes:**
```java
// Enhanced checkAuthentication() method:
private void checkAuthentication() {
    // First check if there's a JWT validation error stored in request attributes
    ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    if (requestAttributes != null) {
        HttpServletRequest request = requestAttributes.getRequest();
        InvalidTokenException jwtError = (InvalidTokenException) request.getAttribute("JWT_VALIDATION_ERROR");
        if (jwtError != null) {
            logger.warn("JWT validation error detected: {}", jwtError.getMessage());
            throw jwtError; // Re-throw the specific JWT validation error
        }
    }
    
    // Then check normal authentication
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication == null || !authentication.isAuthenticated() || 
        authentication.getPrincipal().equals("anonymousUser")) {
        logger.warn("Unauthenticated access attempt to GraphQL operation");
        throw new UnauthorizedException("Authentication required. Please provide a valid JWT token.");
    }
}
```

### Task 3: Validation
**Compilation Test:** ✅ PASSED
- All changes compile successfully
- No syntax or dependency errors
- Ready for runtime testing

## Expected Behavior After Fix

### Scenario 1: Expired JWT Token
**Input:** `Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************.FTDPiUWnR-xiT1HKezPrLijPzAs8kkSxkl0pod_jT78`

**Expected Output:**
```json
{
  "errors": [
    {
      "message": "JWT token has expired",
      "extensions": {
        "code": "INVALID_TOKEN",
        "type": "AUTHENTICATION_ERROR",
        "operation": "getDocumentById"
      }
    }
  ],
  "data": {"getDocumentById": null}
}
```

### Scenario 2: Malformed JWT Token
**Input:** `Authorization: Bearer asdsadsadada`

**Expected Output:**
```json
{
  "errors": [
    {
      "message": "Invalid or malformed JWT token",
      "extensions": {
        "code": "INVALID_TOKEN",
        "type": "AUTHENTICATION_ERROR",
        "operation": "getDocumentById"
      }
    }
  ],
  "data": {"getDocumentById": null}
}
```

### Scenario 3: No Authorization Header
**Input:** (no Authorization header)

**Expected Output:**
```json
{
  "errors": [
    {
      "message": "Authentication required. Please provide a valid JWT token.",
      "extensions": {
        "code": "FORBIDDEN",
        "type": "AUTHORIZATION_ERROR",
        "operation": "getDocumentById"
      }
    }
  ],
  "data": {"getDocumentById": null}
}
```

## Technical Details

### Flow After Fix:
1. **Expired JWT token** → `JwtAuthenticationFilter.doFilterInternal()`
2. **Filter calls** → `tokenProvider.validateToken(jwt)`
3. **TokenProvider throws** → `InvalidTokenException("JWT token has expired")`
4. **Filter catches specifically** → `catch (InvalidTokenException ex)`
5. **Error stored** → `request.setAttribute("JWT_VALIDATION_ERROR", ex)`
6. **Request continues** → Filter chain proceeds
7. **GraphQL resolver** → `checkAuthentication()` checks request attributes
8. **Error detected** → Finds `InvalidTokenException` in request attributes
9. **Error re-thrown** → Throws the specific JWT validation error
10. **GraphQL handler** → Returns proper `INVALID_TOKEN` error code

### Key Benefits:
- **Specific error messages** for different JWT validation failures
- **Proper error codes** (`INVALID_TOKEN` vs `FORBIDDEN`)
- **Maintains existing functionality** for other authentication scenarios
- **Clean separation** between JWT validation errors and authorization errors

## Files Modified
1. `src/main/java/com/ascentbusiness/dms_svc/security/JwtAuthenticationFilter.java`
2. `src/main/java/com/ascentbusiness/dms_svc/resolver/DocumentResolver.java`

## Testing Recommendations
1. Test with expired JWT tokens
2. Test with malformed JWT tokens  
3. Test with no authorization header
4. Test with valid JWT tokens (ensure no regression)
5. Verify all GraphQL operations that use `checkAuthentication()`

## Status
✅ **Implementation Complete** - Ready for testing
