# Document Download API Documentation

## Overview

This document describes the proper way to download documents from the DMS service. The previous GraphQL `downloadDocument` query had issues with file content delivery, so we've implemented dedicated REST API endpoints for reliable file downloads.

## Problem Summary

The original GraphQL `downloadDocument` query was only returning document metadata instead of actual file content, causing downloaded files to appear empty when opened.

**Root Cause:**
- GraphQL resolver called `documentService.getDocumentById()` (metadata only)
- Should have called `documentService.downloadDocument()` (actual file bytes)
- GraphQL is not ideal for binary file transfers

## Solution: REST API Endpoints

We've implemented versioned REST API endpoints specifically designed for file downloads with proper HTTP headers and streaming support.

## API Endpoints

### V1 API - Basic Download

#### Download Document
```http
GET /api/v1/documents/{id}/download
```

**Description:** Basic document download with proper file headers.

**Parameters:**
- `id` (path): Document ID (Long)

**Response:**
- **Content-Type:** Document's MIME type
- **Content-Disposition:** `attachment; filename="document.pdf"`
- **Content-Length:** File size in bytes
- **Custom Headers:**
  - `X-Document-ID`: Document ID
  - `X-Document-Version`: Document version
  - `X-API-Version`: "1.0"

**Example:**
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -o downloaded_file.pdf \
     "http://localhost:8080/api/v1/documents/123/download"
```

### V2 API - Enhanced Download (Recommended)

#### Download Document with Streaming
```http
GET /api/v2/documents/{id}/download?inline=false
```

**Description:** Enhanced download with better streaming support and comprehensive metadata headers.

**Parameters:**
- `id` (path): Document ID (Long)
- `inline` (query, optional): Display inline instead of download (default: false)

**Response:**
- **Content-Type:** Document's MIME type
- **Content-Disposition:** `attachment` or `inline` based on parameter
- **Content-Length:** File size in bytes
- **Cache-Control:** `private, max-age=3600`
- **ETag:** Document version-based ETag
- **Custom Headers:**
  - `X-Document-ID`: Document ID
  - `X-Document-Name`: Document name
  - `X-Document-Version`: Document version
  - `X-Document-Status`: Document status
  - `X-Document-Creator`: Creator user ID
  - `X-Document-Created`: Creation timestamp
  - `X-Document-Modified`: Last modified timestamp
  - `X-Storage-Provider`: Storage provider type
  - `X-API-Version`: "2.0"

**Examples:**

Download as attachment:
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -o downloaded_file.pdf \
     "http://localhost:8080/api/v2/documents/123/download"
```

Display inline (for PDFs, images):
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost:8080/api/v2/documents/123/download?inline=true"
```

#### Get Download Metadata
```http
GET /api/v2/documents/{id}/download/info
```

**Description:** Get document download information without transferring file content.

**Parameters:**
- `id` (path): Document ID (Long)

**Response:** JSON object with download metadata
```json
{
  "documentId": 123,
  "name": "Financial Report Q4",
  "originalFileName": "financial_report_q4.pdf",
  "fileSize": 2048576,
  "mimeType": "application/pdf",
  "version": 2,
  "status": "ACTIVE",
  "storageProvider": "S3",
  "createdBy": "john.doe",
  "createdDate": "2024-01-15T10:30:00Z",
  "lastModifiedDate": "2024-01-20T14:45:00Z",
  "downloadUrl": "/api/v2/documents/123/download",
  "inlineUrl": "/api/v2/documents/123/download?inline=true",
  "apiVersion": "2.0",
  "timestamp": "2024-01-25T09:15:30Z"
}
```

**Example:**
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost:8080/api/v2/documents/123/download/info"
```

## Authentication & Authorization

All endpoints require:
- **Authentication:** Valid JWT token in `Authorization: Bearer <token>` header
- **Authorization:** User must have READ permission for the document
- **Roles:** USER, ADMIN, or MANAGER role required

## Rate Limiting

- **Download endpoints:** 30 requests per minute
- **Info endpoint:** 60 requests per minute
- **Bulk download:** 5 requests per 5 minutes

## Error Responses

### 404 Not Found
```json
{
  "error": "DOCUMENT_NOT_FOUND",
  "message": "Document not found with id: 123",
  "timestamp": "2024-01-25T09:15:30Z",
  "status": 404
}
```

### 403 Forbidden
```json
{
  "error": "UNAUTHORIZED",
  "message": "You don't have permission to download this document",
  "timestamp": "2024-01-25T09:15:30Z",
  "status": 403
}
```

### 500 Internal Server Error
```json
{
  "error": "INTERNAL_SERVER_ERROR",
  "message": "An unexpected error occurred during document download",
  "timestamp": "2024-01-25T09:15:30Z",
  "status": 500
}
```

## Migration from GraphQL

### Before (Problematic)
```graphql
query DownloadDocument($id: ID!) {
  downloadDocument(id: $id) {
    id
    name
    originalFileName
    fileSize
    mimeType
    # This was only returning metadata, not file content!
  }
}
```

### After (Recommended)
```javascript
// Use REST API for actual file download
const downloadDocument = async (documentId, token) => {
  const response = await fetch(`/api/v2/documents/${documentId}/download`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (response.ok) {
    const blob = await response.blob();
    const filename = getFilenameFromHeaders(response.headers);
    
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }
};

const getFilenameFromHeaders = (headers) => {
  const contentDisposition = headers.get('content-disposition');
  if (contentDisposition) {
    const match = contentDisposition.match(/filename="(.+)"/);
    return match ? decodeURIComponent(match[1]) : 'document';
  }
  return 'document';
};
```

## Frontend Integration Examples

### React Component
```jsx
import React, { useState } from 'react';

const DocumentDownloader = ({ documentId, token }) => {
  const [downloading, setDownloading] = useState(false);
  const [error, setError] = useState(null);

  const downloadDocument = async () => {
    setDownloading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/v2/documents/${documentId}/download`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Download failed: ${response.statusText}`);
      }
      
      const blob = await response.blob();
      const filename = getFilenameFromHeaders(response.headers);
      
      // Create download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
    } catch (err) {
      setError(err.message);
    } finally {
      setDownloading(false);
    }
  };

  return (
    <div>
      <button onClick={downloadDocument} disabled={downloading}>
        {downloading ? 'Downloading...' : 'Download Document'}
      </button>
      {error && <div className="error">Error: {error}</div>}
    </div>
  );
};
```

### Angular Service
```typescript
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DocumentDownloadService {
  
  constructor(private http: HttpClient) {}
  
  downloadDocument(documentId: number, token: string): Observable<Blob> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });
    
    return this.http.get(`/api/v2/documents/${documentId}/download`, {
      headers,
      responseType: 'blob'
    });
  }
  
  getDownloadInfo(documentId: number, token: string): Observable<any> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });
    
    return this.http.get(`/api/v2/documents/${documentId}/download/info`, {
      headers
    });
  }
}
```

## Testing

### Manual Testing
```bash
# Test download
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -o test_download.pdf \
     "http://localhost:8080/api/v2/documents/1/download"

# Test download info
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost:8080/api/v2/documents/1/download/info"

# Test inline display
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost:8080/api/v2/documents/1/download?inline=true"
```

### Integration Tests
```java
@Test
public void testDocumentDownload() {
    // Test that download returns actual file content
    ResponseEntity<byte[]> response = restTemplate.exchange(
        "/api/v2/documents/1/download",
        HttpMethod.GET,
        new HttpEntity<>(createAuthHeaders()),
        byte[].class
    );
    
    assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    assertThat(response.getBody()).isNotEmpty();
    assertThat(response.getHeaders().getContentType()).isNotNull();
    assertThat(response.getHeaders().getFirst("Content-Disposition")).contains("attachment");
}
```

## Best Practices

1. **Use V2 API:** Always use `/api/v2/` endpoints for new implementations
2. **Handle Errors:** Implement proper error handling for all HTTP status codes
3. **Show Progress:** For large files, consider showing download progress
4. **Validate Content:** Verify downloaded file size matches expected size
5. **Security:** Always validate JWT tokens and user permissions
6. **Caching:** Leverage ETag headers for client-side caching
7. **Rate Limiting:** Respect rate limits to avoid being blocked

## Troubleshooting

### Common Issues

1. **Empty Files:** Ensure you're using REST API, not GraphQL
2. **Permission Denied:** Verify user has READ permission for the document
3. **Rate Limited:** Wait before retrying if you hit rate limits
4. **Large Files:** Use streaming approach for files > 10MB
5. **Special Characters:** Filenames are URL-encoded automatically

### Debug Headers

Check these response headers for debugging:
- `X-Document-ID`: Confirms correct document
- `X-Document-Version`: Confirms document version
- `X-API-Version`: Confirms API version used
- `Content-Length`: Should match expected file size

## Future Enhancements

- **Bulk Download:** ZIP multiple documents (planned)
- **Resume Downloads:** Support for interrupted download resume
- **Streaming:** Chunked transfer encoding for very large files
- **Compression:** Optional gzip compression for text files
- **Watermarking:** Optional watermark injection for sensitive documents

## Support

For issues or questions about the Document Download API:
1. Check this documentation first
2. Review server logs for error details
3. Test with curl to isolate client vs server issues
4. Contact the DMS development team with specific error messages