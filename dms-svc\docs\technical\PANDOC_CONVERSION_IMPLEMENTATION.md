# Pandoc Document Conversion Implementation

## Overview

This document describes the implementation of Pandoc-based document conversion functionality in the DMS system, providing enhanced document conversion capabilities with fallback to existing implementations.

## Features Implemented

### 1. Pandoc Integration Service
- **PandocConversionService**: Core service for Pandoc-based document conversions
- **Automatic Pandoc Detection**: Checks for Pandoc availability on startup
- **Fallback Support**: Automatically falls back to existing implementations when Pandoc is unavailable
- **Cross-platform Support**: Works on Windows, Linux, and macOS

### 2. Enhanced PDF to Word Conversion
- **Pandoc-first Approach**: Uses Pandoc when available for better formatting preservation
- **Legacy Fallback**: Falls back to existing PDFBox-based implementation
- **Improved Quality**: Better handling of complex PDF layouts and formatting

### 3. Enhanced Word to PDF Conversion
- **Pandoc-first Approach**: Uses Pandoc when available for better formatting preservation
- **Legacy Fallback**: Falls back to existing iText-based implementation
- **Better Formatting**: Preserves document structure and styling more accurately

### 4. New Markdown to Word Conversion
- **Full Markdown Support**: Converts Markdown files to Word documents
- **Rich Formatting**: Supports headers, lists, code blocks, tables, and more
- **Pandoc and Fallback**: Uses Pandoc for full feature support, basic conversion as fallback
- **GraphQL Integration**: Complete GraphQL mutations for multipart and file path conversions

## Architecture

### Service Layer
```
PandocConversionService
├── Pandoc availability detection
├── Document conversion execution
├── Timeout and error handling
└── Temporary file management

MarkdownToWordConversionService
├── Markdown-specific conversion logic
├── Virus scanning integration
├── Audit logging
└── Fallback conversion implementation

Enhanced Existing Services:
├── PdfToWordConversionService (updated)
└── WordToPdfConversionService (updated)
```

### Configuration
```
PandocConfig
├── Executable path configuration
├── Timeout settings
├── File size limits
├── Fallback behavior
└── Additional command arguments
```

### GraphQL Integration
```
MarkdownConversionResolver
├── convertMarkdownToWordMultipart
├── convertMarkdownToWordFromPath
├── Input validation
└── Error handling
```

## Configuration Options

### Application Properties
```properties
# Pandoc Configuration
dms.pandoc.executable-path=pandoc
dms.pandoc.enabled=true
dms.pandoc.enable-fallback=true
dms.pandoc.timeout-seconds=300
dms.pandoc.max-file-size=52428800
dms.pandoc.virus-scanner=MOCK
dms.pandoc.temp-directory=
dms.pandoc.cleanup-after-hours=24
dms.pandoc.check-availability-on-startup=true
dms.pandoc.additional-args=
```

### Configuration Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `executable-path` | `pandoc` | Path to Pandoc executable |
| `enabled` | `true` | Enable/disable Pandoc conversion |
| `enable-fallback` | `true` | Enable fallback to existing implementations |
| `timeout-seconds` | `300` | Conversion timeout in seconds |
| `max-file-size` | `52428800` | Maximum file size (50MB) |
| `virus-scanner` | `MOCK` | Default virus scanner type |
| `temp-directory` | `` | Custom temp directory (uses system default if empty) |
| `cleanup-after-hours` | `24` | Auto-cleanup interval |
| `check-availability-on-startup` | `true` | Check Pandoc on startup |
| `additional-args` | `` | Additional Pandoc command arguments |

## GraphQL Schema Extensions

### Markdown Conversion Mutations
```graphql
# Convert Markdown to Word from multipart file
convertMarkdownToWordMultipart(input: MarkdownConversionMultipartInput!): MarkdownConversionResult!

# Convert Markdown to Word from file path
convertMarkdownToWordFromPath(input: MarkdownConversionPathInput!): MarkdownConversionResult!
```

### Input Types
```graphql
input MarkdownConversionMultipartInput {
  file: Upload!
  scannerType: VirusScannerType
}

input MarkdownConversionPathInput {
  filePath: String!
  scannerType: VirusScannerType
}
```

### Result Type
```graphql
type MarkdownConversionResult {
  sessionId: String!
  originalFileName: String!
  convertedFileName: String!
  downloadPath: String!
  fileSize: Long!
  virusScanResponse: MarkdownVirusScanResponse!
  success: Boolean!
  message: String!
  errorDetails: String
  completedAt: DateTime
  processingTimeMs: Long
  usedPandoc: Boolean!
  conversionMethod: String!
}
```

## Installation and Setup

### 1. Install Pandoc
```bash
# Ubuntu/Debian
sudo apt-get install pandoc

# CentOS/RHEL
sudo yum install pandoc

# macOS
brew install pandoc

# Windows
# Download from https://pandoc.org/installing.html
```

### 2. Verify Installation
```bash
pandoc --version
```

### 3. Configure DMS
Update `application.properties` with appropriate Pandoc settings.

## Usage Examples

### GraphQL Mutations

#### Convert Markdown to Word (Multipart)
```graphql
mutation {
  convertMarkdownToWordMultipart(input: {
    file: $file
    scannerType: MOCK
  }) {
    sessionId
    originalFileName
    convertedFileName
    downloadPath
    success
    usedPandoc
    conversionMethod
    processingTimeMs
  }
}
```

#### Convert Markdown to Word (File Path)
```graphql
mutation {
  convertMarkdownToWordFromPath(input: {
    filePath: "/path/to/document.md"
    scannerType: MOCK
  }) {
    sessionId
    success
    usedPandoc
    conversionMethod
    downloadPath
  }
}
```

## Fallback Behavior

### When Pandoc is Available
1. **PDF to Word**: Uses Pandoc for better formatting preservation
2. **Word to PDF**: Uses Pandoc for better layout handling
3. **Markdown to Word**: Uses Pandoc for full Markdown feature support

### When Pandoc is Unavailable
1. **PDF to Word**: Falls back to PDFBox text extraction + POI Word generation
2. **Word to PDF**: Falls back to POI text extraction + iText PDF generation
3. **Markdown to Word**: Falls back to basic Markdown parsing + POI Word generation

### Fallback Indicators
- `usedPandoc`: Boolean indicating if Pandoc was used
- `conversionMethod`: String indicating the method used ("pandoc" or "fallback")

## Error Handling

### Common Error Scenarios
1. **Pandoc Not Found**: Falls back to existing implementation
2. **Conversion Timeout**: Configurable timeout with graceful failure
3. **File Size Exceeded**: Validates against configured limits
4. **Virus Detection**: Integrates with existing virus scanning
5. **Invalid File Format**: Validates file extensions and content

### Error Response Format
```json
{
  "success": false,
  "message": "Conversion failed: reason",
  "errorDetails": "Detailed error information",
  "sessionId": "session-id-for-tracking"
}
```

## Testing

### Test Coverage
- **Unit Tests**: All services, resolvers, and configurations
- **Integration Tests**: End-to-end conversion workflows
- **GraphQL Tests**: Complete GraphQL integration testing
- **Fallback Tests**: Scenarios with and without Pandoc
- **Performance Tests**: Large file handling and timeout scenarios

### Running Tests
```bash
# Run all conversion tests
mvn test -Dtest="**/*PandocConversionServiceTest,**/*MarkdownToWordConversionServiceTest,**/*MarkdownConversionResolverTest,**/*PandocConfigTest,**/*MarkdownConversionIntegrationTest,**/*MarkdownConversionGraphQLIntegrationTest"

# Run with test script
./scripts/run-all-tests.bat
```

## Performance Considerations

### Optimization Features
- **Process Timeout**: Prevents hanging conversions
- **File Size Limits**: Configurable maximum file sizes
- **Temporary File Cleanup**: Automatic cleanup of conversion artifacts
- **Resource Management**: Proper cleanup of system resources

### Performance Metrics
- **Conversion Time**: Tracked per conversion operation
- **Memory Usage**: Optimized for large file handling
- **Concurrent Operations**: Thread-safe implementation

## Security Features

### Virus Scanning Integration
- **Pre-conversion Scanning**: All files scanned before conversion
- **Configurable Scanners**: Support for multiple virus scanner types
- **Audit Logging**: Complete audit trail for all conversions

### File Validation
- **Extension Validation**: Validates file extensions
- **Size Limits**: Enforces maximum file sizes
- **Path Traversal Protection**: Prevents directory traversal attacks

## Monitoring and Observability

### Audit Events
- `CONVERSION_INITIATED`: Conversion process started
- `CONVERSION_VIRUS_SCAN_STARTED`: Virus scanning initiated
- `CONVERSION_VIRUS_SCAN_COMPLETED`: Virus scanning completed
- `CONVERSION_COMPLETED`: Conversion successfully completed
- `CONVERSION_FAILED`: Conversion failed
- `CONVERSION_FILE_DOWNLOADED`: Converted file ready for download

### Metrics Tracked
- Conversion success/failure rates
- Processing times by conversion type
- Pandoc vs fallback usage statistics
- File size distributions
- Error frequency and types

## Troubleshooting

### Common Issues

#### Pandoc Not Found
```
Error: Pandoc is not available for conversion
Solution: Install Pandoc or enable fallback mode
```

#### Conversion Timeout
```
Error: Pandoc conversion timed out after X seconds
Solution: Increase timeout or reduce file size
```

#### Permission Issues
```
Error: Failed to create temp file
Solution: Check temp directory permissions
```

### Debug Configuration
```properties
# Enable debug logging
logging.level.com.ascentbusiness.dms_svc.service.PandocConversionService=DEBUG
logging.level.com.ascentbusiness.dms_svc.service.MarkdownToWordConversionService=DEBUG
```

## Future Enhancements

### Planned Features
1. **Additional Format Support**: HTML, EPUB, LaTeX conversions
2. **Batch Conversion**: Multiple file conversion in single operation
3. **Template Support**: Custom document templates for conversions
4. **Advanced Options**: More Pandoc command-line options
5. **Conversion Queuing**: Asynchronous conversion processing

### Integration Opportunities
1. **Document Templates**: Integration with template system
2. **Workflow Integration**: Conversion as workflow steps
3. **SharePoint Integration**: Direct conversion from SharePoint documents
4. **Elasticsearch Integration**: Index converted document content

## Conclusion

The Pandoc conversion implementation provides a robust, scalable solution for document conversion with intelligent fallback capabilities. The system maintains backward compatibility while offering enhanced conversion quality when Pandoc is available.

For questions or issues, refer to the troubleshooting section or contact the development team.
