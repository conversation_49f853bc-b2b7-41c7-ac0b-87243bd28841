# DMS Business Features API Documentation

**Last Updated:** January 2025  
**Version:** 2.0  
**Service URL:** `http://localhost:9093` (development)

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Document Management](#document-management)
4. [Search and Discovery](#search-and-discovery)
5. [Security and Audit](#security-and-audit)
6. [<PERSON><PERSON><PERSON> Handling](#error-handling)
7. [Rate Limiting](#rate-limiting)

## Overview

The DMS Business Features API provides comprehensive document management capabilities designed for enterprise use. The service operates primarily through GraphQL with REST endpoints for specific operations like file downloads.

### Base URLs
- **GraphQL API**: `http://localhost:9093/graphql`
- **GraphiQL Interface**: `http://localhost:9093/graphiql`
- **REST Downloads**: `http://localhost:9093/api/v2/documents`
- **Health Check**: `http://localhost:9093/actuator/health`

### API Architecture
- **Primary Interface**: GraphQL for all document operations
- **File Downloads**: Optimized REST endpoints
- **Authentication**: JWT-based security
- **Real-time**: WebSocket subscriptions (future)

## Authentication

### JWT Token Authentication
All API requests require a valid JWT token in the Authorization header.

```http
Authorization: Bearer <jwt-token>
```

### Test Token Generation (Development)
```graphql
mutation GenerateTestToken($input: JwtTokenRequest!) {
  generateTestToken(input: $input) {
    token
    tokenType
    expiresAt
    userId
    roles
    permissions
  }
}
```

**Variables:**
```json
{
  "input": {
    "userId": "test-user",
    "roles": ["USER"],
    "permissions": ["READ", "WRITE"],
    "expirationMinutes": 60
  }
}
```

**cURL Example:**
```bash
curl -X POST http://localhost:9093/graphql \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mutation GenerateTestToken($input: JwtTokenRequest!) { generateTestToken(input: $input) { token tokenType expiresAt } }",
    "variables": {
      "input": {
        "userId": "test-user",
        "roles": ["USER"],
        "permissions": ["READ", "WRITE"]
      }
    }
  }'
```

## Document Management

### Upload Document

#### Enhanced Upload (Recommended)
```graphql
mutation UploadDocumentEnhanced($input: EnhancedDocumentUploadInput!) {
  uploadDocumentEnhanced(input: $input) {
    success
    document {
      id
      name
      version
      status
      storageProvider
      fileSize
      mimeType
      createdDate
      createdBy
    }
    uploadId
    fileName
    fileSize
    processingStrategy
    processingStatus
    message
    uploadedAt
  }
}
```

**Input Schema:**
```graphql
input EnhancedDocumentUploadInput {
  file: Upload!
  name: String!
  description: String
  keywords: [String!]
  storageProvider: StorageProvider = LOCAL
  overrideFile: Boolean = false
  validateFileType: Boolean = true
  enableProgressTracking: Boolean = true
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput
}
```

#### Multipart Upload Example
```bash
curl -X POST http://localhost:9093/graphql \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F 'operations={"query":"mutation UploadDocumentEnhanced($input: EnhancedDocumentUploadInput!) { uploadDocumentEnhanced(input: $input) { success document { id name version } message } }","variables":{"input":{"name":"Business Report","description":"Quarterly business report","keywords":["business","report"],"storageProvider":"LOCAL","validateFileType":true}}}' \
  -F 'map={"0":["variables.input.file"]}' \
  -F '0=@/path/to/document.pdf'
```

### Download Document

#### Get Download Information
```http
GET /api/v2/documents/{documentId}/download/info
Authorization: Bearer <token>
```

**Response:**
```json
{
  "documentId": 123,
  "name": "Business Report",
  "originalFileName": "business_report.pdf",
  "fileSize": 2048576,
  "mimeType": "application/pdf",
  "version": 1,
  "status": "ACTIVE",
  "storageProvider": "LOCAL",
  "createdBy": "test-user",
  "createdDate": "2025-01-15T10:30:00Z",
  "downloadUrl": "/api/v2/documents/123/download",
  "apiVersion": "2.0"
}
```

#### Download File
```http
GET /api/v2/documents/{documentId}/download
Authorization: Bearer <token>
```

**Query Parameters:**
- `inline` (optional): `true` for inline display, `false` for download (default: false)

### Document Metadata Operations

#### Get Document by ID
```graphql
query GetDocumentById($id: ID!) {
  getDocumentById(id: $id) {
    id
    name
    description
    version
    status
    storageProvider
    fileSize
    mimeType
    createdDate
    lastModifiedDate
    createdBy
    tags
    permissions {
      userId
      permissionType
      isActive
      expiresAt
    }
    classificationMetadata {
      module
      confidentialityLevel
      dataClassification
    }
    ownershipMetadata {
      owner
      department
      expiryDate
    }
  }
}
```

#### Update Document Metadata
```graphql
mutation UpdateDocumentMetadata($id: ID!, $input: UpdateDocumentInput!) {
  updateDocumentMetadata(id: $id, input: $input) {
    id
    name
    description
    lastModifiedDate
  }
}
```

### Delete Document
```graphql
mutation DeleteDocument($id: ID!) {
  deleteDocument(id: $id)
}
```

## Search and Discovery

### Document Search
```graphql
query SearchDocuments($filter: DocumentSearchInput, $pagination: PaginationInput) {
  searchDocuments(filter: $filter, pagination: $pagination) {
    content {
      id
      name
      version
      status
      fileSize
      mimeType
      createdDate
      createdBy
      tags
    }
    totalElements
    totalPages
    size
    number
    first
    last
  }
}
```

**Search Filters:**
```graphql
input DocumentSearchInput {
  name: String
  description: String
  keywords: [String!]
  storageProvider: StorageProvider
  status: DocumentStatus
  creatorUserId: String
  dateFrom: DateTime
  dateTo: DateTime
  fileType: String
  minFileSize: Long
  maxFileSize: Long
  module: String
  confidentialityLevel: String
  owner: String
  department: String
}
```

**Example Search:**
```json
{
  "filter": {
    "name": "report",
    "status": "ACTIVE",
    "dateFrom": "2025-01-01T00:00:00Z",
    "tags": ["business"]
  },
  "pagination": {
    "page": 0,
    "size": 20,
    "sortBy": "createdDate",
    "sortDirection": "DESC"
  }
}
```

## Security and Audit

### Get Audit Logs
```graphql
query GetAuditLogs($filter: AuditLogFilterInput, $pagination: PaginationInput) {
  getAuditLogs(filter: $filter, pagination: $pagination) {
    content {
      id
      documentId
      action
      userId
      details
      timestamp
      correlationId
      ipAddress
      userAgent
    }
    totalElements
    totalPages
  }
}
```

### Security Violations
```graphql
query GetSecurityViolations($filter: SecurityViolationFilterInput, $pagination: PaginationInput) {
  getSecurityViolations(filter: $filter, pagination: $pagination) {
    content {
      id
      userId
      violationType
      severity
      violationDetails
      timestamp
      resolved
    }
    totalElements
  }
}
```

## Error Handling

### Standard HTTP Status Codes
- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication required or invalid
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **413 Payload Too Large**: File size exceeds limits
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

### GraphQL Error Format
```json
{
  "data": null,
  "errors": [
    {
      "message": "Document not found",
      "locations": [{"line": 2, "column": 3}],
      "path": ["getDocumentById"],
      "extensions": {
        "code": "DOCUMENT_NOT_FOUND",
        "documentId": "123",
        "correlationId": "abc-123-def"
      }
    }
  ]
}
```

### Custom Error Codes
- **DOCUMENT_NOT_FOUND**: Document does not exist
- **UNAUTHORIZED**: Invalid or expired token
- **INSUFFICIENT_PERMISSIONS**: User lacks required permissions
- **FILE_TOO_LARGE**: File exceeds size limits
- **INVALID_FILE_TYPE**: File type not supported
- **DUPLICATE_FILE_EXCEPTION**: File with same name exists

## Rate Limiting

### Rate Limits
- **Standard Users**: 1000 requests/hour
- **File Upload**: 100 uploads/hour
- **Search Operations**: 500 searches/hour

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640095200
```

### Rate Limit Exceeded Response
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Please try again later.",
    "details": {
      "limit": 1000,
      "resetTime": "2025-01-27T12:00:00Z"
    }
  }
}
```

## Testing and Development

### Interactive Testing
Use the built-in test page for development:
- **URL**: `http://localhost:9093/test-dms.html`
- **Features**: Authentication, upload, search, download testing
- **No Dependencies**: Works directly in browser

### GraphiQL Interface
- **URL**: `http://localhost:9093/graphiql`
- **Features**: Interactive GraphQL query builder
- **Schema**: Built-in schema documentation
- **Authentication**: Add Authorization header in interface

---

## Support and Resources

- **GraphiQL Interface**: http://localhost:9093/graphiql
- **Health Check**: http://localhost:9093/actuator/health
- **Test Page**: http://localhost:9093/test-dms.html
- **Main Documentation**: [Complete API Documentation](DMS_Complete_API_Documentation.md)

**Last Updated:** January 2025  
**API Version:** 2.0  
**Service Port:** 9093