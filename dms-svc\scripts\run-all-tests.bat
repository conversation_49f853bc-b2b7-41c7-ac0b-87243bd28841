@echo off
REM DMS Service - Comprehensive Test Execution Script
REM This script runs all test categories and generates detailed reports
REM Includes: Unit, Integration, E2E, Security, Performance, Contract, Config, Infrastructure, API, and GraphQL tests
REM VSCode-friendly version with resource management

echo ========================================
echo DMS Service Automated Testing Suite
echo ========================================
echo Version: 2.1 - VSCode-Friendly Test Runner
echo.
echo ⚠ WARNING: This script runs intensive tests that may impact VSCode performance.
echo ⚠ Consider using 'run-vscode-tests.bat' for lighter testing during development.
echo ⚠ Or run with --unit-only for faster execution.
echo ⚠ See scripts\TESTING_GUIDE.md for more information.
echo.

REM Check if running in VSCode terminal and warn user
if defined VSCODE_PID (
    echo ⚠ DETECTED: Running in VSCode terminal
    echo ⚠ This may cause VSCode to become unresponsive
    echo ⚠ Recommendation: Use 'run-vscode-tests.bat' or run in external terminal
    echo.
    timeout /t 5 /nobreak >nul
)

REM Set process priority to below normal to avoid overwhelming VSCode
REM Skip complex process detection for now to avoid syntax errors
echo Setting lower process priority to be VSCode-friendly...
wmic process where "name='java.exe'" CALL setpriority "below normal" >nul 2>&1

REM Set Maven options for better resource management
set MAVEN_OPTS=-Xmx1g -XX:MaxMetaspaceSize=256m -Djava.awt.headless=true -XX:+UseG1GC -Dmaven.wagon.http.pool=false
set MAVEN_BATCH_ECHO=off

REM Add timeout for individual test phases (30 minutes max per phase)
set TEST_TIMEOUT=1800

REM Set variables
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set REPORT_DIR=target\test-reports\%TIMESTAMP%
set TEMP_LOG_FILE=%TEMP%\dms-test-execution-%TIMESTAMP%.log
set LOG_FILE=%REPORT_DIR%\test-execution.log
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..

REM Initialize failure counters for all test categories
set UNIT_TEST_FAILED=0
set INTEGRATION_TEST_FAILED=0
set E2E_TEST_FAILED=0
set SECURITY_TEST_FAILED=0
set PERFORMANCE_TEST_FAILED=0
set CONTRACT_TEST_FAILED=0
set CONFIG_TEST_FAILED=0
set INFRASTRUCTURE_TEST_FAILED=0
set API_TEST_FAILED=0
set GRAPHQL_TEST_FAILED=0
set COMPLIANCE_TEST_FAILED=0
set RETENTION_TEST_FAILED=0
set DOCUMENTATION_TEST_FAILED=0
set DOCUMENT_SHARING_TEST_FAILED=0
set EXTENDED_PROCESSING_TEST_FAILED=0
set VIRUS_SCANNING_TEST_FAILED=0
set CONVERSION_TEST_FAILED=0

REM Parse command line arguments
set RUN_UNIT_TESTS=1
set RUN_INTEGRATION_TESTS=1
set RUN_E2E_TESTS=1
set RUN_SECURITY_TESTS=1
set RUN_PERFORMANCE_TESTS=1
set RUN_CONTRACT_TESTS=1
set RUN_CONFIG_TESTS=1
set RUN_INFRASTRUCTURE_TESTS=1
set RUN_API_TESTS=1
set RUN_GRAPHQL_TESTS=1
set RUN_COMPLIANCE_TESTS=1
set RUN_RETENTION_TESTS=1
set RUN_DOCUMENTATION_TESTS=1
set RUN_DOCUMENT_SHARING_TESTS=1
set RUN_CONVERSION_TESTS=1
set FAIL_FAST=0
set GENERATE_REPORTS=1

:parse_args
if "%1"=="" goto :args_done
if /i "%1"=="--unit-only" (
    set RUN_INTEGRATION_TESTS=0
    set RUN_E2E_TESTS=0
    set RUN_SECURITY_TESTS=0
    set RUN_PERFORMANCE_TESTS=0
    set RUN_CONTRACT_TESTS=0
    set RUN_CONFIG_TESTS=0
    set RUN_INFRASTRUCTURE_TESTS=0
    set RUN_API_TESTS=0
    set RUN_GRAPHQL_TESTS=0
    set RUN_COMPLIANCE_TESTS=0
    set RUN_RETENTION_TESTS=0
    set RUN_DOCUMENT_SHARING_TESTS=0
    set RUN_DOCUMENTATION_TESTS=0
)
if /i "%1"=="--integration-only" (
    set RUN_UNIT_TESTS=0
    set RUN_E2E_TESTS=0
    set RUN_SECURITY_TESTS=0
    set RUN_PERFORMANCE_TESTS=0
    set RUN_CONTRACT_TESTS=0
    set RUN_CONFIG_TESTS=0
    set RUN_INFRASTRUCTURE_TESTS=0
    set RUN_API_TESTS=0
    set RUN_GRAPHQL_TESTS=0
    set RUN_COMPLIANCE_TESTS=0
    set RUN_RETENTION_TESTS=0
    set RUN_DOCUMENT_SHARING_TESTS=0
    set RUN_DOCUMENTATION_TESTS=0
)
if /i "%1"=="--security-only" (
    set RUN_UNIT_TESTS=0
    set RUN_INTEGRATION_TESTS=0
    set RUN_E2E_TESTS=0
    set RUN_PERFORMANCE_TESTS=0
    set RUN_CONTRACT_TESTS=0
    set RUN_CONFIG_TESTS=0
    set RUN_INFRASTRUCTURE_TESTS=0
    set RUN_API_TESTS=0
    set RUN_GRAPHQL_TESTS=0
    set RUN_COMPLIANCE_TESTS=0
    set RUN_RETENTION_TESTS=0
    set RUN_DOCUMENT_SHARING_TESTS=0
    set RUN_DOCUMENTATION_TESTS=0
)
if /i "%1"=="--sharing-only" (
    set RUN_UNIT_TESTS=0
    set RUN_INTEGRATION_TESTS=0
    set RUN_E2E_TESTS=0
    set RUN_SECURITY_TESTS=0
    set RUN_PERFORMANCE_TESTS=0
    set RUN_CONTRACT_TESTS=0
    set RUN_CONFIG_TESTS=0
    set RUN_INFRASTRUCTURE_TESTS=0
    set RUN_API_TESTS=0
    set RUN_GRAPHQL_TESTS=0
    set RUN_COMPLIANCE_TESTS=0
    set RUN_RETENTION_TESTS=0
    set RUN_DOCUMENTATION_TESTS=0
)
if /i "%1"=="--documentation-only" (
    set RUN_UNIT_TESTS=0
    set RUN_INTEGRATION_TESTS=0
    set RUN_E2E_TESTS=0
    set RUN_SECURITY_TESTS=0
    set RUN_PERFORMANCE_TESTS=0
    set RUN_CONTRACT_TESTS=0
    set RUN_CONFIG_TESTS=0
    set RUN_INFRASTRUCTURE_TESTS=0
    set RUN_API_TESTS=0
    set RUN_GRAPHQL_TESTS=0
    set RUN_COMPLIANCE_TESTS=0
    set RUN_RETENTION_TESTS=0
    set RUN_DOCUMENT_SHARING_TESTS=0
)
if /i "%1"=="--fail-fast" set FAIL_FAST=1
if /i "%1"=="--no-reports" set GENERATE_REPORTS=0
if /i "%1"=="--help" goto :show_help
shift
goto :parse_args

:args_done

REM Create temporary log file first (outside target directory to avoid clean conflicts)
echo Test execution started at %date% %time% > "%TEMP_LOG_FILE%"
echo Report directory: %REPORT_DIR% >> "%TEMP_LOG_FILE%"
echo Arguments: %* >> "%TEMP_LOG_FILE%"
echo. >> "%TEMP_LOG_FILE%"

echo Starting comprehensive test execution...
echo Logs will be saved to: %LOG_FILE%
echo.

REM Skip to main execution (help section is at the end)
goto :main_execution

:show_help
echo.
echo Usage: run-all-tests.bat [OPTIONS]
echo.
echo Options:
echo   --unit-only         Run only unit tests
echo   --integration-only  Run only integration tests
echo   --security-only     Run only security tests
echo   --sharing-only      Run only document sharing tests
echo   --documentation-only Run only documentation and automation tests
echo   --fail-fast         Stop on first test failure
echo   --no-reports        Skip report generation
echo   --help              Show this help message
echo.
echo Examples:
echo   run-all-tests.bat                    # Run all tests
echo   run-all-tests.bat --unit-only        # Run only unit tests
echo   run-all-tests.bat --fail-fast        # Stop on first failure
echo.
exit /b 0

:main_execution

REM Check prerequisites
echo Checking prerequisites...
where mvn >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Maven is not installed or not in PATH
    goto :error
)

where java >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    goto :error
)

REM Clean and compile (VSCode-friendly approach)
echo [1/18] Cleaning and compiling project...

REM Skip clean to avoid file locking conflicts with VSCode
echo Skipping clean to avoid VSCode file conflicts...
echo Proceeding directly to compilation...

REM Use offline mode and reduced parallelism to be gentler on system resources
echo Compiling project with VSCode-friendly settings...
echo Running: mvn compile test-compile -Denforcer.skip=true -q --batch-mode
call mvn compile test-compile -Denforcer.skip=true -q --batch-mode >> "%TEMP_LOG_FILE%" 2>&1
if errorlevel 1 (
    echo ERROR: Compilation failed. Check %TEMP_LOG_FILE% for details.
    echo Trying compilation without parallel execution...
    echo Running: mvn compile test-compile -Denforcer.skip=true --batch-mode
    call mvn compile test-compile -Denforcer.skip=true --batch-mode >> "%TEMP_LOG_FILE%" 2>&1
    if errorlevel 1 (
        echo ERROR: Compilation failed. Check %TEMP_LOG_FILE% for details.
        echo Displaying last 20 lines of log file:
        if exist "%TEMP_LOG_FILE%" (
            powershell -Command "Get-Content '%TEMP_LOG_FILE%' | Select-Object -Last 20"
        )
        goto :error
    )
)
echo ✓ Compilation successful

REM Now create the final report directory and move the log file
if not exist "%REPORT_DIR%" mkdir "%REPORT_DIR%"
copy "%TEMP_LOG_FILE%" "%LOG_FILE%" > nul
del "%TEMP_LOG_FILE%"

REM Run Unit Tests
if %RUN_UNIT_TESTS%==1 (
    echo [2/18] Running Unit Tests...
    echo Running unit tests with reduced resource usage...
    call mvn test -Dtest="**/*Test,**/*LargeFileUploadTest,!**/*IntegrationTest,!**/*E2ETest,!**/*SecurityTest,!**/*PerformanceTest,!**/*ContractTest" -DexcludedGroups="integration,e2e,security,performance,contract" -Denforcer.skip=true -T 1 -q --batch-mode -Dmaven.test.failure.ignore=true >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Unit tests had failures. Check reports for details.
        set UNIT_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Unit tests passed
        set UNIT_TEST_FAILED=0
    )
) else (
    echo [2/18] Skipping Unit Tests...
)

REM Run Integration Tests
if %RUN_INTEGRATION_TESTS%==1 (
    echo [3/18] Running Integration Tests...
    echo Running integration tests with reduced resource usage...
    call mvn integration-test -Dtest="**/*IntegrationTest,**/*DocumentSharingIntegrationTest,**/*WorkflowControllerIntegrationTest,**/*ElasticsearchIntegrationTest,**/*ExtendedFileProcessingIntegrationTest,**/*MarkdownConversionIntegrationTest,**/*MarkdownConversionGraphQLIntegrationTest,**/*MarkdownConversionRestIntegrationTest,**/*PdfConversionIntegrationTest,**/*PdfConversionGraphQLIntegrationTest,**/*WordConversionIntegrationTest,**/*WordConversionGraphQLIntegrationTest,**/*TamperProofAuditIntegrationTest,**/*VirusScanningIntegrationTest,**/*CorrelationIdIntegrationTest,**/*DocumentPermissionIntegrationTest,**/*DocumentServiceIntegrationTest,**/*DocumentationAndAutomationIntegrationTest,**/*AuditGraphQLIntegrationTest,**/*ConversionGraphQLIntegrationTest,**/*DiagnosticsGraphQLIntegrationTest,**/*DocumentUploadGraphQLIntegrationTest,**/*TemplateManagementGraphQLIntegrationTest,**/*GraphQLMigrationIntegrationTest,**/*GraphQLMultipartUploadTest,**/*GraphQLSchemaValidationTest,**/*UrlUploadIntegrationTest,**/*UrlDownloadServiceTest" -Denforcer.skip=true -T 1 -q --batch-mode -Dmaven.test.failure.ignore=true >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Integration tests had failures. Check reports for details.
        set INTEGRATION_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Integration tests passed
        set INTEGRATION_TEST_FAILED=0
    )
) else (
    echo [3/18] Skipping Integration Tests...
)

REM Run E2E Tests
if %RUN_E2E_TESTS%==1 (
    echo [4/18] Running End-to-End Tests...
    call mvn integration-test -Dtest="**/*E2ETest,**/*DocumentSharingGraphQLE2ETest,**/*DocumentGraphQLE2ETest,**/*AdvancedSearchGraphQLE2ETest,**/*DocumentPermissionGraphQLTest,**/*ApplicationContextLoadTest,**/*CompleteWorkflowE2ETest,**/*DocumentSharingGraphQLE2ETestNoDocker" -Denforcer.skip=true --batch-mode -Dmaven.test.failure.ignore=true >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ E2E tests had failures. Check reports for details.
        set E2E_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ E2E tests passed
        set E2E_TEST_FAILED=0
    )
) else (
    echo [4/18] Skipping E2E Tests...
)

REM Run Security Tests
if %RUN_SECURITY_TESTS%==1 (
    echo [5/18] Running Security Tests...
    call mvn test -Dtest="**/*SecurityTest,**/*AuditSecurityTest,**/*WorkflowSecurityTest,**/*PIIEncryptionServiceTest,**/*JwtTokenProviderLoggingTest,**/*CorrelationIdFilterTest,**/*GraphQLSecurityIntegrationTest" -Denforcer.skip=true --batch-mode -Dmaven.test.failure.ignore=true >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Security tests had failures. Check reports for details.
        set SECURITY_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Security tests passed
        set SECURITY_TEST_FAILED=0
    )
) else (
    echo [5/18] Skipping Security Tests...
)

REM Run Performance Tests
if %RUN_PERFORMANCE_TESTS%==1 (
    echo [6/18] Running Performance Tests...
    call mvn test -Dtest="**/*PerformanceTest,**/*BenchmarkTest,**/*AuditPerformanceTest,**/*SearchPerformanceTest,**/*AsyncProcessingPerformanceTest,**/*GraphQLPerformanceTest" -Denforcer.skip=true --batch-mode -Dmaven.test.failure.ignore=true >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Performance tests had failures. Check reports for details.
        set PERFORMANCE_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Performance tests passed
        set PERFORMANCE_TEST_FAILED=0
    )
) else (
    echo [6/18] Skipping Performance Tests...
)

REM Run Contract Tests
if %RUN_CONTRACT_TESTS%==1 (
    echo [7/18] Running Contract Tests...
    call mvn test -Dtest="**/*ContractTest,**/*GraphQLContractTest,**/*APIContractTest" -Denforcer.skip=true --batch-mode -Dmaven.test.failure.ignore=true >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Contract tests had failures. Check reports for details.
        set CONTRACT_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Contract tests passed
        set CONTRACT_TEST_FAILED=0
    )
) else (
    echo [7/18] Skipping Contract Tests...
)

REM Run Configuration Tests
if %RUN_CONFIG_TESTS%==1 (
    echo [8/18] Running Configuration Tests...
    call mvn test -Dtest="**/*ConfigTest,**/*BasicTest,**/*FileProcessingConfigTest,**/*PandocConfigTest,**/*PdfConversionConfigTest,**/*WordConversionConfigTest,**/*SecurityHeadersConfigTest,**/*ProfileTest" -Denforcer.skip=true --batch-mode -Dmaven.test.failure.ignore=true >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Configuration tests had failures. Check reports for details.
        set CONFIG_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Configuration tests passed
        set CONFIG_TEST_FAILED=0
    )
) else (
    echo [8/18] Skipping Configuration Tests...
)

REM Run Infrastructure Tests
if %RUN_INFRASTRUCTURE_TESTS%==1 (
    echo [9/18] Running Infrastructure Tests...
    if exist "%SCRIPT_DIR%run-infrastructure-tests.ps1" (
        powershell -ExecutionPolicy Bypass -NonInteractive -File "%SCRIPT_DIR%run-infrastructure-tests.ps1" >> "%LOG_FILE%" 2>&1
        if %ERRORLEVEL% neq 0 (
            echo ⚠ Infrastructure tests had failures. Check reports for details.
            set INFRASTRUCTURE_TEST_FAILED=1
            if %FAIL_FAST%==1 goto :error
        ) else (
            echo ✓ Infrastructure tests passed
            set INFRASTRUCTURE_TEST_FAILED=0
        )
    ) else (
        echo ⚠ Infrastructure test script not found, skipping...
        set INFRASTRUCTURE_TEST_FAILED=0
    )
) else (
    echo [9/18] Skipping Infrastructure Tests...
)

REM Run API Tests
if %RUN_API_TESTS%==1 (
    echo [10/18] Running API Tests...
    if exist "%PROJECT_ROOT%\tests\scripts\test-api-functionality.ps1" (
        powershell -ExecutionPolicy Bypass -NonInteractive -File "%PROJECT_ROOT%\tests\scripts\test-api-functionality.ps1" >> "%LOG_FILE%" 2>&1
        if %ERRORLEVEL% neq 0 (
            echo ⚠ API tests had failures. Check reports for details.
            set API_TEST_FAILED=1
            if %FAIL_FAST%==1 goto :error
        ) else (
            echo ✓ API tests passed
            set API_TEST_FAILED=0
        )
    ) else (
        echo ⚠ API test script not found, skipping...
        set API_TEST_FAILED=0
    )
) else (
    echo [10/18] Skipping API Tests...
)

REM Run GraphQL Tests
if %RUN_GRAPHQL_TESTS%==1 (
    echo [11/18] Running GraphQL Tests...
    if exist "%PROJECT_ROOT%\tests\scripts\test-graphql.ps1" (
        powershell -ExecutionPolicy Bypass -NonInteractive -File "%PROJECT_ROOT%\tests\scripts\test-graphql.ps1" >> "%LOG_FILE%" 2>&1
        if %ERRORLEVEL% neq 0 (
            echo ⚠ GraphQL tests had failures. Check reports for details.
            set GRAPHQL_TEST_FAILED=1
            if %FAIL_FAST%==1 goto :error
        ) else (
            echo ✓ GraphQL tests passed
            set GRAPHQL_TEST_FAILED=0
        )
    ) else (
        echo ⚠ GraphQL test script not found, running individual GraphQL tests...
        call mvn test -Dtest="**/*GraphQL*Test,**/*AuditGraphQLResolverTest,**/*GraphQLConfigurationTest" -Denforcer.skip=true -Dmaven.test.failure.ignore=true --batch-mode -q >> "%LOG_FILE%" 2>&1
        if %ERRORLEVEL% neq 0 (
            echo ⚠ GraphQL tests had failures. Check reports for details.
            set GRAPHQL_TEST_FAILED=1
            if %FAIL_FAST%==1 goto :error
        ) else (
            echo ✓ GraphQL tests passed
            set GRAPHQL_TEST_FAILED=0
        )
    )
) else (
    echo [11/18] Skipping GraphQL Tests...
)

REM Run Compliance Tests
if %RUN_COMPLIANCE_TESTS%==1 (
    echo [12/18] Running Compliance Tests...
    call mvn test -Dtest="**/*ComplianceTest,**/*Compliance*BasicTest,**/*ComplianceClassificationBasicTest,**/*ComplianceFrameworkBasicTest,**/*ComplianceFrameworkIntegrationTest" -Denforcer.skip=true -Dmaven.test.failure.ignore=true --batch-mode -q >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Compliance tests had failures. Check reports for details.
        set COMPLIANCE_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Compliance tests passed
        set COMPLIANCE_TEST_FAILED=0
    )
) else (
    echo [12/18] Skipping Compliance Tests...
)

REM Run Retention Policy Tests
if %RUN_RETENTION_TESTS%==1 (
    echo [13/18] Running Retention Policy Tests...
    call mvn test -Dtest="**/*RetentionTest,**/*Retention*BasicTest,**/*RetentionPolicyBasicTest,**/*RetentionRepositoryBasicTest,**/*RetentionServiceBasicTest" -Denforcer.skip=true -Dmaven.test.failure.ignore=true --batch-mode -q >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Retention tests had failures. Check reports for details.
        set RETENTION_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Retention tests passed
        set RETENTION_TEST_FAILED=0
    )
) else (
    echo [13/18] Skipping Retention Tests...
)

REM Run Document Sharing Tests
if %RUN_DOCUMENT_SHARING_TESTS%==1 (
    echo [14/18] Running Document Sharing Tests...
    call mvn test -Dtest="**/*DocumentShar*Test,**/*BulkShar*Test,**/*Shar*Test,**/*DocumentSharingIntegrationTest,**/*DocumentSharingGraphQLE2ETest" -Denforcer.skip=true -Dmaven.test.failure.ignore=true --batch-mode -q >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Document sharing tests had failures. Check reports for details.
        set DOCUMENT_SHARING_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Document sharing tests passed
        set DOCUMENT_SHARING_TEST_FAILED=0
    )
) else (
    echo [14/18] Skipping Document Sharing Tests...
)

REM Run Extended File Processing Tests
echo [15/18] Running Extended File Processing Tests...
if exist "%SCRIPT_DIR%..\test-scripts\run-extended-file-processing-tests.bat" (
    call "%SCRIPT_DIR%..\test-scripts\run-extended-file-processing-tests.bat" >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Extended file processing tests had failures. Check reports for details.
        set EXTENDED_PROCESSING_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Extended file processing tests passed
        set EXTENDED_PROCESSING_TEST_FAILED=0
    )
) else if exist "%SCRIPT_DIR%..\test-scripts\run-async-processing-tests.bat" (
    echo ⚠ Extended file processing test script not found, running async processing tests...
    call "%SCRIPT_DIR%..\test-scripts\run-async-processing-tests.bat" >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Async processing tests had failures. Check reports for details.
        set EXTENDED_PROCESSING_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Async processing tests passed
        set EXTENDED_PROCESSING_TEST_FAILED=0
    )
) else (
    echo ⚠ Extended file processing test script not found, running individual tests...
    call mvn test -Dtest="**/*FileProcessingConfigTest,**/*ProcessingStrategyTest,**/*AsyncDocumentProcessorTest,**/*ExtendedFileProcessingIntegrationTest,**/*AsyncProcessingJobRepositoryTest,**/*AsyncProcessingJobEntityTest,**/*AsyncJobStatusTest,**/*ChunkedUploadManagerTest,**/*AsyncProcessingResolverTest" -Denforcer.skip=true -Dmaven.test.failure.ignore=true --batch-mode -q >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Extended file processing tests had failures. Check reports for details.
        set EXTENDED_PROCESSING_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Extended file processing tests passed
        set EXTENDED_PROCESSING_TEST_FAILED=0
    )
)

REM Run Virus Scanning Tests
echo [16/18] Running Virus Scanning Tests...
if exist "%SCRIPT_DIR%test-virus-scanning.bat" (
    call "%SCRIPT_DIR%test-virus-scanning.bat" >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Virus scanning tests had failures. Check reports for details.
        set VIRUS_SCANNING_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Virus scanning tests passed
        set VIRUS_SCANNING_TEST_FAILED=0
    )
) else (
    echo ⚠ Virus scanning test script not found, running individual tests...
    call mvn test -Dtest="**/*VirusScanner*Test,**/*VirusScanning*Test,**/*BulkUpload*Test,**/*MockVirusScannerTest,**/*VirusScannerFactoryTest,**/*VirusScanningServiceTest,**/*DocumentResolverVirusScanTest,**/*BulkUploadServiceTest" -Denforcer.skip=true -Dmaven.test.failure.ignore=true --batch-mode -q >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Virus scanning tests had failures. Check reports for details.
        set VIRUS_SCANNING_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Virus scanning tests passed
        set VIRUS_SCANNING_TEST_FAILED=0
    )
)

REM Run Document Conversion Tests (Pandoc, Markdown, PDF, Word)
if %RUN_CONVERSION_TESTS%==1 (
    echo [17/18] Running Document Conversion Tests...
    call mvn test -Dtest="**/*PandocConversionServiceTest,**/*MarkdownToWordConversionServiceTest,**/*MarkdownConversionResolverTest,**/*PandocConfigTest,**/*MarkdownConversionIntegrationTest,**/*MarkdownConversionGraphQLIntegrationTest,**/*PdfToWordConversionServiceTest,**/*WordToPdfConversionServiceTest,**/*WordToPdfConversionManualTest,**/*PdfConversionResolverTest,**/*WordConversionResolverTest" -Denforcer.skip=true -Dmaven.test.failure.ignore=true --batch-mode -q >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Document conversion tests had failures. Check reports for details.
        set CONVERSION_TEST_FAILED=1
    ) else (
        echo ✓ Document conversion tests completed successfully.
        set CONVERSION_TEST_FAILED=0
    )
) else (
    echo [17/18] Skipping Document Conversion Tests...
)

REM Run Documentation and Automation Tests
if %RUN_DOCUMENTATION_TESTS%==1 (
    echo [18/18] Running Documentation and Automation Tests...
    call mvn test -Dtest="**/*ApiDocumentationTest,**/*JavaDocCoverageTest,**/*TroubleshootingGuidesTest,**/*DependencyManagementTest,**/*RollbackProceduresTest,**/*DocumentationAndAutomationIntegrationTest" -Denforcer.skip=true -Dmaven.test.failure.ignore=true --batch-mode -q >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Documentation and automation tests had failures. Check reports for details.
        set DOCUMENTATION_TEST_FAILED=1
        if %FAIL_FAST%==1 goto :error
    ) else (
        echo ✓ Documentation and automation tests passed
        set DOCUMENTATION_TEST_FAILED=0
    )
) else (
    echo [18/18] Skipping Documentation and Automation Tests...
)

REM Generate Code Coverage Report
if %GENERATE_REPORTS%==1 (
    echo Generating code coverage report and test reports...
    echo Using reduced resource settings for report generation...
    call mvn jacoco:report -Denforcer.skip=true -q --batch-mode >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Code coverage report generation failed
    ) else (
        echo ✓ Code coverage report generated
    )

    REM Generate Test Reports
    echo Generating Surefire HTML test reports...
    call mvn surefire-report:report-only -Denforcer.skip=true -q --batch-mode >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Surefire test report generation had issues
    ) else (
        echo ✓ Surefire test reports generated
    )

    REM Generate Site with Test Reports
    echo Generating Maven site with test reports...
    call mvn site:site -Denforcer.skip=true -q --batch-mode >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ⚠ Site generation had issues
    ) else (
        echo ✓ Maven site generated with test reports
    )

    REM Copy reports to timestamped directory
    echo.
    echo Copying reports to timestamped directory...
    if exist "target\surefire-reports" (
        xcopy "target\surefire-reports\*" "%REPORT_DIR%\surefire-reports\" /E /I /Q >> "%LOG_FILE%" 2>&1
    )
    if exist "target\failsafe-reports" (
        xcopy "target\failsafe-reports\*" "%REPORT_DIR%\failsafe-reports\" /E /I /Q >> "%LOG_FILE%" 2>&1
    )
    if exist "target\site\jacoco" (
        xcopy "target\site\jacoco\*" "%REPORT_DIR%\coverage\" /E /I /Q >> "%LOG_FILE%" 2>&1
    )
    if exist "target\test-reports" (
        xcopy "target\test-reports\*" "%REPORT_DIR%\custom-reports\" /E /I /Q >> "%LOG_FILE%" 2>&1
    )
    if exist "target\infrastructure-test-reports" (
        xcopy "target\infrastructure-test-reports\*" "%REPORT_DIR%\infrastructure-reports\" /E /I /Q >> "%LOG_FILE%" 2>&1
    )

    REM Copy PowerShell test logs if they exist
    if exist "C:\temp\dms-*.log" (
        xcopy "C:\temp\dms-*.log" "%REPORT_DIR%\powershell-logs\" /I /Q >> "%LOG_FILE%" 2>&1
    )

    REM Copy documentation test reports if they exist
    if exist "target\documentation-test-results" (
        xcopy "target\documentation-test-results\*" "%REPORT_DIR%\documentation-reports\" /E /I /Q >> "%LOG_FILE%" 2>&1
    )
) else (
    echo Skipping report generation...
)

REM Generate summary
echo.
echo ========================================
echo TEST EXECUTION SUMMARY
echo ========================================
echo Execution completed at %date% %time%
echo.

if %RUN_UNIT_TESTS%==1 (
    if %UNIT_TEST_FAILED%==0 (
        echo ✓ Unit Tests: PASSED
    ) else (
        echo ✗ Unit Tests: FAILED
    )
)

if %RUN_INTEGRATION_TESTS%==1 (
    if %INTEGRATION_TEST_FAILED%==0 (
        echo ✓ Integration Tests: PASSED
    ) else (
        echo ✗ Integration Tests: FAILED
    )
)

if %RUN_E2E_TESTS%==1 (
    if %E2E_TEST_FAILED%==0 (
        echo ✓ E2E Tests: PASSED
    ) else (
        echo ✗ E2E Tests: FAILED
    )
)

if %RUN_SECURITY_TESTS%==1 (
    if %SECURITY_TEST_FAILED%==0 (
        echo ✓ Security Tests: PASSED
    ) else (
        echo ✗ Security Tests: FAILED
    )
)

if %RUN_PERFORMANCE_TESTS%==1 (
    if %PERFORMANCE_TEST_FAILED%==0 (
        echo ✓ Performance Tests: PASSED
    ) else (
        echo ✗ Performance Tests: FAILED
    )
)

if %RUN_CONTRACT_TESTS%==1 (
    if %CONTRACT_TEST_FAILED%==0 (
        echo ✓ Contract Tests: PASSED
    ) else (
        echo ✗ Contract Tests: FAILED
    )
)

if %RUN_CONFIG_TESTS%==1 (
    if %CONFIG_TEST_FAILED%==0 (
        echo ✓ Configuration Tests: PASSED
    ) else (
        echo ✗ Configuration Tests: FAILED
    )
)

if %RUN_INFRASTRUCTURE_TESTS%==1 (
    if %INFRASTRUCTURE_TEST_FAILED%==0 (
        echo ✓ Infrastructure Tests: PASSED
    ) else (
        echo ✗ Infrastructure Tests: FAILED
    )
)

if %RUN_API_TESTS%==1 (
    if %API_TEST_FAILED%==0 (
        echo ✓ API Tests: PASSED
    ) else (
        echo ✗ API Tests: FAILED
    )
)

if %RUN_GRAPHQL_TESTS%==1 (
    if %GRAPHQL_TEST_FAILED%==0 (
        echo ✓ GraphQL Tests: PASSED
    ) else (
        echo ✗ GraphQL Tests: FAILED
    )
)

if %RUN_COMPLIANCE_TESTS%==1 (
    if %COMPLIANCE_TEST_FAILED%==0 (
        echo ✓ Compliance Tests: PASSED
    ) else (
        echo ✗ Compliance Tests: FAILED
    )
)

if %RUN_RETENTION_TESTS%==1 (
    if %RETENTION_TEST_FAILED%==0 (
        echo ✓ Retention Tests: PASSED
    ) else (
        echo ✗ Retention Tests: FAILED
    )
)

REM Extended File Processing Tests (always run)
if %EXTENDED_PROCESSING_TEST_FAILED%==0 (
    echo ✓ Extended File Processing Tests: PASSED
) else (
    echo ✗ Extended File Processing Tests: FAILED
)

if %RUN_DOCUMENT_SHARING_TESTS%==1 (
    if %DOCUMENT_SHARING_TEST_FAILED%==0 (
        echo ✓ Document Sharing Tests: PASSED
    ) else (
        echo ✗ Document Sharing Tests: FAILED
    )
)

REM Virus Scanning Tests (always run)
if %VIRUS_SCANNING_TEST_FAILED%==0 (
    echo ✓ Virus Scanning Tests: PASSED
) else (
    echo ✗ Virus Scanning Tests: FAILED
)

if %RUN_CONVERSION_TESTS%==1 (
    if %CONVERSION_TEST_FAILED%==0 (
        echo ✓ Document Conversion Tests: PASSED
    ) else (
        echo ✗ Document Conversion Tests: FAILED
    )
)

if %RUN_DOCUMENTATION_TESTS%==1 (
    if %DOCUMENTATION_TEST_FAILED%==0 (
        echo ✓ Documentation and Automation Tests: PASSED
    ) else (
        echo ✗ Documentation and Automation Tests: FAILED
    )
)

echo.
echo Reports available in: %REPORT_DIR%
echo - Surefire Reports: %REPORT_DIR%\surefire-reports\
echo - Failsafe Reports: %REPORT_DIR%\failsafe-reports\
echo - Coverage Report: %REPORT_DIR%\coverage\index.html
echo - Custom Reports: %REPORT_DIR%\custom-reports\
echo - Infrastructure Reports: %REPORT_DIR%\infrastructure-reports\
echo - Documentation Reports: %REPORT_DIR%\documentation-reports\
echo - PowerShell Logs: %REPORT_DIR%\powershell-logs\
echo - Execution Log: %LOG_FILE%
echo.
echo HTML Test Reports:
echo - Surefire HTML Report: file:///D:/MyDevelopment/dms-svc/target/site/surefire-report.html
echo - Maven Site: file:///D:/MyDevelopment/dms-svc/target/site/index.html
echo - JaCoCo Coverage: file:///D:/MyDevelopment/dms-svc/target/site/jacoco/index.html

REM Calculate overall result
set /a TOTAL_FAILURES=%UNIT_TEST_FAILED%+%INTEGRATION_TEST_FAILED%+%E2E_TEST_FAILED%+%SECURITY_TEST_FAILED%+%PERFORMANCE_TEST_FAILED%+%CONTRACT_TEST_FAILED%+%CONFIG_TEST_FAILED%+%INFRASTRUCTURE_TEST_FAILED%+%API_TEST_FAILED%+%GRAPHQL_TEST_FAILED%+%COMPLIANCE_TEST_FAILED%+%RETENTION_TEST_FAILED%+%DOCUMENT_SHARING_TEST_FAILED%+%CONVERSION_TEST_FAILED%+%DOCUMENTATION_TEST_FAILED%+%EXTENDED_PROCESSING_TEST_FAILED%+%VIRUS_SCANNING_TEST_FAILED%

REM Count total test suites run (Extended Processing Tests and Virus Scanning Tests always run, so add 2)
set /a TOTAL_SUITES_RUN=%RUN_UNIT_TESTS%+%RUN_INTEGRATION_TESTS%+%RUN_E2E_TESTS%+%RUN_SECURITY_TESTS%+%RUN_PERFORMANCE_TESTS%+%RUN_CONTRACT_TESTS%+%RUN_CONFIG_TESTS%+%RUN_INFRASTRUCTURE_TESTS%+%RUN_API_TESTS%+%RUN_GRAPHQL_TESTS%+%RUN_COMPLIANCE_TESTS%+%RUN_RETENTION_TESTS%+%RUN_DOCUMENT_SHARING_TESTS%+%RUN_CONVERSION_TESTS%+%RUN_DOCUMENTATION_TESTS%+2

echo.
echo Test Suites Run: %TOTAL_SUITES_RUN%
echo Test Suites Failed: %TOTAL_FAILURES%

REM Generate HTML test report
echo.
echo ========================================
echo 📊 GENERATING HTML TEST REPORT
echo ========================================
echo.
echo Generating HTML test report from XML results...
powershell -ExecutionPolicy Bypass -File "%~dp0generate-simple-report.ps1"
if %ERRORLEVEL% EQU 0 (
    echo ✓ HTML test report generated successfully
    echo   Report location: %~dp0..\target\site\surefire-report.html
) else (
    echo ✗ Failed to generate HTML test report
)

if %TOTAL_FAILURES%==0 (
    echo.
    echo 🎉 OVERALL RESULT: ALL TESTS PASSED
    echo ========================================
    echo.
    echo 📋 HTML Test Report: file:///%~dp0..\target\site\surefire-report.html
    echo.
    exit /b 0
) else (
    echo.
    echo ❌ OVERALL RESULT: %TOTAL_FAILURES% TEST SUITE(S) FAILED
    echo ========================================
    echo.
    echo 📋 HTML Test Report: file:///%~dp0..\target\site\surefire-report.html
    echo.
    exit /b 1
)

:error
echo.
echo ❌ CRITICAL ERROR: Test execution failed
REM Check if we need to copy temp log file to final location
if exist "%TEMP_LOG_FILE%" (
    if not exist "%REPORT_DIR%" mkdir "%REPORT_DIR%"
    copy "%TEMP_LOG_FILE%" "%LOG_FILE%" > nul
    del "%TEMP_LOG_FILE%"
    echo Check %LOG_FILE% for details
) else (
    echo Check %LOG_FILE% for details
)
echo ========================================
exit /b 1
