package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "dms.storage")
public class StorageConfigurationProperties {
    
    /**
     * Default storage provider for documents
     */
    private StorageProvider provider = StorageProvider.LOCAL;
    
    /**
     * Local storage configuration
     */
    private Local local = new Local();
    
    /**
     * S3 storage configuration
     */
    private S3 s3 = new S3();

    /**
     * SharePoint storage configuration
     */
    private SharePoint sharepoint = new SharePoint();

    @Data
    public static class Local {
        /**
         * Base path for local file storage
         */
        private String basePath = "./storage/documents";
    }
    
    @Data
    public static class S3 {
        /**
         * S3 bucket name
         */
        private String bucketName = "grc-dms-bucket";
        
        /**
         * AWS region
         */
        private String region = "us-east-1";
        
        /**
         * AWS access key
         */
        private String accessKey;
        
        /**
         * AWS secret key
         */
        private String secretKey;
        
        /**
         * S3 endpoint (for localstack or custom endpoints)
         */
        private String endpoint;
    }
    
    @Data
    public static class SharePoint {
        /**
         * Azure AD Tenant ID
         */
        private String tenantId;
        
        /**
         * Azure App Registration Client ID
         */
        private String clientId;
        
        /**
         * Azure App Registration Client Secret
         */
        private String clientSecret;
        
        /**
         * OAuth2 scopes for Microsoft Graph API
         */
        private String scopes = "User.Read,Sites.ReadWrite.All,Files.ReadWrite.All";
        
        /**
         * Microsoft Graph API base URL
         */
        private String graphApiUrl = "https://graph.microsoft.com/v1.0";
        
        /**
         * OAuth2 token URL for authentication
         */
        private String tokenUrl;
        
        /**
         * SharePoint site URL
         */
        private String siteUrl;
        
        /**
         * SharePoint Document Library name
         */
        private String documentLibrary = "DMS";
        
        /**
         * Connection timeout in milliseconds
         */
        private int connectionTimeout = 30000;
        
        /**
         * Read timeout in milliseconds
         */
        private int readTimeout = 60000;
        
        /**
         * Maximum retry attempts for failed operations
         */
        private int maxRetries = 3;
        
        /**
         * Delay between retry attempts in milliseconds
         */
        private int retryDelayMs = 1000;
        
        /**
         * Enable circuit breaker pattern
         */
        private boolean enableCircuitBreaker = true;
        
        /**
         * Circuit breaker failure threshold
         */
        private int circuitBreakerThreshold = 5;
        
        /**
         * Circuit breaker timeout in milliseconds
         */
        private int circuitBreakerTimeoutMs = 60000;
        
        /**
         * Enable debug logging
         */
        private boolean debug = false;
        
        /**
         * Enable request/response logging
         */
        private boolean enableRequestLogging = false;
        
        /**
         * Enable fail-safe startup (application starts even if SharePoint connection fails)
         */
        private boolean failSafeStartup = false;
    }
}
