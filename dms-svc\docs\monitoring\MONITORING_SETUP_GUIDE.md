# DMS Monitoring & Observability Setup Guide

## Overview

This guide provides comprehensive instructions for setting up monitoring and observability for the DMS (Document Management System) service. The monitoring stack includes:

- **Structured Logging**: JSON-formatted logs with correlation IDs and tracing
- **Distributed Tracing**: OpenTelemetry/Zipkin integration for request tracing
- **Metrics Collection**: Prometheus metrics for business and technical metrics
- **Health Checks**: Advanced health indicators for all system components
- **Alerting**: Prometheus AlertManager with multiple notification channels
- **Dashboards**: Grafana dashboards for visualization

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   DMS Service   │───▶│   Prometheus    │───▶│     Grafana     │
│                 │    │                 │    │                 │
│ - Metrics       │    │ - Metrics Store │    │ - Dashboards    │
│ - Health Checks │    │ - Alert Rules   │    │ - Visualization │
│ - Traces        │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         │                       ▼
         │              ┌─────────────────┐
         │              │  AlertManager   │
         │              │                 │
         │              │ - Notifications │
         │              │ - Routing       │
         │              │ - Inhibition    │
         │              └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐
│     Zipkin      │    │   Log Aggregator│
│                 │    │                 │
│ - Trace Storage │    │ - EL<PERSON> Stack     │
│ - Trace UI      │    │ - Structured    │
│                 │    │   Logs          │
└─────────────────┘    └─────────────────┘
```

## Prerequisites

- Docker and Docker Compose
- Java 21+
- Maven 3.8+
- Access to monitoring infrastructure (Prometheus, Grafana, etc.)

## Quick Start

### 1. Enable Monitoring in DMS Service

The DMS service comes with monitoring capabilities built-in. Ensure the following configuration in `application.properties`:

```properties
# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true

# Tracing Configuration
management.tracing.enabled=true
management.tracing.sampling.probability=0.1
management.zipkin.tracing.endpoint=http://localhost:9411/api/v2/spans
```

### 2. Start Monitoring Stack

Use the provided Docker Compose configuration:

```bash
cd monitoring/docker
docker-compose up -d
```

This will start:
- Prometheus (port 9090)
- Grafana (port 3000)
- AlertManager (port 9093)
- Zipkin (port 9411)

### 3. Configure Grafana

1. Access Grafana at http://localhost:3000
2. Login with admin/admin
3. Import the DMS dashboard from `monitoring/grafana/dms-overview-dashboard.json`
4. Configure Prometheus as data source: http://prometheus:9090

### 4. Verify Setup

1. Check DMS metrics: http://localhost:8080/dms/actuator/prometheus
2. Check Prometheus targets: http://localhost:9090/targets
3. View Grafana dashboard: http://localhost:3000
4. Check AlertManager: http://localhost:9093

## Monitoring Components

### Structured Logging

The DMS service uses structured JSON logging with the following features:

- **Correlation ID tracking** across all requests
- **Distributed tracing integration** with trace and span IDs
- **Separate log files** for different log types:
  - `dms-application.log` - General application logs
  - `dms-audit.log` - Audit events
  - `dms-security.log` - Security events
  - `dms-performance.log` - Performance metrics

#### Log Format Example

```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "level": "INFO",
  "message": "Document uploaded successfully",
  "service": "dms-svc",
  "environment": "production",
  "correlationId": "abc123-def456-ghi789",
  "traceId": "1234567890abcdef",
  "spanId": "abcdef1234567890",
  "userId": "user123",
  "operation": "DOCUMENT_UPLOAD",
  "resourceId": "doc456"
}
```

### Business Metrics

The DMS service exposes comprehensive business metrics:

#### Document Lifecycle Metrics
- `dms_business_documents_created_total` - Documents created
- `dms_business_documents_updated_total` - Documents updated
- `dms_business_documents_deleted_total` - Documents deleted
- `dms_business_documents_versioned_total` - Document versions created

#### User Activity Metrics
- `dms_business_user_login_total` - User logins
- `dms_business_user_login_failed_total` - Failed login attempts
- `dms_business_user_active` - Currently active users

#### Storage Metrics
- `dms_business_storage_used_bytes` - Total storage used
- `dms_business_storage_quota_utilization` - Storage quota utilization percentage

#### Compliance Metrics
- `dms_business_compliance_violations_total` - Compliance violations
- `dms_business_audit_events_total` - Audit events

### Health Checks

Advanced health indicators monitor:

#### Database Health
- Connection validity and performance
- Query performance and slow query detection
- Data integrity checks
- Connection pool status

#### Storage Health
- Storage provider connectivity
- Read/write operation testing
- Capacity and utilization monitoring
- Provider-specific health checks

#### Elasticsearch Health
- Cluster status and node information
- Index health and statistics
- Search functionality testing

#### System Health
- JVM memory usage and garbage collection
- CPU utilization and system load
- Disk space monitoring
- Thread health and deadlock detection

### Alerting Rules

Comprehensive alerting covers:

#### Critical Alerts
- Application down
- Database connectivity issues
- Critical memory usage
- Compliance violations

#### Warning Alerts
- High response times
- Elevated error rates
- Performance degradation
- Security events

#### Business Alerts
- High document upload failure rates
- Low cache hit ratios
- Storage utilization warnings

### Notification Channels

AlertManager supports multiple notification channels:

- **Email** - For all alert types
- **Slack** - For critical and performance alerts
- **PagerDuty** - For critical alerts requiring immediate response

## Configuration

### Environment Variables

Set the following environment variables for production:

```bash
# SMTP Configuration
SMTP_PASSWORD=your-smtp-password

# Slack Integration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# PagerDuty Integration
PAGERDUTY_INTEGRATION_KEY=your-pagerduty-key

# Application Configuration
ENVIRONMENT=production
APPLICATION_VERSION=1.0.0
```

### Prometheus Configuration

Update `monitoring/prometheus/prometheus.yml`:

```yaml
scrape_configs:
  - job_name: 'dms-service'
    static_configs:
      - targets: ['dms-service:8080']
    metrics_path: '/dms/actuator/prometheus'
    scrape_interval: 15s
```

### Grafana Data Sources

Configure Prometheus data source in Grafana:

```yaml
apiVersion: 1
datasources:
  - name: Prometheus
    type: prometheus
    url: http://prometheus:9090
    access: proxy
    isDefault: true
```

## Troubleshooting

### Common Issues

1. **Metrics not appearing in Prometheus**
   - Check DMS service is exposing metrics: `curl http://localhost:8080/dms/actuator/prometheus`
   - Verify Prometheus configuration and targets

2. **Alerts not firing**
   - Check AlertManager configuration
   - Verify alert rule syntax in Prometheus
   - Check notification channel configuration

3. **Traces not appearing in Zipkin**
   - Verify Zipkin endpoint configuration
   - Check tracing is enabled in application properties
   - Ensure sampling probability is appropriate

4. **Dashboard not loading data**
   - Verify Grafana data source configuration
   - Check Prometheus connectivity
   - Validate dashboard queries

### Log Analysis

Use structured logging for troubleshooting:

```bash
# Find all errors for a specific correlation ID
grep "correlationId\":\"abc123-def456-ghi789" dms-application.log | jq '.'

# Find all security events
grep "logType\":\"SECURITY" dms-security.log | jq '.'

# Find slow operations
grep "duration" dms-performance.log | jq 'select(.duration > 1000)'
```

## Best Practices

1. **Monitor business metrics** alongside technical metrics
2. **Use correlation IDs** for request tracing
3. **Set appropriate alert thresholds** to avoid alert fatigue
4. **Regularly review and update** dashboards and alerts
5. **Test alerting channels** regularly
6. **Monitor the monitoring stack** itself
7. **Use structured logging** consistently
8. **Implement proper log retention** policies

## Security Considerations

1. **Secure monitoring endpoints** with authentication
2. **Avoid logging sensitive data** in metrics and logs
3. **Use encrypted communication** between monitoring components
4. **Implement proper access controls** for monitoring dashboards
5. **Regularly update monitoring components** for security patches

## Maintenance

### Regular Tasks

1. **Review alert effectiveness** monthly
2. **Update dashboard queries** as needed
3. **Clean up old metrics and logs** based on retention policies
4. **Test disaster recovery** procedures
5. **Update monitoring documentation**

### Capacity Planning

Monitor the monitoring infrastructure:
- Prometheus storage usage
- Grafana performance
- Log storage requirements
- Network bandwidth usage

## Support

For issues with monitoring setup:
1. Check the troubleshooting section
2. Review application logs
3. Consult monitoring component documentation
4. Contact the DMS team for assistance
