# DMS Service - Basic Upload & Download Guide

## Overview

The Document Management Service (DMS) provides secure document upload and download capabilities through GraphQL API endpoints and REST download endpoints. This guide covers the essential operations needed to integrate with the DMS service.

## Table of Contents

1. [Authentication](#authentication)
2. [GraphQL Endpoint](#graphql-endpoint)
3. [Document Upload](#document-upload)
4. [Document Download](#document-download)
5. [Angular 18 Integration](#angular-18-integration)
6. [Common Headers](#common-headers)
7. [Error Handling](#error-handling)
8. [Testing](#testing)

## Authentication

The DMS service uses JWT (JSON Web Token) authentication. You need to obtain a valid JWT token before making any document operations.

### Generate Test Token (Development)

```graphql
mutation GenerateTestToken($input: JwtTokenRequest!) {
  generateTestToken(input: $input) {
    token
    tokenType
    expiresAt
  }
}
```

**Variables:**
```json
{
  "input": {
    "username": "test-user",
    "roles": ["USER"],
    "permissions": ["READ", "WRITE"]
  }
}
```

**Headers:**
```json
{
  "Content-Type": "application/json"
}
```

## GraphQL Endpoint

**Base URL:** `http://localhost:9093/graphql` (development)
**GraphiQL Interface:** `http://localhost:9093/graphiql`
**Production URL:** Update according to your deployment

## Document Upload

### Basic Upload Mutation

```graphql
mutation UploadDocument($input: UploadDocumentInput!) {
  uploadDocument(input: $input) {
    id
    name
    version
    status
    storageProvider
    filePath
    mimeType
    fileSize
    createdBy
    createdDate
    lastModifiedDate
    keywords
  }
}
```

### Enhanced Upload Mutation (Recommended)

```graphql
mutation UploadDocumentEnhanced($input: EnhancedDocumentUploadInput!) {
  uploadDocumentEnhanced(input: $input) {
    success
    document {
      id
      name
      version
      status
      storageProvider
      filePath
      mimeType
      fileSize
      createdBy
      createdDate
      lastModifiedDate
      keywords
    }
    uploadId
    fileName
    fileSize
    processingStrategy
    processingStatus
    message
    uploadedAt
  }
}
```

### GraphiQL Upload Example

**Request Headers:**
```json
{
  "Authorization": "Bearer YOUR_JWT_TOKEN",
  "Content-Type": "multipart/form-data"
}
```

**Variables (Basic Upload):**
```json
{
  "input": {
    "name": "Sample Document",
    "description": "This is a test document",
    "keywords": ["test", "sample"],
    "storageProvider": "LOCAL",
    "overrideFile": false
  }
}
```

**Variables (Enhanced Upload):**
```json
{
  "input": {
    "name": "Sample Document",
    "description": "This is a test document",
    "keywords": ["test", "sample"],
    "storageProvider": "LOCAL",
    "overrideFile": false,
    "validateFileType": true,
    "enableProgressTracking": true,
    "classificationMetadata": {
      "module": "HR",
      "businessUnit": "Operations",
      "confidentialityLevel": "Internal"
    }
  }
}
```

**File Upload:**
- In GraphiQL, use the file upload interface
- Select your file using the file picker
- The file will be automatically mapped to the `$input.file` variable

### cURL Upload Example

```bash
curl -X POST \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F 'operations={"query":"mutation UploadDocument($input: UploadDocumentInput!) { uploadDocument(input: $input) { id name version status storageProvider filePath mimeType fileSize createdBy createdDate lastModifiedDate keywords } }","variables":{"input":{"name":"Sample Document","description":"Test document","keywords":["test"],"storageProvider":"LOCAL","overrideFile":false}}}' \
  -F 'map={"0":["variables.input.file"]}' \
  -F '0=@/path/to/your/document.pdf' \
  http://localhost:9093/graphql
```

## Document Download

### Download Query (Deprecated - Use REST API)

```graphql
query DownloadDocument($id: ID!) {
  downloadDocument(id: $id) {
    id
    name
    version
    status
    storageProvider
    filePath
    mimeType
    fileSize
    createdBy
    createdDate
    lastModifiedDate
  }
}
```

**Note:** The GraphQL `downloadDocument` query is deprecated for actual file downloads. Use the REST API endpoints instead.

### REST Download Endpoints (Recommended)

#### V2 API - Enhanced Download

```
GET /api/v2/documents/{documentId}/download
```

**Headers:**
```json
{
  "Authorization": "Bearer YOUR_JWT_TOKEN"
}
```

**Query Parameters:**
- `inline` (optional): `true` to display inline, `false` to download as attachment (default: false)

#### Download Metadata Only

```
GET /api/v2/documents/{documentId}/download/info
```

**Response:**
```json
{
  "documentId": 123,
  "name": "Sample Document",
  "originalFileName": "sample_document.pdf",
  "fileSize": 2048576,
  "mimeType": "application/pdf",
  "version": 1,
  "status": "ACTIVE",
  "storageProvider": "LOCAL",
  "createdBy": "test-user",
  "createdDate": "2024-01-15T10:30:00Z",
  "lastModifiedDate": "2024-01-20T14:45:00Z",
  "downloadUrl": "/api/v2/documents/123/download",
  "inlineUrl": "/api/v2/documents/123/download?inline=true",
  "apiVersion": "2.0",
  "timestamp": "2024-01-25T09:15:30Z"
}
```

### cURL Download Examples

```bash
# Get download metadata
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:9093/api/v2/documents/123/download/info

# Download file as attachment
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -o downloaded_file.pdf \
     http://localhost:9093/api/v2/documents/123/download

# Display file inline (for PDFs, images)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:9093/api/v2/documents/123/download?inline=true
```

## Angular 18 Integration

### Service Setup

Create a DMS service in your Angular application:

```typescript
// dms.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError, throwError } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DmsService {
  private readonly graphqlUrl = 'http://localhost:9093/graphql';
  private readonly downloadUrl = 'http://localhost:9093/api/v2/documents';

  constructor(private http: HttpClient) {}

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('jwt_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  private getUploadHeaders(): HttpHeaders {
    const token = localStorage.getItem('jwt_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`
      // Don't set Content-Type for multipart/form-data - let browser set it
    });
  }

  // Generate test token (development only)
  generateTestToken(username: string, roles: string[] = ['USER']): Observable<any> {
    const query = `
      mutation GenerateTestToken($input: JwtTokenRequest!) {
        generateTestToken(input: $input) {
          token
          tokenType
          expiresAt
        }
      }
    `;

    const variables = {
      input: { 
        username, 
        roles,
        permissions: ['READ', 'WRITE']
      }
    };

    return this.http.post(this.graphqlUrl, { query, variables }, {
      headers: new HttpHeaders({ 'Content-Type': 'application/json' })
    });
  }

  // Enhanced document upload
  uploadDocumentEnhanced(file: File, metadata?: any): Observable<any> {
    const formData = new FormData();
    
    const operations = {
      query: `
        mutation UploadDocumentEnhanced($input: EnhancedDocumentUploadInput!) {
          uploadDocumentEnhanced(input: $input) {
            success
            document {
              id
              name
              version
              status
              storageProvider
              filePath
              mimeType
              fileSize
              createdBy
              createdDate
              lastModifiedDate
              keywords
            }
            uploadId
            fileName
            fileSize
            processingStrategy
            processingStatus
            message
            uploadedAt
          }
        }
      `,
      variables: {
        input: {
          file: null,
          name: metadata?.name || file.name,
          description: metadata?.description || '',
          keywords: metadata?.keywords || [],
          storageProvider: metadata?.storageProvider || 'LOCAL',
          overrideFile: metadata?.overrideFile || false,
          validateFileType: true,
          enableProgressTracking: true,
          ...metadata
        }
      }
    };

    const map = { "0": ["variables.input.file"] };

    formData.append('operations', JSON.stringify(operations));
    formData.append('map', JSON.stringify(map));
    formData.append('0', file);

    return this.http.post(this.graphqlUrl, formData, {
      headers: this.getUploadHeaders()
    }).pipe(
      catchError(error => {
        console.error('Upload error:', error);
        if (error.status === 401) {
          localStorage.removeItem('jwt_token');
        }
        return throwError(() => error);
      })
    );
  }

  // Basic document upload (for backward compatibility)
  uploadDocument(file: File, metadata?: any): Observable<any> {
    const formData = new FormData();
    
    const operations = {
      query: `
        mutation UploadDocument($input: UploadDocumentInput!) {
          uploadDocument(input: $input) {
            id
            name
            version
            status
            storageProvider
            filePath
            mimeType
            fileSize
            createdBy
            createdDate
            lastModifiedDate
            keywords
          }
        }
      `,
      variables: {
        input: {
          file: null,
          name: metadata?.name || file.name,
          description: metadata?.description || '',
          keywords: metadata?.keywords || [],
          storageProvider: metadata?.storageProvider || 'LOCAL',
          overrideFile: metadata?.overrideFile || false
        }
      }
    };

    const map = { "0": ["variables.input.file"] };

    formData.append('operations', JSON.stringify(operations));
    formData.append('map', JSON.stringify(map));
    formData.append('0', file);

    return this.http.post(this.graphqlUrl, formData, {
      headers: this.getUploadHeaders()
    });
  }

  // Get download metadata
  getDownloadInfo(documentId: string): Observable<any> {
    return this.http.get(`${this.downloadUrl}/${documentId}/download/info`, {
      headers: this.getHeaders()
    });
  }

  // Direct download
  downloadDocument(documentId: string): Observable<Blob> {
    return this.http.get(`${this.downloadUrl}/${documentId}/download`, {
      headers: this.getHeaders(),
      responseType: 'blob'
    });
  }

  // Search documents
  searchDocuments(filter?: any, pagination?: any): Observable<any> {
    const query = `
      query SearchDocuments($filter: DocumentSearchInput, $pagination: PaginationInput) {
        searchDocuments(filter: $filter, pagination: $pagination) {
          content {
            id
            name
            version
            status
            storageProvider
            filePath
            mimeType
            fileSize
            createdBy
            createdDate
            lastModifiedDate
            keywords
          }
          totalElements
          totalPages
          size
          number
          first
          last
        }
      }
    `;

    const variables = { 
      filter: filter || {}, 
      pagination: pagination || { page: 0, size: 10, sortBy: "createdDate", sortDirection: "DESC" }
    };

    return this.http.post(this.graphqlUrl, { query, variables }, {
      headers: this.getHeaders()
    });
  }

  // Get document by ID
  getDocument(documentId: string): Observable<any> {
    const query = `
      query GetDocument($id: ID!) {
        getDocument(id: $id) {
          id
          name
          version
          status
          storageProvider
          filePath
          mimeType
          fileSize
          createdBy
          createdDate
          lastModifiedDate
          keywords
        }
      }
    `;

    const variables = { id: documentId };

    return this.http.post(this.graphqlUrl, { query, variables }, {
      headers: this.getHeaders()
    });
  }
}
```

### Component Example

```typescript
// document-manager.component.ts
import { Component, OnInit } from '@angular/core';
import { DmsService } from './dms.service';

@Component({
  selector: 'app-document-manager',
  template: `
    <div class="document-manager">
      <h2>Document Management</h2>
      
      <!-- Authentication Section -->
      <div *ngIf="!isAuthenticated" class="auth-section">
        <h3>Authentication</h3>
        <input [(ngModel)]="username" placeholder="Username" />
        <button (click)="authenticate()">Get Test Token</button>
      </div>

      <!-- Upload Section -->
      <div *ngIf="isAuthenticated" class="upload-section">
        <h3>Upload Document</h3>
        <input type="file" (change)="onFileSelected($event)" />
        <input [(ngModel)]="documentName" placeholder="Document Name" />
        <textarea [(ngModel)]="documentDescription" placeholder="Description"></textarea>
        <input [(ngModel)]="documentKeywords" placeholder="Keywords (comma separated)" />
        <button (click)="uploadDocument()" [disabled]="!selectedFile">Upload</button>
        
        <div *ngIf="uploadProgress" class="progress">
          Upload Progress: {{ uploadProgress }}%
        </div>
      </div>

      <!-- Document List -->
      <div *ngIf="isAuthenticated" class="document-list">
        <h3>Documents</h3>
        <button (click)="loadDocuments()">Refresh List</button>
        
        <div *ngFor="let doc of documents" class="document-item">
          <div class="document-info">
            <strong>{{ doc.name }}</strong>
            <span class="file-size">({{ formatFileSize(doc.fileSize) }})</span>
            <div class="document-meta">
              <span>Version: {{ doc.version }}</span>
              <span>Created: {{ doc.createdDate | date:'short' }}</span>
              <span>By: {{ doc.createdBy }}</span>
            </div>
          </div>
          <div class="document-actions">
            <button (click)="downloadDocument(doc.id)">Download</button>
            <button (click)="viewDocumentInfo(doc.id)">Info</button>
          </div>
        </div>
      </div>

      <!-- Document Info Modal -->
      <div *ngIf="selectedDocumentInfo" class="modal-overlay" (click)="closeDocumentInfo()">
        <div class="modal-content" (click)="$event.stopPropagation()">
          <h3>Document Information</h3>
          <pre>{{ selectedDocumentInfo | json }}</pre>
          <button (click)="closeDocumentInfo()">Close</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .document-manager {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .auth-section, .upload-section, .document-list {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    
    .document-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      border-bottom: 1px solid #eee;
    }
    
    .document-info {
      flex: 1;
    }
    
    .document-meta {
      font-size: 0.9em;
      color: #666;
      margin-top: 5px;
    }
    
    .document-meta span {
      margin-right: 15px;
    }
    
    .document-actions button {
      margin-left: 10px;
    }
    
    .file-size {
      color: #888;
      font-size: 0.9em;
    }
    
    .progress {
      margin-top: 10px;
      padding: 10px;
      background-color: #f0f0f0;
      border-radius: 3px;
    }
    
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    
    .modal-content {
      background: white;
      padding: 20px;
      border-radius: 5px;
      max-width: 600px;
      max-height: 80vh;
      overflow-y: auto;
    }
    
    input, textarea, button {
      margin: 5px;
      padding: 8px;
    }
    
    button {
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    }
    
    button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
    
    button:hover:not(:disabled) {
      background-color: #0056b3;
    }
  `]
})
export class DocumentManagerComponent implements OnInit {
  username = 'test-user';
  isAuthenticated = false;
  selectedFile: File | null = null;
  documentName = '';
  documentDescription = '';
  documentKeywords = '';
  documents: any[] = [];
  uploadProgress: number | null = null;
  selectedDocumentInfo: any = null;

  constructor(private dmsService: DmsService) {
    // Check if already authenticated
    this.isAuthenticated = !!localStorage.getItem('jwt_token');
  }

  ngOnInit() {
    if (this.isAuthenticated) {
      this.loadDocuments();
    }
  }

  authenticate() {
    this.dmsService.generateTestToken(this.username).subscribe({
      next: (response) => {
        if (response.data?.generateTestToken?.token) {
          localStorage.setItem('jwt_token', response.data.generateTestToken.token);
          this.isAuthenticated = true;
          this.loadDocuments();
          console.log('Authentication successful');
        }
      },
      error: (error) => {
        console.error('Authentication failed:', error);
        alert('Authentication failed');
      }
    });
  }

  onFileSelected(event: any) {
    this.selectedFile = event.target.files[0];
    if (this.selectedFile && !this.documentName) {
      this.documentName = this.selectedFile.name;
    }
  }

  uploadDocument() {
    if (!this.selectedFile) return;

    const metadata = {
      name: this.documentName,
      description: this.documentDescription,
      keywords: this.documentKeywords.split(',').map(k => k.trim()).filter(k => k),
      storageProvider: 'LOCAL',
      overrideFile: false,
      classificationMetadata: {
        module: 'General',
        businessUnit: 'IT',
        confidentialityLevel: 'Internal'
      }
    };

    this.uploadProgress = 0;

    this.dmsService.uploadDocumentEnhanced(this.selectedFile, metadata).subscribe({
      next: (response) => {
        console.log('Upload successful:', response);
        if (response.data?.uploadDocumentEnhanced?.success) {
          alert('Document uploaded successfully');
          this.resetUploadForm();
          this.loadDocuments();
        } else {
          alert('Upload failed: ' + (response.data?.uploadDocumentEnhanced?.message || 'Unknown error'));
        }
        this.uploadProgress = null;
      },
      error: (error) => {
        console.error('Upload failed:', error);
        alert('Upload failed');
        this.uploadProgress = null;
      }
    });
  }

  downloadDocument(documentId: string) {
    this.dmsService.downloadDocument(documentId).subscribe({
      next: (blob) => {
        // Get document info to get the filename
        this.dmsService.getDownloadInfo(documentId).subscribe({
          next: (info) => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = info.originalFileName || 'document';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
          },
          error: () => {
            // Fallback if we can't get the filename
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'document';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
          }
        });
      },
      error: (error) => {
        console.error('Download failed:', error);
        alert('Download failed');
      }
    });
  }

  viewDocumentInfo(documentId: string) {
    this.dmsService.getDownloadInfo(documentId).subscribe({
      next: (info) => {
        this.selectedDocumentInfo = info;
      },
      error: (error) => {
        console.error('Failed to get document info:', error);
        alert('Failed to get document info');
      }
    });
  }

  closeDocumentInfo() {
    this.selectedDocumentInfo = null;
  }

  loadDocuments() {
    this.dmsService.searchDocuments().subscribe({
      next: (response) => {
        this.documents = response.data?.searchDocuments?.content || [];
      },
      error: (error) => {
        console.error('Failed to load documents:', error);
      }
    });
  }

  resetUploadForm() {
    this.selectedFile = null;
    this.documentName = '';
    this.documentDescription = '';
    this.documentKeywords = '';
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
```

### Module Configuration

```typescript
// app.module.ts
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';

import { AppComponent } from './app.component';
import { DocumentManagerComponent } from './document-manager.component';
import { DmsService } from './dms.service';

@NgModule({
  declarations: [
    AppComponent,
    DocumentManagerComponent
  ],
  imports: [
    BrowserModule,
    HttpClientModule,
    FormsModule
  ],
  providers: [DmsService],
  bootstrap: [AppComponent]
})
export class AppModule { }
```

## Common Headers

### For GraphQL Requests
```json
{
  "Authorization": "Bearer YOUR_JWT_TOKEN",
  "Content-Type": "application/json"
}
```

### For File Upload (multipart/form-data)
```json
{
  "Authorization": "Bearer YOUR_JWT_TOKEN"
}
```
*Note: Don't set Content-Type for uploads - let the browser set it automatically*

### For REST Download
```json
{
  "Authorization": "Bearer YOUR_JWT_TOKEN"
}
```

## Error Handling

### Common Error Responses

1. **Authentication Error (401)**
```json
{
  "errors": [
    {
      "message": "Authentication required. Please provide a valid JWT token.",
      "extensions": {
        "code": "UNAUTHORIZED"
      }
    }
  ]
}
```

2. **File Too Large (413)**
```json
{
  "errors": [
    {
      "message": "File size exceeds maximum allowed size",
      "extensions": {
        "code": "FILE_TOO_LARGE"
      }
    }
  ]
}
```

3. **Invalid File Type (400)**
```json
{
  "errors": [
    {
      "message": "File type not allowed",
      "extensions": {
        "code": "INVALID_FILE_TYPE"
      }
    }
  ]
}
```

4. **Document Not Found (404)**
```json
{
  "error": "DOCUMENT_NOT_FOUND",
  "message": "Document not found with id: 123",
  "timestamp": "2024-01-25T09:15:30Z",
  "status": 404
}
```

### Angular Error Handling

```typescript
// In your service methods, add proper error handling:
uploadDocumentEnhanced(file: File, metadata?: any): Observable<any> {
  // ... existing code ...
  
  return this.http.post(this.graphqlUrl, formData, {
    headers: this.getUploadHeaders()
  }).pipe(
    catchError(error => {
      console.error('Upload error:', error);
      if (error.status === 401) {
        // Handle authentication error
        localStorage.removeItem('jwt_token');
        // Redirect to login or show auth form
      } else if (error.status === 413) {
        // Handle file too large
        alert('File is too large. Please select a smaller file.');
      } else if (error.status === 400) {
        // Handle validation errors
        const errorMessage = error.error?.errors?.[0]?.message || 'Invalid request';
        alert('Upload failed: ' + errorMessage);
      }
      return throwError(() => error);
    })
  );
}
```

## Testing

### Interactive Test Page

The DMS service includes an interactive test page that you can access at:

**URL:** `http://localhost:9093/test-dms.html`

This test page provides:
- Authentication with test token generation
- Document upload with file selection and metadata
- Document search functionality
- Document download with automatic filename detection
- Real-time result display with success/error feedback

### GraphiQL Interface

1. Navigate to `http://localhost:9093/graphiql`
2. First, generate a test token using the `generateTestToken` mutation
3. Copy the returned token
4. Add the Authorization header: `{"Authorization": "Bearer YOUR_TOKEN"}`
5. Use the upload/download mutations as shown above

### cURL Testing

```bash
# Test authentication
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query":"mutation GenerateTestToken($input: JwtTokenRequest!) { generateTestToken(input: $input) { token tokenType expiresAt } }","variables":{"input":{"username":"test-user","roles":["USER"],"permissions":["READ","WRITE"]}}}' \
  http://localhost:9093/graphql

# Test document search
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"query":"query SearchDocuments($filter: DocumentSearchInput, $pagination: PaginationInput) { searchDocuments(filter: $filter, pagination: $pagination) { content { id name version status fileSize } totalElements } }","variables":{"filter":{},"pagination":{"page":0,"size":10,"sortBy":"createdDate","sortDirection":"DESC"}}}' \
  http://localhost:9093/graphql
```

## Environment Configuration

Update your Angular environment files:

```typescript
// environment.ts
export const environment = {
  production: false,
  dmsApiUrl: 'http://localhost:9093',
  dmsGraphqlUrl: 'http://localhost:9093/graphql'
};

// environment.prod.ts
export const environment = {
  production: true,
  dmsApiUrl: 'https://your-production-dms-url.com',
  dmsGraphqlUrl: 'https://your-production-dms-url.com/graphql'
};
```

## Advanced Features

### Chunked Upload for Large Files

```graphql
# Initialize chunked upload
mutation InitializeChunkedUpload($input: ChunkedUploadInitInput!) {
  initializeChunkedUpload(input: $input) {
    sessionId
    fileName
    totalSize
    chunkSize
    totalChunks
    status
    createdAt
    expiresAt
  }
}

# Upload chunk
mutation UploadChunk($input: ChunkUploadInput!) {
  uploadChunk(input: $input) {
    sessionId
    fileName
    uploadedChunks
    totalChunks
    progress
    status
    lastActivityAt
  }
}

# Complete chunked upload
mutation CompleteChunkedUpload($input: CompleteChunkedUploadInput!) {
  completeChunkedUpload(input: $input) {
    success
    document {
      id
      name
      version
    }
    message
  }
}
```

### Bulk Upload

```graphql
mutation BulkUploadDocuments($input: BulkDocumentUploadInput!) {
  bulkUploadDocuments(input: $input) {
    totalFiles
    successfulUploads
    failedUploads
    results {
      success
      fileName
      fileSize
      message
      document {
        id
        name
      }
    }
    overallStatus
    processingTimeMs
  }
}
```

### Advanced Search

```graphql
query AdvancedSearch($input: AdvancedSearchInput!, $pagination: PaginationInput) {
  advancedSearch(input: $input, pagination: $pagination) {
    documents {
      content {
        id
        name
        relevanceScore
        highlights {
          name
          description
          content
        }
      }
    }