# Test Suite Quick Reference Guide

## Quick Fix Summary (June 28, 2025)

### Issues Fixed ✅
1. **Numbering Inconsistencies** - All phases now consistently numbered [1/18] to [18/18]
2. **Interactive Prompts** - Added `--batch-mode` and `-NonInteractive` flags to prevent hanging
3. **Surefire Report Generation** - Standardized Maven commands for consistent reporting
4. **Comprehensive Test Coverage** - Updated all test categories with specific patterns

### Test Execution Phases (18 Total)

| Phase | Test Category | Coverage |
|-------|---------------|----------|
| [1/18] | Compilation | Project compilation with VSCode-friendly settings |
| [2/18] | Unit Tests | All unit tests (excludes integration/E2E/security/performance) |
| [3/18] | Integration Tests | 17 specific integration test types |
| [4/18] | E2E Tests | 6 specific end-to-end test types |
| [5/18] | Security Tests | 6 specific security test types |
| [6/18] | Performance Tests | 5 specific performance test types |
| [7/18] | Contract Tests | API and GraphQL contract tests |
| [8/18] | Configuration Tests | 8 specific configuration test types |
| [9/18] | Infrastructure Tests | PowerShell-based infrastructure tests |
| [10/18] | API Tests | PowerShell-based API functionality tests |
| [11/18] | GraphQL Tests | PowerShell-based GraphQL tests |
| [12/18] | Compliance Tests | 5 specific compliance test types |
| [13/18] | Retention Tests | 5 specific retention policy test types |
| [14/18] | Document Sharing Tests | Document and bulk sharing tests |
| [15/18] | Extended File Processing | Async and file processing tests |
| [16/18] | Virus Scanning Tests | 8 specific virus scanning test types |
| [17/18] | Document Conversion Tests | 10 specific conversion test types |
| [18/18] | Documentation Tests | 6 specific documentation test types |

### Key Command Line Options

```batch
# Basic Usage
run-all-tests.bat                    # Run all 18 test phases
run-all-tests.bat --unit-only        # Run only unit tests
run-all-tests.bat --integration-only # Run only integration tests
run-all-tests.bat --fail-fast        # Stop on first failure
run-all-tests.bat --no-reports       # Skip report generation
run-all-tests.bat --help             # Show help

# Specific Categories
run-all-tests.bat --security-only     # Security tests only
run-all-tests.bat --sharing-only      # Document sharing tests only
run-all-tests.bat --documentation-only # Documentation tests only
```

### Non-Interactive Flags Added

**Maven Commands**:
- `--batch-mode` - Prevents interactive prompts
- `-Dmaven.test.failure.ignore=true` - Continues on test failures
- `-q` - Quiet mode to reduce output

**PowerShell Commands**:
- `-NonInteractive` - Prevents interactive prompts
- `-ExecutionPolicy Bypass` - Allows script execution

### Report Locations

```
target/test-reports/[timestamp]/
├── surefire-reports/          # HTML test reports
├── coverage/index.html        # JaCoCo coverage report
└── test-execution.log         # Detailed execution log

Direct Links:
- file:///D:/MyDevelopment/dms-svc/target/site/surefire-report.html
- file:///D:/MyDevelopment/dms-svc/target/site/jacoco/index.html
```

### Troubleshooting

**Script Hangs?**
- Check for missing `--batch-mode` or `-NonInteractive` flags
- Verify no interactive prompts in custom scripts

**Tests Not Running?**
- Verify test file naming matches patterns
- Check Maven can compile test files
- Ensure correct directory structure

**Reports Missing?**
- Check Maven site plugin configuration
- Verify JaCoCo plugin setup
- Ensure sufficient disk space

### Test Coverage Patterns

**Unit Tests**: `**/*Test,!**/*IntegrationTest,!**/*E2ETest,!**/*SecurityTest,!**/*PerformanceTest,!**/*ContractTest`

**Integration Tests**: Includes 17 specific types like `DocumentSharingIntegrationTest`, `ElasticsearchIntegrationTest`, etc.

**Security Tests**: Includes `AuditSecurityTest`, `PIIEncryptionServiceTest`, `JwtTokenProviderLoggingTest`, etc.

**Virus Scanning**: Includes `MockVirusScannerTest`, `VirusScannerFactoryTest`, `BulkUploadServiceTest`, etc.

### For Developers

When adding new tests:
1. Follow existing naming conventions
2. Place in appropriate directory structure
3. Update test patterns if needed
4. Test with `--unit-only` first

### Documentation References

- **Full Documentation**: `docs/TEST_SUITE_IMPROVEMENTS_DOCUMENTATION.md`
- **Testing Guide**: `scripts/TESTING_GUIDE.md`
- **Script Location**: `scripts/run-all-tests.bat`

---
*Last Updated: June 28, 2025 - Version 2.1*
