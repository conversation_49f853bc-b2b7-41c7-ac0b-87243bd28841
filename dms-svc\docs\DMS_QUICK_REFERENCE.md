# DMS Service - Quick Reference

## Authentication
```graphql
mutation GenerateTestToken($input: JwtTokenRequest!) {
  generateTestToken(input: $input) {
    token
    tokenType
    expiresAt
  }
}
```
**Variables:** `{"input": {"username": "test-user", "roles": ["USER"], "permissions": ["READ", "WRITE"]}}`

## Upload Document
```graphql
mutation UploadDocumentEnhanced($input: EnhancedDocumentUploadInput!) {
  uploadDocumentEnhanced(input: $input) {
    success
    document { id name version status }
    uploadId
    fileName
    message
  }
}
```

## Download Document (REST)
```bash
# Get download info
GET /api/v2/documents/{id}/download/info

# Download file
GET /api/v2/documents/{id}/download
```

## Search Documents
```graphql
query SearchDocuments($filter: DocumentSearchInput, $pagination: PaginationInput) {
  searchDocuments(filter: $filter, pagination: $pagination) {
    content { id name version status createdDate }
    totalElements
  }
}
```

## Common Headers
- **GraphQL:** `{"Authorization": "Bearer TOKEN", "Content-Type": "application/json"}`
- **Upload:** `{"Authorization": "Bearer TOKEN"}` (no Content-Type)
- **Download:** `{"Authorization": "Bearer TOKEN"}`

## Endpoints
- **GraphQL:** `http://localhost:9093/graphql`
- **GraphiQL:** `http://localhost:9093/graphiql`
- **Download:** `http://localhost:9093/api/v2/documents/{id}/download`

## Angular Service Template
```typescript
@Injectable()
export class DmsService {
  private graphqlUrl = 'http://localhost:9093/graphql';
  
  uploadDocument(file: File, metadata: any): Observable<any> {
    const formData = new FormData();
    formData.append('operations', JSON.stringify({
      query: 'mutation UploadDocumentEnhanced($input: EnhancedDocumentUploadInput!) { ... }',
      variables: { input: { file: null, ...metadata } }
    }));
    formData.append('map', JSON.stringify({"0": ["variables.input.file"]}));
    formData.append('0', file);
    
    return this.http.post(this.graphqlUrl, formData, {
      headers: new HttpHeaders({ 'Authorization': `Bearer ${token}` })
    });
  }
}