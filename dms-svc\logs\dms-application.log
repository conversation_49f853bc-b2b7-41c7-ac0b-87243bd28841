{"@timestamp":"2025-07-31T11:25:31.0845447Z","@version":"1","level":"INFO","message":"Starting DmsSvcApplication using Java 21.0.7 with PID 23680 (D:\\GRC CODE\\dms-svc\\target\\classes started by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in D:\\GRC CODE\\dms-svc)","service":"dms-svc","environment":"local","version":"unknown","correlationId":"","traceId":"","spanId":"","thread":"main","logger":"com.ascentbusiness.dms_svc.DmsSvcApplication"}
{"@timestamp":"2025-07-31T11:25:31.1646215Z","@version":"1","level":"INFO","message":"No active profile set, falling back to 1 default profile: \"default\"","service":"dms-svc","environment":"local","version":"unknown","correlationId":"","traceId":"","spanId":"","thread":"main","logger":"com.ascentbusiness.dms_svc.DmsSvcApplication"}
{"@timestamp":"2025-07-31T11:26:04.4485461Z","@version":"1","level":"INFO","message":"Starting JWT secret validation...","service":"dms-svc","environment":"local","version":"unknown","correlationId":"","traceId":"","spanId":"","thread":"main","logger":"com.ascentbusiness.dms_svc.security.JwtTokenProvider"}
{"@timestamp":"2025-07-31T11:26:04.4491131Z","@version":"1","level":"INFO","message":"JWT secret validation passed. Key length: 103 bytes (824 bits)","service":"dms-svc","environment":"local","version":"unknown","correlationId":"","traceId":"","spanId":"","thread":"main","logger":"com.ascentbusiness.dms_svc.security.JwtTokenProvider"}
{"@timestamp":"2025-07-31T11:26:05.1130821Z","@version":"1","level":"INFO","message":"SecurityHeadersFilter initialized","service":"dms-svc","environment":"local","version":"unknown","correlationId":"","traceId":"","spanId":"","thread":"main","logger":"com.ascentbusiness.dms_svc.filter.SecurityHeadersFilter"}
{"@timestamp":"2025-07-31T11:26:50.2740018Z","@version":"1","level":"INFO","message":"SecurityHeadersFilter destroyed","service":"dms-svc","environment":"local","version":"unknown","correlationId":"","traceId":"","spanId":"","thread":"main","logger":"com.ascentbusiness.dms_svc.filter.SecurityHeadersFilter"}
