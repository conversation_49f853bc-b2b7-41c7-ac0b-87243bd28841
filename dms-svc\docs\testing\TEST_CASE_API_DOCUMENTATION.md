# DMS Test Case API Documentation

## Overview

The DMS Test Case API provides comprehensive access to all test case specifications via REST endpoints. This API serves CSV-based test case data in JSON format, enabling testers to access all test cases in one centralized location without authentication requirements.

## Base URL
```
http://localhost:9093/api/test-cases
```

## API Features

- **No Authentication Required**: All endpoints are publicly accessible for testers
- **CORS Enabled**: Cross-origin requests supported for testing tools
- **Caching**: Test case data is cached for improved performance
- **Comprehensive Coverage**: All 77 test cases across 15 categories
- **Search Functionality**: Full-text search across all test cases
- **Real-time Data**: Reflects latest CSV file changes

## Available Endpoints

### 1. Test Case Overview
```http
GET /api/test-cases
```

**Description**: Get overview of all test categories with counts and descriptions.

**Response**:
```json
{
  "totalCategories": 15,
  "totalTestCases": 77,
  "categories": [
    {
      "categoryName": "No_Access",
      "displayName": "No Access",
      "testRange": "01-05",
      "testCount": 5,
      "coverageAreas": "Token validation",
      "description": "No authentication / Invalid tokens"
    }
  ],
  "availableCategories": ["No_Access", "READ_Permission", ...]
}
```

### 2. Test Categories Summary
```http
GET /api/test-cases/summary
```

**Description**: Get detailed summary of all test categories.

### 3. Available Categories
```http
GET /api/test-cases/categories
```

**Description**: Get list of all available test categories.

**Response**:
```json
["No_Access", "READ_Permission", "WRITE_Permission", "DELETE_Permission", ...]
```

### 4. Category-Specific Endpoints

#### No Access Test Cases
```http
GET /api/test-cases/no-access
```

#### READ Permission Test Cases
```http
GET /api/test-cases/read-permission
```

#### WRITE Permission Test Cases
```http
GET /api/test-cases/write-permission
```

#### DELETE Permission Test Cases
```http
GET /api/test-cases/delete-permission
```

#### ADMIN Permission Test Cases
```http
GET /api/test-cases/admin-permission
```

#### Creator Privileges Test Cases
```http
GET /api/test-cases/creator-privileges
```

#### Multi Role Test Cases
```http
GET /api/test-cases/multi-role
```

#### Error Handling Test Cases
```http
GET /api/test-cases/error-handling
```

#### Storage Providers Test Cases
```http
GET /api/test-cases/storage-providers
```

#### Search Filter Test Cases
```http
GET /api/test-cases/search-filter
```

#### Audit Logs Test Cases
```http
GET /api/test-cases/audit-logs
```

#### Security Validation Test Cases
```http
GET /api/test-cases/security-validation
```

#### Performance Test Cases
```http
GET /api/test-cases/performance
```

#### Integration Test Cases
```http
GET /api/test-cases/integration
```

#### Boundary Tests Test Cases
```http
GET /api/test-cases/boundary-tests
```

**Category Response Format**:
```json
{
  "category": "READ_Permission",
  "displayName": "READ Permission",
  "totalTests": 7,
  "testRange": "6-12",
  "coverageAreas": "Query operations",
  "description": "READ permission allowed/forbidden operations",
  "testCases": [
    {
      "serialNumber": "6",
      "category": "READ_Permission",
      "jwtRequest": "mutation { generateTestToken(...) }",
      "jwtToken": "eyJhbGciOiJIUzI1NiJ9...",
      "request": "query { getDocumentById(...) }",
      "response": "{ \"data\": { \"getDocumentById\": {...} } }",
      "result": "PASS",
      "comment": "User with READ permission can view document details",
      "description": "User with READ permission can view document details"
    }
  ]
}
```

### 5. Generic Category Endpoint
```http
GET /api/test-cases/{category}
```

**Parameters**:
- `category` (path): Category name (e.g., "READ_Permission", "No_Access")

### 6. Individual Test Case
```http
GET /api/test-cases/{category}/{testId}
```

**Parameters**:
- `category` (path): Category name
- `testId` (path): Test case serial number

**Response**:
```json
{
  "serialNumber": "6",
  "category": "READ_Permission",
  "jwtRequest": "mutation { generateTestToken(input: { username: \"ReadUser\", roles: [\"VIEWER\"], permissions: [\"READ\"] }) { token tokenType expiresAt } }",
  "jwtToken": "eyJhbGciOiJIUzI1NiJ9.READ_USER_TOKEN",
  "request": "query { getDocumentById(id: \"1\") { id name version status accessRoles { role permission } } }",
  "response": "{ \"data\": { \"getDocumentById\": { \"id\": \"1\", \"name\": \"Sample Document\", \"version\": 1, \"status\": \"ACTIVE\", \"accessRoles\": [{ \"role\": \"VIEWER\", \"permission\": \"READ\" }] } } }",
  "result": "PASS",
  "comment": "User with READ permission can view document details",
  "description": "User with READ permission can view document details"
}
```

### 7. Search Test Cases
```http
GET /api/test-cases/search?q={query}
```

**Parameters**:
- `q` (query, optional): Search query string

**Response**:
```json
{
  "query": "READ",
  "totalResults": 12,
  "testCases": [
    // Array of matching test cases
  ]
}
```

### 8. Health Check
```http
GET /api/test-cases/health
```

**Response**:
```json
{
  "status": "UP",
  "service": "Test Case API",
  "timestamp": 1640995200000,
  "availableCategories": 15,
  "categories": ["No_Access", "READ_Permission", ...]
}
```

## Test Case Data Structure

Each test case contains the following fields:

| Field | Type | Description |
|-------|------|-------------|
| `serialNumber` | String | Unique test case identifier |
| `category` | String | Test category name |
| `jwtRequest` | String | GraphQL mutation to generate JWT token |
| `jwtToken` | String | JWT token for authentication |
| `request` | String | GraphQL query/mutation to test |
| `response` | String | Expected GraphQL response |
| `result` | String | Expected test result (PASS/FAIL) |
| `comment` | String | Test case description |
| `description` | String | Same as comment for consistency |

## Test Categories Coverage

| Category | Test Range | Count | Coverage Areas |
|----------|------------|-------|----------------|
| No Access | 01-05 | 5 | Token validation |
| READ Permission | 06-12 | 7 | Query operations |
| WRITE Permission | 13-20 | 8 | Upload operations |
| DELETE Permission | 21-25 | 5 | Document deletion |
| ADMIN Permission | 26-30 | 5 | Full access |
| Creator Privileges | 31-35 | 5 | Owner rights |
| Multi Role | 36-39 | 4 | Role combinations |
| Error Handling | 40-48 | 9 | Input validation |
| Storage Providers | 49-53 | 5 | Provider operations |
| Search Filter | 54-58 | 5 | Complex queries |
| Audit Logs | 59-62 | 4 | Log access |
| Security Validation | 63-67 | 5 | Security boundaries |
| Performance | 68-69 | 2 | Load testing |
| Integration | 70-74 | 5 | End-to-end |
| Boundary Tests | 75-77 | 3 | System limits |

## GraphQL Operations Covered

### Mutations (6)
- `uploadDocument`
- `uploadDocumentFromPath`
- `uploadDocumentNewVersion`
- `uploadDocumentNewVersionFromPath`
- `deleteDocument`
- `generateTestToken`

### Queries (5)
- `getDocumentById`
- `listDocumentVersions`
- `searchDocuments`
- `getAuditLogsByFilter`
- `downloadDocument`

## Permission Matrix Coverage

✓ READ, WRITE, DELETE, ADMIN permissions  
✓ Creator privileges and ownership rules  
✓ Role-based access and inheritance  
✓ Multi-role and permission combinations  
✓ Document access role enforcement  

## System Features Tested

✓ All storage providers (LOCAL, S3, SHAREPOINT)  
✓ Document versioning and status management  
✓ Audit logging and security monitoring  
✓ Input validation and error handling  
✓ Concurrent access and race conditions  
✓ Search and filtering capabilities  
✓ Security boundaries and attack prevention  

## Usage Examples

### Using cURL

```bash
# Get all test cases overview
curl http://localhost:9093/api/test-cases

# Get READ permission test cases
curl http://localhost:9093/api/test-cases/read-permission

# Get specific test case
curl http://localhost:9093/api/test-cases/READ_Permission/6

# Search test cases
curl "http://localhost:9093/api/test-cases/search?q=READ"

# Health check
curl http://localhost:9093/api/test-cases/health
```

### Using Postman

1. Create a new collection for "DMS Test Cases"
2. Add requests for each endpoint
3. Use environment variables for base URL
4. Create test scripts to validate responses

### Integration with Testing Tools

The API can be integrated with various testing tools:

- **Postman**: Import collection and run automated tests
- **Newman**: Command-line testing with Postman collections
- **REST Assured**: Java-based API testing
- **Custom Scripts**: Use any HTTP client to fetch test data

## Configuration

The test case directory can be configured via application properties:

```properties
# Test Case Configuration
testcase.directory=tests/test-cases
```

## Caching

Test case data is cached for performance:
- Cache type: Redis (configurable)
- TTL: 1 hour (configurable)
- Cache keys: `testCaseSummary`, `testCasesByCategory`, `testCaseById`

## Error Handling

The API returns appropriate HTTP status codes:

- `200 OK`: Successful request
- `404 Not Found`: Category or test case not found
- `500 Internal Server Error`: Server error

## Security

- **No Authentication**: API is publicly accessible
- **CORS Enabled**: Supports cross-origin requests
- **Read-Only**: API only provides read access to test data
- **No Sensitive Data**: API serves test specifications only

## Development Notes

- CSV files are the source of truth
- Changes to CSV files are reflected in API responses
- Service includes proper error handling and logging
- API follows REST conventions and best practices

## Future Enhancements

Potential improvements for the Test Case API:

1. **Real-time Updates**: WebSocket support for live CSV changes
2. **Export Features**: Download test cases in various formats
3. **Test Execution**: Direct API test execution capabilities
4. **Filtering**: Advanced filtering by result, category, etc.
5. **Analytics**: Test execution statistics and reporting
6. **Validation**: CSV format validation and error reporting

## Support

For questions or issues with the Test Case API:

1. Check the health endpoint: `/api/test-cases/health`
2. Review application logs for errors
3. Verify CSV file format and location
4. Ensure proper configuration in application.properties
