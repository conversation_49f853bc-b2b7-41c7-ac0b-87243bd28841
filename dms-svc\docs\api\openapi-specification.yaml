openapi: 3.0.3
info:
  title: Document Management Service (DMS) API
  description: |
    Comprehensive Document Management Service API providing document operations, 
    metadata management, security, audit, and administrative functions.
    
    ## Features
    - Document upload, download, versioning, and management
    - Comprehensive metadata management (classification, ownership, compliance)
    - Security and audit logging with tamper-proof chains
    - Role-based access control (RBAC)
    - Multi-storage provider support (Local, AWS S3, SharePoint)
    - Full-text search with Elasticsearch integration
    - Business features: workflows, templates, webhooks
    
    ## Authentication
    All endpoints require JWT Bearer token authentication except public endpoints.
    
    ## Rate Limiting
    API endpoints are rate-limited to prevent abuse. Limits vary by endpoint and user role.
    
  version: 1.0.0
  contact:
    name: DMS API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:9092/api/v1
    description: Development server
  - url: https://dms-api.company.com/api/v1
    description: Production server

security:
  - BearerAuth: []

paths:
  /health:
    get:
      tags:
        - System
      summary: Health check endpoint
      description: Check the health status of the DMS service
      security: []
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: UP
                  timestamp:
                    type: string
                    format: date-time
                  version:
                    type: string
                    example: 1.0.0
                  components:
                    type: object
                    properties:
                      database:
                        type: object
                        properties:
                          status:
                            type: string
                            example: UP
                      redis:
                        type: object
                        properties:
                          status:
                            type: string
                            example: UP
                      elasticsearch:
                        type: object
                        properties:
                          status:
                            type: string
                            example: UP

  /documents/{id}/download:
    get:
      tags:
        - Documents
      summary: Download document
      description: Download a document by its ID
      parameters:
        - name: id
          in: path
          required: true
          description: Document ID
          schema:
            type: integer
            format: int64
            example: 123
      responses:
        '200':
          description: Document downloaded successfully
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              description: Attachment filename
              schema:
                type: string
                example: attachment; filename="document.pdf"
            Content-Type:
              description: MIME type of the document
              schema:
                type: string
                example: application/pdf
            Content-Length:
              description: Size of the document in bytes
              schema:
                type: integer
                example: 1048576
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /documents/{id}/metadata:
    get:
      tags:
        - Documents
      summary: Get document metadata
      description: Retrieve comprehensive metadata for a document
      parameters:
        - name: id
          in: path
          required: true
          description: Document ID
          schema:
            type: integer
            format: int64
            example: 123
      responses:
        '200':
          description: Document metadata retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentMetadata'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  /audit/logs:
    get:
      tags:
        - Audit
      summary: Get audit logs
      description: Retrieve audit logs with filtering and pagination
      parameters:
        - name: documentId
          in: query
          description: Filter by document ID
          schema:
            type: integer
            format: int64
        - name: userId
          in: query
          description: Filter by user ID
          schema:
            type: string
        - name: action
          in: query
          description: Filter by audit action
          schema:
            $ref: '#/components/schemas/AuditAction'
        - name: dateFrom
          in: query
          description: Filter from date (ISO 8601)
          schema:
            type: string
            format: date-time
        - name: dateTo
          in: query
          description: Filter to date (ISO 8601)
          schema:
            type: string
            format: date-time
        - name: page
          in: query
          description: Page number (0-based)
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: size
          in: query
          description: Page size
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
        - name: sortBy
          in: query
          description: Sort field
          schema:
            type: string
            default: timestamp
        - name: sortDirection
          in: query
          description: Sort direction
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
      responses:
        '200':
          description: Audit logs retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLogPage'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /audit/export:
    post:
      tags:
        - Audit
      summary: Export audit logs
      description: Export audit logs in various formats (PDF, CSV, JSON, Excel)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuditExportRequest'
      responses:
        '200':
          description: Export initiated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExportResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '400':
          $ref: '#/components/responses/BadRequest'

  /security/violations:
    get:
      tags:
        - Security
      summary: Get security violations
      description: Retrieve security violations with filtering and pagination
      parameters:
        - name: documentId
          in: query
          description: Filter by document ID
          schema:
            type: integer
            format: int64
        - name: userId
          in: query
          description: Filter by user ID
          schema:
            type: string
        - name: violationType
          in: query
          description: Filter by violation type
          schema:
            $ref: '#/components/schemas/SecurityViolationType'
        - name: severity
          in: query
          description: Filter by severity level
          schema:
            $ref: '#/components/schemas/ViolationSeverity'
        - name: resolved
          in: query
          description: Filter by resolution status
          schema:
            type: boolean
        - name: page
          in: query
          description: Page number (0-based)
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: size
          in: query
          description: Page size
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
      responses:
        '200':
          description: Security violations retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecurityViolationPage'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /test-cases:
    get:
      tags:
        - Testing
      summary: Get test cases overview
      description: Retrieve overview of all test case categories (development only)
      security: []
      responses:
        '200':
          description: Test cases overview retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestCasesOverview'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication. Include the token in the Authorization header:
        `Authorization: Bearer <your-jwt-token>`

  schemas:
    DocumentMetadata:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 123
        name:
          type: string
          example: "Financial Report Q4 2024"
        description:
          type: string
          example: "Quarterly financial report for Q4 2024"
        version:
          type: integer
          example: 1
        status:
          $ref: '#/components/schemas/DocumentStatus'
        storageProvider:
          $ref: '#/components/schemas/StorageProvider'
        storagePath:
          type: string
          example: "/documents/2024/12/financial-report-q4.pdf"
        originalFileName:
          type: string
          example: "financial-report-q4.pdf"
        fileSize:
          type: integer
          format: int64
          example: 1048576
        mimeType:
          type: string
          example: "application/pdf"
        createdDate:
          type: string
          format: date-time
          example: "2024-12-01T10:30:00Z"
        lastModifiedDate:
          type: string
          format: date-time
          example: "2024-12-01T10:30:00Z"
        creatorUserId:
          type: string
          example: "<EMAIL>"
        tags:
          type: array
          items:
            type: string
          example: ["finance", "report", "Q4", "2024"]
        classificationMetadata:
          $ref: '#/components/schemas/ClassificationMetadata'
        ownershipMetadata:
          $ref: '#/components/schemas/OwnershipMetadata'
        complianceMetadata:
          $ref: '#/components/schemas/ComplianceMetadata'

    ClassificationMetadata:
      type: object
      properties:
        id:
          type: integer
          format: int64
        module:
          type: string
          example: "Finance"
        subModule:
          type: string
          example: "Reporting"
        confidentialityLevel:
          type: string
          example: "Confidential"
        dataClassification:
          type: string
          example: "Sensitive"
        securityLevel:
          type: string
          example: "High"
        accessRestrictions:
          type: string
          example: "Finance team only"
        handlingInstructions:
          type: string
          example: "Handle with care, do not distribute"

    OwnershipMetadata:
      type: object
      properties:
        id:
          type: integer
          format: int64
        owner:
          type: string
          example: "<EMAIL>"
        department:
          type: string
          example: "Finance"
        businessUnit:
          type: string
          example: "Corporate Finance"
        expiryDate:
          type: string
          format: date-time
          example: "2025-12-31T23:59:59Z"
        renewalReminder:
          type: string
          format: date-time
          example: "2025-11-30T09:00:00Z"
        retentionPeriod:
          type: string
          example: "7 years"
        disposalMethod:
          type: string
          example: "Secure deletion"
        archivalDate:
          type: string
          format: date-time
          example: "2031-12-31T23:59:59Z"

    ComplianceMetadata:
      type: object
      properties:
        id:
          type: integer
          format: int64
        complianceStandard:
          type: string
          example: "SOX"
        auditRelevance:
          type: string
          example: "High"
        linkedRisksControls:
          type: string
          example: "Financial reporting controls"
        controlId:
          type: string
          example: "CTRL-FIN-001"
        thirdPartyId:
          type: string
          example: "VENDOR-123"
        policyId:
          type: string
          example: "POL-FIN-001"

    AuditLogPage:
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/AuditLog'
        totalElements:
          type: integer
          format: int64
          example: 1000
        totalPages:
          type: integer
          example: 100
        size:
          type: integer
          example: 10
        number:
          type: integer
          example: 0
        first:
          type: boolean
          example: true
        last:
          type: boolean
          example: false

    AuditLog:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 12345
        documentId:
          type: integer
          format: int64
          example: 123
        action:
          $ref: '#/components/schemas/AuditAction'
        userId:
          type: string
          example: "<EMAIL>"
        details:
          type: string
          example: "Document uploaded successfully"
        timestamp:
          type: string
          format: date-time
          example: "2024-12-01T10:30:00Z"
        correlationId:
          type: string
          example: "req-123e4567-e89b-12d3-a456-426614174000"
        ipAddress:
          type: string
          example: "*************"
        userAgent:
          type: string
          example: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        eventType:
          $ref: '#/components/schemas/AuditEventType'
        eventCategory:
          $ref: '#/components/schemas/AuditEventCategory'
        riskLevel:
          type: string
          example: "LOW"
        chainSequence:
          type: integer
          format: int64
          example: 12345
        previousHash:
          type: string
          example: "a1b2c3d4e5f6..."
        currentHash:
          type: string
          example: "f6e5d4c3b2a1..."

    SecurityViolationPage:
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/SecurityViolation'
        totalElements:
          type: integer
          format: int64
        totalPages:
          type: integer
        size:
          type: integer
        number:
          type: integer
        first:
          type: boolean
        last:
          type: boolean

    SecurityViolation:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 456
        documentId:
          type: integer
          format: int64
          example: 123
        userId:
          type: string
          example: "<EMAIL>"
        violationType:
          $ref: '#/components/schemas/SecurityViolationType'
        severity:
          $ref: '#/components/schemas/ViolationSeverity'
        violationDetails:
          type: string
          example: "Attempted unauthorized access to confidential document"
        correlationId:
          type: string
          example: "req-123e4567-e89b-12d3-a456-426614174000"
        ipAddress:
          type: string
          example: "*************"
        userAgent:
          type: string
          example: "curl/7.68.0"
        timestamp:
          type: string
          format: date-time
          example: "2024-12-01T15:45:00Z"
        resolved:
          type: boolean
          example: false
        resolvedBy:
          type: string
          example: "<EMAIL>"
        resolvedDate:
          type: string
          format: date-time
          example: "2024-12-01T16:00:00Z"
        resolutionNotes:
          type: string
          example: "False positive - legitimate access attempt"

    AuditExportRequest:
      type: object
      required:
        - format
      properties:
        format:
          $ref: '#/components/schemas/ExportFormat'
        filter:
          type: object
          properties:
            documentId:
              type: integer
              format: int64
            userId:
              type: string
            action:
              $ref: '#/components/schemas/AuditAction'
            dateFrom:
              type: string
              format: date-time
            dateTo:
              type: string
              format: date-time
            riskLevel:
              type: string

    ExportResponse:
      type: object
      properties:
        downloadUrl:
          type: string
          example: "https://dms.company.com/exports/audit-logs-20241201.pdf"
        fileName:
          type: string
          example: "audit-logs-20241201.pdf"
        fileSize:
          type: integer
          format: int64
          example: 2048576
        expiresAt:
          type: string
          format: date-time
          example: "2024-12-02T10:30:00Z"

    TestCasesOverview:
      type: object
      properties:
        totalCategories:
          type: integer
          example: 11
        totalTestCases:
          type: integer
          example: 77
        categories:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: "READ_Permission"
              count:
                type: integer
                example: 7
              description:
                type: string
                example: "Test cases for READ permission scenarios"

    # Enums
    DocumentStatus:
      type: string
      enum:
        - ACTIVE
        - HISTORICAL
        - DELETED
      example: ACTIVE

    StorageProvider:
      type: string
      enum:
        - LOCAL
        - S3
        - SHAREPOINT
      example: LOCAL

    AuditAction:
      type: string
      enum:
        - UPLOAD
        - DOWNLOAD
        - UPDATE
        - DELETE
        - VIEW
        - PERMISSION_GRANT
        - PERMISSION_REVOKE
        - LOGIN
        - LOGOUT
        - SECURITY_VIOLATION
        - COMPLIANCE_CHECK
      example: UPLOAD

    AuditEventType:
      type: string
      enum:
        - DOCUMENT_OPERATION
        - SECURITY_EVENT
        - COMPLIANCE_EVENT
        - SYSTEM_EVENT
        - USER_EVENT
      example: DOCUMENT_OPERATION

    AuditEventCategory:
      type: string
      enum:
        - CREATE
        - READ
        - UPDATE
        - DELETE
        - SECURITY
        - COMPLIANCE
        - ADMINISTRATIVE
      example: CREATE

    SecurityViolationType:
      type: string
      enum:
        - UNAUTHORIZED_ACCESS
        - PERMISSION_VIOLATION
        - SUSPICIOUS_ACTIVITY
        - DATA_BREACH
        - POLICY_VIOLATION
        - AUTHENTICATION_FAILURE
        - RATE_LIMIT_EXCEEDED
      example: UNAUTHORIZED_ACCESS

    ViolationSeverity:
      type: string
      enum:
        - LOW
        - MEDIUM
        - HIGH
        - CRITICAL
      example: HIGH

    ExportFormat:
      type: string
      enum:
        - PDF
        - CSV
        - JSON
        - EXCEL
      example: PDF

    Error:
      type: object
      properties:
        error:
          type: string
          example: "DOCUMENT_NOT_FOUND"
        message:
          type: string
          example: "Document with ID 123 not found"
        timestamp:
          type: string
          format: date-time
          example: "2024-12-01T10:30:00Z"
        path:
          type: string
          example: "/api/v1/documents/123/download"
        correlationId:
          type: string
          example: "req-123e4567-e89b-12d3-a456-426614174000"

  responses:
    BadRequest:
      description: Bad request - invalid input parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "INVALID_INPUT"
            message: "Invalid document ID format"
            timestamp: "2024-12-01T10:30:00Z"
            path: "/api/v1/documents/invalid/download"

    Unauthorized:
      description: Unauthorized - missing or invalid JWT token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "UNAUTHORIZED"
            message: "JWT token is missing or invalid"
            timestamp: "2024-12-01T10:30:00Z"
            path: "/api/v1/documents/123/download"

    Forbidden:
      description: Forbidden - insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "FORBIDDEN"
            message: "Insufficient permissions to access this document"
            timestamp: "2024-12-01T10:30:00Z"
            path: "/api/v1/documents/123/download"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "DOCUMENT_NOT_FOUND"
            message: "Document with ID 123 not found"
            timestamp: "2024-12-01T10:30:00Z"
            path: "/api/v1/documents/123/download"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "INTERNAL_SERVER_ERROR"
            message: "An unexpected error occurred"
            timestamp: "2024-12-01T10:30:00Z"
            path: "/api/v1/documents/123/download"

tags:
  - name: System
    description: System health and status endpoints
  - name: Documents
    description: Document management operations
  - name: Audit
    description: Audit logging and compliance operations
  - name: Security
    description: Security monitoring and violation management
  - name: Testing
    description: Testing and development utilities
