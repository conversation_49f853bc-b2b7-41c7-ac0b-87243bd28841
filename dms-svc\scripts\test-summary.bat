@echo off
REM DMS Service - Test Summary Script
REM Quick overview of available tests and recent results

echo ========================================
echo DMS Service Test Summary
echo ========================================
echo.

REM Set variables
set PROJECT_ROOT=%~dp0..
set SCRIPT_DIR=%~dp0

echo Project Root: %PROJECT_ROOT%
echo.

REM Count test files by category
echo Test File Inventory:
echo ========================================

REM Count unit tests
for /f %%i in ('dir /s /b "%PROJECT_ROOT%\src\test\java\*Test.java" 2^>nul ^| find /c /v ""') do set UNIT_COUNT=%%i

REM Count integration tests  
for /f %%i in ('dir /s /b "%PROJECT_ROOT%\src\test\java\*IntegrationTest.java" 2^>nul ^| find /c /v ""') do set INTEGRATION_COUNT=%%i

REM Count E2E tests
for /f %%i in ('dir /s /b "%PROJECT_ROOT%\src\test\java\*E2ETest.java" 2^>nul ^| find /c /v ""') do set E2E_COUNT=%%i

REM Count security tests
for /f %%i in ('dir /s /b "%PROJECT_ROOT%\src\test\java\*SecurityTest.java" 2^>nul ^| find /c /v ""') do set SECURITY_COUNT=%%i

REM Count performance tests
for /f %%i in ('dir /s /b "%PROJECT_ROOT%\src\test\java\*PerformanceTest.java" 2^>nul ^| find /c /v ""') do set PERFORMANCE_COUNT=%%i

REM Count compliance tests
for /f %%i in ('dir /s /b "%PROJECT_ROOT%\src\test\java\*Compliance*Test.java" 2^>nul ^| find /c /v ""') do set COMPLIANCE_COUNT=%%i

REM Count retention tests
for /f %%i in ('dir /s /b "%PROJECT_ROOT%\src\test\java\*Retention*Test.java" 2^>nul ^| find /c /v ""') do set RETENTION_COUNT=%%i

REM Count infrastructure tests
for /f %%i in ('dir /s /b "%PROJECT_ROOT%\tests\infrastructure\*.java" 2^>nul ^| find /c /v ""') do set INFRASTRUCTURE_COUNT=%%i

echo Unit Tests: %UNIT_COUNT%
echo Integration Tests: %INTEGRATION_COUNT%
echo E2E Tests: %E2E_COUNT%
echo Security Tests: %SECURITY_COUNT%
echo Performance Tests: %PERFORMANCE_COUNT%
echo Compliance Tests: %COMPLIANCE_COUNT%
echo Retention Tests: %RETENTION_COUNT%
echo Infrastructure Tests: %INFRASTRUCTURE_COUNT%

REM Calculate total
set /a TOTAL_TESTS=%UNIT_COUNT%+%INTEGRATION_COUNT%+%E2E_COUNT%+%SECURITY_COUNT%+%PERFORMANCE_COUNT%+%COMPLIANCE_COUNT%+%RETENTION_COUNT%+%INFRASTRUCTURE_COUNT%
echo.
echo Total Test Files: %TOTAL_TESTS%

echo.
echo Test Scripts Available:
echo ========================================

REM Check for test scripts
if exist "%SCRIPT_DIR%run-all-tests.bat" (
    echo ✓ Consolidated Test Runner: run-all-tests.bat
) else (
    echo ✗ Consolidated Test Runner: MISSING
)

if exist "%SCRIPT_DIR%run-infrastructure-tests.ps1" (
    echo ✓ Infrastructure Tests: run-infrastructure-tests.ps1
) else (
    echo ✗ Infrastructure Tests: MISSING
)

if exist "%SCRIPT_DIR%run-tests-with-coverage.sh" (
    echo ✓ Coverage Tests: run-tests-with-coverage.sh
) else (
    echo ✗ Coverage Tests: MISSING
)

if exist "%PROJECT_ROOT%\tests\scripts\test-api-functionality.ps1" (
    echo ✓ API Tests: test-api-functionality.ps1
) else (
    echo ✗ API Tests: MISSING
)

if exist "%PROJECT_ROOT%\tests\scripts\test-graphql.ps1" (
    echo ✓ GraphQL Tests: test-graphql.ps1
) else (
    echo ✗ GraphQL Tests: MISSING
)

echo.
echo Recent Test Results:
echo ========================================

REM Check for recent test reports
if exist "%PROJECT_ROOT%\target\test-reports" (
    echo Recent test reports found in target\test-reports\
    for /f "tokens=*" %%i in ('dir /b /od "%PROJECT_ROOT%\target\test-reports" 2^>nul') do set LATEST_REPORT=%%i
    if defined LATEST_REPORT (
        echo Latest report: %LATEST_REPORT%
        if exist "%PROJECT_ROOT%\target\test-reports\%LATEST_REPORT%\test-execution.log" (
            echo ✓ Execution log available
        )
        if exist "%PROJECT_ROOT%\target\test-reports\%LATEST_REPORT%\coverage\index.html" (
            echo ✓ Coverage report available
        )
    )
) else (
    echo No recent test reports found
    echo Run 'scripts\run-all-tests.bat' to generate reports
)

echo.
echo Quick Commands:
echo ========================================
echo Run all tests:           scripts\run-all-tests.bat
echo Run unit tests only:     scripts\run-all-tests.bat --unit-only
echo Run with fail-fast:      scripts\run-all-tests.bat --fail-fast
echo Run security tests:      scripts\run-all-tests.bat --security-only
echo Show help:               scripts\run-all-tests.bat --help
echo.
echo Infrastructure tests:    scripts\run-infrastructure-tests.ps1
echo Coverage analysis:       scripts\run-tests-with-coverage.sh
echo.

REM Check Maven and Java
echo Environment Check:
echo ========================================

where mvn >nul 2>&1
if %ERRORLEVEL%==0 (
    echo ✓ Maven is available
    for /f "tokens=3" %%i in ('mvn --version 2^>nul ^| findstr "Apache Maven"') do echo   Version: %%i
) else (
    echo ✗ Maven not found in PATH
)

where java >nul 2>&1
if %ERRORLEVEL%==0 (
    echo ✓ Java is available
    for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do echo   Version: %%i
) else (
    echo ✗ Java not found in PATH
)

where docker >nul 2>&1
if %ERRORLEVEL%==0 (
    echo ✓ Docker is available
) else (
    echo ⚠ Docker not found (optional for infrastructure tests)
)

REM Generate HTML test report if XML results exist
echo.
echo ========================================
echo HTML Test Report
echo ========================================
if exist "%PROJECT_ROOT%\target\surefire-reports\TEST-*.xml" (
    echo Generating HTML test report from existing XML results...
    powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%generate-simple-report.ps1"
    if %ERRORLEVEL% EQU 0 (
        echo ✓ HTML test report generated successfully
        echo   Report location: %PROJECT_ROOT%\target\site\surefire-report.html
        echo   Open in browser: file:///%PROJECT_ROOT%\target\site\surefire-report.html
    ) else (
        echo ✗ Failed to generate HTML test report
    )
) else (
    echo ⚠ No test XML results found. Run tests first to generate HTML report.
    echo   Use: scripts\run-all-tests.bat
)

echo.
echo ========================================
echo Test summary complete
echo ========================================
