# Secrets for DMS Service
# Note: In production, these should be created using external secret management tools
# like AWS Secrets Manager, Azure Key Vault, or HashiCorp Vault

apiVersion: v1
kind: Secret
metadata:
  name: dms-secrets
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: dms-system
type: Opaque
data:
  # Database credentials (base64 encoded)
  # Default values - MUST be changed in production
  SPRING_DATASOURCE_PASSWORD: ZG1zX3Bhc3N3b3Jk # dms_password
  
  # Redis password (base64 encoded)
  SPRING_REDIS_PASSWORD: cmVkaXNfcGFzc3dvcmQ= # redis_password
  
  # JWT Secret (base64 encoded)
  # This is a 256-bit secret key - MUST be changed in production
  JWT_SECRET: cHJvZFNlY3JldEtleTEyMzQ1Njc4OTAxMjM0NTY3ODkwMTIzNDU2Nzg5MEFCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFla
  
  # AWS credentials for S3 storage (base64 encoded)
  AWS_ACCESS_KEY_ID: WU9VUl9BV1NfQUNDRVNTX0tFWQ== # YOUR_AWS_ACCESS_KEY
  AWS_SECRET_ACCESS_KEY: WU9VUl9BV1NfU0VDUkVUX0tFWQ== # YOUR_AWS_SECRET_KEY
  
  # S3 Configuration (base64 encoded)
  DMS_STORAGE_S3_BUCKET_NAME: ZG1zLWRvY3VtZW50cy1wcm9k # dms-documents-prod
  DMS_STORAGE_S3_REGION: dXMtZWFzdC0x # us-east-1
  
  # SharePoint Integration (base64 encoded)
  AZURE_TENANT_ID: WU9VUl9BWlVSRV9URU5BTlRfSUQ= # YOUR_AZURE_TENANT_ID
  AZURE_CLIENT_ID: WU9VUl9BWlVSRV9DTElFTlRfSUQ= # YOUR_AZURE_CLIENT_ID
  AZURE_CLIENT_SECRET: WU9VUl9BWlVSRV9DTElFTlRfU0VDUkVU # YOUR_AZURE_CLIENT_SECRET
  
  # Grafana Admin Password (base64 encoded)
  GRAFANA_ADMIN_PASSWORD: YWRtaW4xMjM= # admin123
  GRAFANA_SECRET_KEY: Z3JhZmFuYVNlY3JldEtleTEyMw== # grafanaSecretKey123

---
# TLS Secret for HTTPS
apiVersion: v1
kind: Secret
metadata:
  name: dms-tls-secret
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: tls
    app.kubernetes.io/part-of: dms-system
type: kubernetes.io/tls
data:
  # TLS certificate and key (base64 encoded)
  # These should be replaced with actual certificates in production
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t # Your TLS certificate
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t # Your TLS private key

---
# Docker Registry Secret (if using private registry)
apiVersion: v1
kind: Secret
metadata:
  name: dms-registry-secret
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: registry
    app.kubernetes.io/part-of: dms-system
type: kubernetes.io/dockerconfigjson
data:
  # Docker registry credentials (base64 encoded)
  # Format: {"auths":{"registry-url":{"username":"user","password":"pass","auth":"base64(user:pass)"}}}
  .dockerconfigjson: eyJhdXRocyI6e319 # Empty auth config - replace with actual registry credentials

---
# Database Secret for MySQL
apiVersion: v1
kind: Secret
metadata:
  name: mysql-secret
  namespace: dms-system
  labels:
    app.kubernetes.io/name: mysql
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: dms-system
type: Opaque
data:
  # MySQL root password (base64 encoded)
  MYSQL_ROOT_PASSWORD: cm9vdF9wYXNzd29yZA== # root_password
  
  # MySQL database name (base64 encoded)
  MYSQL_DATABASE: ZG1zX3Byb2Q= # dms_prod
  
  # MySQL user credentials (base64 encoded)
  MYSQL_USER: ZG1zX3VzZXI= # dms_user
  MYSQL_PASSWORD: ZG1zX3Bhc3N3b3Jk # dms_password

---
# Redis Secret
apiVersion: v1
kind: Secret
metadata:
  name: redis-secret
  namespace: dms-system
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
    app.kubernetes.io/part-of: dms-system
type: Opaque
data:
  # Redis password (base64 encoded)
  REDIS_PASSWORD: cmVkaXNfcGFzc3dvcmQ= # redis_password
