# DMS Security Configuration Guide

## Overview

This document outlines the security measures implemented in the DMS service and provides guidance for secure configuration in different environments.

## Security Features Implemented

### 1. Environment-Based Configuration
- **Hardcoded credentials removed** from configuration files
- **Environment variables** used for sensitive configuration
- **Profile-specific** configurations for different environments

### 2. Input Validation & Sanitization
- **Comprehensive input validation** for all user inputs
- **SQL injection prevention** through pattern detection
- **XSS protection** with input sanitization
- **Path traversal protection** for file operations
- **File size and type validation**

### 3. HTTP Security Headers
- **Content Security Policy (CSP)** to prevent XSS attacks
- **X-Frame-Options** to prevent clickjacking
- **X-Content-Type-Options** to prevent MIME sniffing
- **Strict-Transport-Security** for HTTPS enforcement
- **Referrer-Policy** for privacy protection
- **Permissions-Policy** to restrict browser features

### 4. CORS Configuration
- **Restrictive CORS** settings for production
- **Configurable allowed origins** via environment variables
- **Specific allowed headers** instead of wildcards

### 5. JWT Security
- **Configurable JWT secrets** via environment variables
- **Token expiration** configuration
- **Proper token validation** and error handling

## Environment Configuration

### Local Development

Use the provided setup scripts:

**Windows:**
```bash
scripts/setup-local-env.bat
```

**Linux/Mac:**
```bash
source scripts/setup-local-env.sh
```

**Manual Setup:**
```bash
# Database
export DB_URL="***********************************************************************************************"
export DB_USERNAME="root"
export DB_PASSWORD="root"

# JWT
export JWT_SECRET="your-secure-local-secret-key"
export JWT_EXPIRATION="86400000"

# CORS
export CORS_ALLOWED_ORIGINS="http://localhost:3000,http://localhost:8080,http://localhost:9093"
```

### Production Deployment

**CRITICAL: Never use default values in production!**

1. **Copy the template:**
   ```bash
   cp src/main/resources/application-prod.properties.template src/main/resources/application-prod.properties
   ```

2. **Set environment variables:**
   ```bash
   # Database (use secure credentials)
   export DB_URL="*************************************************"
   export DB_USERNAME="your-secure-username"
   export DB_PASSWORD="your-secure-password"

   # JWT (generate with: openssl rand -base64 64)
   export JWT_SECRET="your-production-jwt-secret"
   export JWT_EXPIRATION="3600000"

   # CORS (restrict to your domains)
   export CORS_ALLOWED_ORIGINS="https://yourdomain.com,https://app.yourdomain.com"

   # SSL
   export SSL_ENABLED="true"
   export SSL_KEYSTORE_PATH="/path/to/keystore.p12"
   export SSL_KEYSTORE_PASSWORD="your-keystore-password"
   ```

## Security Best Practices

### 1. Secrets Management
- **Never commit secrets** to version control
- Use **external secret management** (Azure Key Vault, AWS Secrets Manager)
- **Rotate secrets regularly**
- Use **strong, randomly generated** passwords and keys

### 2. Database Security
- Use **dedicated database users** with minimal privileges
- Enable **SSL/TLS** for database connections
- Implement **connection pooling** with proper limits
- Regular **security updates** for database software

### 3. Application Security
- Keep **dependencies updated** regularly
- Run **security scans** in CI/CD pipeline
- Implement **rate limiting** for APIs
- Use **HTTPS only** in production
- Enable **audit logging** for all operations

### 4. Network Security
- Use **firewalls** to restrict access
- Implement **VPN** for administrative access
- **Monitor network traffic** for anomalies
- Use **load balancers** with SSL termination

## Security Monitoring

### 1. Audit Logging
- All security events are logged with correlation IDs
- Failed authentication attempts are tracked
- Input validation failures are recorded
- Security violations trigger alerts

### 2. Health Checks
- Database connectivity monitoring
- External service availability checks
- SSL certificate expiration monitoring
- Application performance metrics

### 3. Alerting
Configure alerts for:
- Multiple failed login attempts
- Unusual API usage patterns
- Security violation events
- System resource exhaustion
- SSL certificate expiration

## Incident Response

### 1. Security Violation Detection
- Automatic logging of security violations
- Severity-based classification
- Correlation ID tracking for investigation

### 2. Response Procedures
1. **Immediate**: Block suspicious IP addresses
2. **Short-term**: Rotate compromised credentials
3. **Long-term**: Review and update security measures

## Compliance

### 1. Data Protection
- Input validation prevents data corruption
- Audit trails for compliance reporting
- Secure data transmission and storage

### 2. Access Control
- Role-based access control (RBAC)
- Document-level permissions
- User activity monitoring

## Security Testing

### 1. Automated Testing
- Input validation tests
- Security header verification
- Authentication and authorization tests

### 2. Manual Testing
- Penetration testing recommendations
- Security code review checklist
- Vulnerability assessment procedures

## Updates and Maintenance

### 1. Regular Updates
- Security patches for dependencies
- Operating system updates
- Database security updates

### 2. Security Reviews
- Quarterly security configuration review
- Annual penetration testing
- Regular vulnerability assessments

## Contact

For security issues or questions:
- Create a security issue in the project repository
- Follow responsible disclosure practices
- Include detailed reproduction steps
