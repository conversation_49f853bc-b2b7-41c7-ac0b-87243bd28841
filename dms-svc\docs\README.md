# DMS Service Documentation

This directory contains basic documentation for the Document Management Service (DMS), focusing on essential upload and download operations with GraphQL and Angular 18 integration.

## 📚 Documentation Files

### [DMS_BASIC_GUIDE.md](./DMS_BASIC_GUIDE.md)
**Complete integration guide** covering:
- Authentication with JWT tokens
- Document upload via GraphQL
- Document download via REST API
- Full Angular 18 service implementation
- Component examples with UI
- Error handling and troubleshooting
- Advanced features (chunked upload, bulk operations)

### [DMS_QUICK_REFERENCE.md](./DMS_QUICK_REFERENCE.md)
**Quick reference card** with:
- Essential GraphQL queries and mutations
- Common request headers
- API endpoints
- Angular service template
- Copy-paste code snippets

### [test-dms.html](./test-dms.html)
**Interactive test page** for:
- Testing authentication
- Uploading documents
- Searching documents
- Downloading files
- No framework dependencies - just open in browser

## 🚀 Quick Start

1. **Start the DMS service** (ensure it's running on `http://localhost:9093`)

2. **Test with HTML page**:
   ```bash
   # Open the test page in your browser
   open dms-svc/docs/test-dms.html
   ```

3. **Test with GraphiQL**:
   ```
   http://localhost:9093/graphiql
   ```

4. **Integrate with Angular**:
   - Copy the service code from `DMS_BASIC_GUIDE.md`
   - Follow the component examples
   - Configure your environment files

## 🔑 Authentication

The DMS service uses JWT authentication. For development/testing:

```graphql
mutation GenerateTestToken($input: JwtTokenRequest!) {
  generateTestToken(input: $input) {
    token
    tokenType
    expiresAt
  }
}
```

## 📤 Upload Documents

### Basic Upload
```graphql
mutation UploadDocument($input: UploadDocumentInput!) {
  uploadDocument(input: $input) {
    id name version status fileSize
  }
}
```

### Enhanced Upload (Recommended)
```graphql
mutation UploadDocumentEnhanced($input: EnhancedDocumentUploadInput!) {
  uploadDocumentEnhanced(input: $input) {
    success
    document { id name version }
    message
  }
}
```

## 📥 Download Documents

### REST API (Recommended)
```bash
# Get download metadata
GET /api/v2/documents/{id}/download/info

# Download file
GET /api/v2/documents/{id}/download
```

### GraphQL (Deprecated for file downloads)
```graphql
query DownloadDocument($id: ID!) {
  downloadDocument(id: $id) {
    id name version status
  }
}
```

## 🔍 Search Documents

```graphql
query SearchDocuments($filter: DocumentSearchInput, $pagination: PaginationInput) {
  searchDocuments(filter: $filter, pagination: $pagination) {
    content { id name version status createdDate }
    totalElements
  }
}
```

## 🌐 API Endpoints

- **GraphQL**: `http://localhost:9093/graphql`
- **GraphiQL**: `http://localhost:9093/graphiql`
- **Download**: `http://localhost:9093/api/v2/documents/{id}/download`
- **Health Check**: `http://localhost:9093/actuator/health`

## 📋 Common Headers

| Operation | Headers |
|-----------|---------|
| GraphQL Queries | `{"Authorization": "Bearer TOKEN", "Content-Type": "application/json"}` |
| File Upload | `{"Authorization": "Bearer TOKEN"}` |
| File Download | `{"Authorization": "Bearer TOKEN"}` |

## 🛠️ Angular Integration

### Service Setup
```typescript
@Injectable()
export class DmsService {
  private graphqlUrl = 'http://localhost:9093/graphql';
  
  uploadDocument(file: File, metadata: any): Observable<any> {
    // Implementation in DMS_BASIC_GUIDE.md
  }
  
  downloadDocument(documentId: string): Observable<Blob> {
    // Implementation in DMS_BASIC_GUIDE.md
  }
}
```

### Component Usage
```typescript
@Component({...})
export class DocumentManagerComponent {
  constructor(private dmsService: DmsService) {}
  
  uploadDocument() {
    this.dmsService.uploadDocument(file, metadata).subscribe(...)
  }
}
```

## 🚨 Error Handling

Common error responses:
- **401 Unauthorized**: Invalid or missing JWT token
- **404 Not Found**: Document doesn't exist
- **413 Payload Too Large**: File size exceeds limit
- **400 Bad Request**: Invalid file type or validation error

## 🔧 Troubleshooting

1. **Empty downloaded files**: Use REST API instead of GraphQL for downloads
2. **Upload timeouts**: Use chunked upload for large files (>10MB)
3. **Authentication errors**: Generate new JWT token
4. **CORS issues**: Ensure proper CORS configuration

## 📖 Additional Resources

- [Existing API Documentation](./api/DOCUMENT_DOWNLOAD_API.md) - Detailed REST API docs
- GraphQL Schema: `dms-svc/src/main/resources/graphql/schema.graphqls`
- Upload Schema: `dms-svc/src/main/resources/graphql/document-upload-schema.graphqls`

## 🤝 Support

For issues or questions:
1. Check the troubleshooting section in `DMS_BASIC_GUIDE.md`
2. Test with the HTML test page
3. Review server logs for error details
4. Contact the DMS development team

---

**Note**: This documentation focuses on basic operations. For advanced features like workflow management, template operations, and compliance metadata, refer to the complete API documentation.