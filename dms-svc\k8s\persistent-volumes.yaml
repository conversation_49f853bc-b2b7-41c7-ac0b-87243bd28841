# Persistent Volumes for DMS Service
# MySQL Persistent Volume
apiVersion: v1
kind: PersistentVolume
metadata:
  name: mysql-pv
  labels:
    app.kubernetes.io/name: mysql
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: dms-system
spec:
  capacity:
    storage: 20Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: fast-ssd
  hostPath:
    path: /data/mysql
    type: DirectoryOrCreate

---
# MySQL Persistent Volume Claim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mysql-pvc
  namespace: dms-system
  labels:
    app.kubernetes.io/name: mysql
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: dms-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd

---
# Redis Persistent Volume
apiVersion: v1
kind: PersistentVolume
metadata:
  name: redis-pv
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
    app.kubernetes.io/part-of: dms-system
spec:
  capacity:
    storage: 5Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: fast-ssd
  hostPath:
    path: /data/redis
    type: DirectoryOrCreate

---
# Redis Persistent Volume Claim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: dms-system
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
    app.kubernetes.io/part-of: dms-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: fast-ssd

---
# Elasticsearch Persistent Volume
apiVersion: v1
kind: PersistentVolume
metadata:
  name: elasticsearch-pv
  labels:
    app.kubernetes.io/name: elasticsearch
    app.kubernetes.io/component: search
    app.kubernetes.io/part-of: dms-system
spec:
  capacity:
    storage: 30Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: fast-ssd
  hostPath:
    path: /data/elasticsearch
    type: DirectoryOrCreate

---
# Elasticsearch Persistent Volume Claim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: elasticsearch-pvc
  namespace: dms-system
  labels:
    app.kubernetes.io/name: elasticsearch
    app.kubernetes.io/component: search
    app.kubernetes.io/part-of: dms-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 30Gi
  storageClassName: fast-ssd

---
# Prometheus Persistent Volume
apiVersion: v1
kind: PersistentVolume
metadata:
  name: prometheus-pv
  labels:
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/component: monitoring
    app.kubernetes.io/part-of: dms-system
spec:
  capacity:
    storage: 50Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: fast-ssd
  hostPath:
    path: /data/prometheus
    type: DirectoryOrCreate

---
# Prometheus Persistent Volume Claim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: prometheus-pvc
  namespace: dms-system
  labels:
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/component: monitoring
    app.kubernetes.io/part-of: dms-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
  storageClassName: fast-ssd

---
# Grafana Persistent Volume
apiVersion: v1
kind: PersistentVolume
metadata:
  name: grafana-pv
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: monitoring
    app.kubernetes.io/part-of: dms-system
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: fast-ssd
  hostPath:
    path: /data/grafana
    type: DirectoryOrCreate

---
# Grafana Persistent Volume Claim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-pvc
  namespace: dms-system
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: monitoring
    app.kubernetes.io/part-of: dms-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
# DMS Logs Persistent Volume
apiVersion: v1
kind: PersistentVolume
metadata:
  name: dms-logs-pv
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: logs
    app.kubernetes.io/part-of: dms-system
spec:
  capacity:
    storage: 20Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: shared-storage
  hostPath:
    path: /data/dms-logs
    type: DirectoryOrCreate

---
# DMS Logs Persistent Volume Claim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: dms-logs-pvc
  namespace: dms-system
  labels:
    app.kubernetes.io/name: dms
    app.kubernetes.io/component: logs
    app.kubernetes.io/part-of: dms-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 20Gi
  storageClassName: shared-storage

---
# Storage Class for Fast SSD
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fast-ssd
  labels:
    app.kubernetes.io/name: storage
    app.kubernetes.io/component: storage-class
    app.kubernetes.io/part-of: dms-system
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
reclaimPolicy: Retain

---
# Storage Class for Shared Storage
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: shared-storage
  labels:
    app.kubernetes.io/name: storage
    app.kubernetes.io/component: storage-class
    app.kubernetes.io/part-of: dms-system
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
reclaimPolicy: Retain
