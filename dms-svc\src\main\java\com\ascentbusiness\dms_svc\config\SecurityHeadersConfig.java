package com.ascentbusiness.dms_svc.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;
import org.springframework.security.web.header.writers.StaticHeadersWriter;

import java.util.HashMap;
import java.util.Map;

/**
 * Comprehensive security headers configuration for the DMS system
 * Implements industry-standard security headers for protection against various attacks
 */
@Configuration
public class SecurityHeadersConfig {
    
    @Value("${dms.security.headers.csp.enabled:true}")
    private boolean cspEnabled;
    
    @Value("${dms.security.headers.csp.policy:default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'}")
    private String cspPolicy;
    
    @Value("${dms.security.headers.csp.report-only:false}")
    private boolean cspReportOnly;
    
    @Value("${dms.security.headers.hsts.enabled:true}")
    private boolean hstsEnabled;
    
    @Value("${dms.security.headers.hsts.max-age:31536000}")
    private long hstsMaxAge;
    
    @Value("${dms.security.headers.hsts.include-subdomains:true}")
    private boolean hstsIncludeSubdomains;
    
    @Value("${dms.security.headers.hsts.preload:true}")
    private boolean hstsPreload;
    
    @Value("${dms.security.headers.frame-options:DENY}")
    private String frameOptions;
    
    @Value("${dms.security.headers.content-type-options:nosniff}")
    private String contentTypeOptions;
    
    @Value("${dms.security.headers.referrer-policy:strict-origin-when-cross-origin}")
    private String referrerPolicy;
    
    @Value("${dms.security.headers.permissions-policy:geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=(), vibrate=(), fullscreen=(self), sync-xhr=()}")
    private String permissionsPolicy;
    
    @Value("${dms.security.headers.cross-origin-embedder-policy:require-corp}")
    private String crossOriginEmbedderPolicy;
    
    @Value("${dms.security.headers.cross-origin-opener-policy:same-origin}")
    private String crossOriginOpenerPolicy;
    
    @Value("${dms.security.headers.cross-origin-resource-policy:same-origin}")
    private String crossOriginResourcePolicy;
    
    @Value("${dms.security.headers.expect-ct.enabled:false}")
    private boolean expectCtEnabled;
    
    @Value("${dms.security.headers.expect-ct.max-age:86400}")
    private long expectCtMaxAge;
    
    @Value("${dms.security.headers.expect-ct.enforce:false}")
    private boolean expectCtEnforce;
    
    @Value("${dms.security.headers.expect-ct.report-uri:}")
    private String expectCtReportUri;
    
    /**
     * Get Content Security Policy header configuration
     */
    public Map<String, String> getCSPHeaders() {
        Map<String, String> headers = new HashMap<>();
        
        if (cspEnabled) {
            String headerName = cspReportOnly ? "Content-Security-Policy-Report-Only" : "Content-Security-Policy";
            headers.put(headerName, cspPolicy);
        }
        
        return headers;
    }
    
    /**
     * Get HSTS header configuration
     */
    public Map<String, String> getHSTSHeaders() {
        Map<String, String> headers = new HashMap<>();
        
        if (hstsEnabled) {
            StringBuilder hstsValue = new StringBuilder();
            hstsValue.append("max-age=").append(hstsMaxAge);
            
            if (hstsIncludeSubdomains) {
                hstsValue.append("; includeSubDomains");
            }
            
            if (hstsPreload) {
                hstsValue.append("; preload");
            }
            
            headers.put("Strict-Transport-Security", hstsValue.toString());
        }
        
        return headers;
    }
    
    /**
     * Get all security headers
     */
    public Map<String, String> getAllSecurityHeaders() {
        Map<String, String> headers = new HashMap<>();
        
        // CSP headers
        headers.putAll(getCSPHeaders());
        
        // HSTS headers
        headers.putAll(getHSTSHeaders());
        
        // Frame options
        headers.put("X-Frame-Options", frameOptions);
        
        // Content type options
        headers.put("X-Content-Type-Options", contentTypeOptions);
        
        // Referrer policy
        headers.put("Referrer-Policy", referrerPolicy);
        
        // Permissions policy
        headers.put("Permissions-Policy", permissionsPolicy);
        
        // Cross-origin policies
        headers.put("Cross-Origin-Embedder-Policy", crossOriginEmbedderPolicy);
        headers.put("Cross-Origin-Opener-Policy", crossOriginOpenerPolicy);
        headers.put("Cross-Origin-Resource-Policy", crossOriginResourcePolicy);
        
        // Expect-CT header
        if (expectCtEnabled) {
            StringBuilder expectCtValue = new StringBuilder();
            expectCtValue.append("max-age=").append(expectCtMaxAge);
            
            if (expectCtEnforce) {
                expectCtValue.append(", enforce");
            }
            
            if (expectCtReportUri != null && !expectCtReportUri.trim().isEmpty()) {
                expectCtValue.append(", report-uri=\"").append(expectCtReportUri).append("\"");
            }
            
            headers.put("Expect-CT", expectCtValue.toString());
        }
        
        // Additional security headers
        headers.put("X-XSS-Protection", "1; mode=block");
        headers.put("X-Download-Options", "noopen");
        headers.put("X-Permitted-Cross-Domain-Policies", "none");
        headers.put("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.put("Pragma", "no-cache");
        headers.put("Expires", "0");
        
        return headers;
    }
    
    /**
     * Get ReferrerPolicy enum value for Spring Security configuration
     */
    public ReferrerPolicyHeaderWriter.ReferrerPolicy getReferrerPolicyEnum() {
        switch (referrerPolicy.toLowerCase()) {
            case "no-referrer":
                return ReferrerPolicyHeaderWriter.ReferrerPolicy.NO_REFERRER;
            case "no-referrer-when-downgrade":
                return ReferrerPolicyHeaderWriter.ReferrerPolicy.NO_REFERRER_WHEN_DOWNGRADE;
            case "origin":
                return ReferrerPolicyHeaderWriter.ReferrerPolicy.ORIGIN;
            case "origin-when-cross-origin":
                return ReferrerPolicyHeaderWriter.ReferrerPolicy.ORIGIN_WHEN_CROSS_ORIGIN;
            case "same-origin":
                return ReferrerPolicyHeaderWriter.ReferrerPolicy.SAME_ORIGIN;
            case "strict-origin":
                return ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN;
            case "strict-origin-when-cross-origin":
                return ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN;
            case "unsafe-url":
                return ReferrerPolicyHeaderWriter.ReferrerPolicy.UNSAFE_URL;
            default:
                return ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN;
        }
    }
    
    /**
     * Create static headers writers for additional security headers
     */
    @Bean
    public StaticHeadersWriter additionalSecurityHeadersWriter() {
        // Permissions Policy - use default if null
        String permissionsPolicyValue = permissionsPolicy != null ? permissionsPolicy : "geolocation=(), microphone=(), camera=()";
        
        // Cross-Origin policies - use defaults if null
        String crossOriginEmbedderPolicyValue = crossOriginEmbedderPolicy != null ? crossOriginEmbedderPolicy : "require-corp";
        String crossOriginOpenerPolicyValue = crossOriginOpenerPolicy != null ? crossOriginOpenerPolicy : "same-origin";
        String crossOriginResourcePolicyValue = crossOriginResourcePolicy != null ? crossOriginResourcePolicy : "same-origin";
        
        // Build Expect-CT header if enabled
        String expectCtValue = null;
        if (expectCtEnabled) {
            StringBuilder expectCtBuilder = new StringBuilder();
            expectCtBuilder.append("max-age=").append(expectCtMaxAge);
            
            if (expectCtEnforce) {
                expectCtBuilder.append(", enforce");
            }
            
            if (expectCtReportUri != null && !expectCtReportUri.trim().isEmpty()) {
                expectCtBuilder.append(", report-uri=\"").append(expectCtReportUri).append("\"");
            }
            
            expectCtValue = expectCtBuilder.toString();
        }
        
        // Create StaticHeadersWriter with conditional Expect-CT header
        if (expectCtEnabled && expectCtValue != null) {
            return new StaticHeadersWriter(
                "Permissions-Policy", permissionsPolicyValue,
                "Cross-Origin-Embedder-Policy", crossOriginEmbedderPolicyValue,
                "Cross-Origin-Opener-Policy", crossOriginOpenerPolicyValue,
                "Cross-Origin-Resource-Policy", crossOriginResourcePolicyValue,
                "X-XSS-Protection", "1; mode=block",
                "X-Download-Options", "noopen",
                "X-Permitted-Cross-Domain-Policies", "none",
                "Expect-CT", expectCtValue
            );
        } else {
            return new StaticHeadersWriter(
                "Permissions-Policy", permissionsPolicyValue,
                "Cross-Origin-Embedder-Policy", crossOriginEmbedderPolicyValue,
                "Cross-Origin-Opener-Policy", crossOriginOpenerPolicyValue,
                "Cross-Origin-Resource-Policy", crossOriginResourcePolicyValue,
                "X-XSS-Protection", "1; mode=block",
                "X-Download-Options", "noopen",
                "X-Permitted-Cross-Domain-Policies", "none"
            );
        }
    }
    
    // Getters for configuration values
    public boolean isCspEnabled() { return cspEnabled; }
    public String getCspPolicy() { return cspPolicy; }
    public boolean isCspReportOnly() { return cspReportOnly; }
    public boolean isHstsEnabled() { return hstsEnabled; }
    public long getHstsMaxAge() { return hstsMaxAge; }
    public boolean isHstsIncludeSubdomains() { return hstsIncludeSubdomains; }
    public boolean isHstsPreload() { return hstsPreload; }
    public String getFrameOptions() { return frameOptions; }
    public String getContentTypeOptions() { return contentTypeOptions; }
    public String getReferrerPolicy() { return referrerPolicy; }
    public String getPermissionsPolicy() { return permissionsPolicy; }
}
