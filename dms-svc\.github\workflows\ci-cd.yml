# CI/CD Pipeline for DMS Service
name: DMS Service CI/CD

on:
  push:
    branches: [ main, develop, 'release/*' ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  JAVA_VERSION: '21'
  MAVEN_OPTS: '-Xmx3072m'
  DOCKER_REGISTRY: 'your-registry.com'
  IMAGE_NAME: 'dms-service'

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Run Maven compile
      run: mvn clean compile -B

    - name: Run Checkstyle
      run: mvn checkstyle:check -B

    - name: Run SpotBugs
      run: mvn spotbugs:check -B

    - name: Run OWASP Dependency Check
      run: mvn org.owasp:dependency-check-maven:check -B

    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # Unit and Integration Tests
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    needs: code-quality
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: dms_test
          MYSQL_USER: dms_user
          MYSQL_PASSWORD: dms_password
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

      elasticsearch:
        image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
        env:
          discovery.type: single-node
          xpack.security.enabled: false
          ES_JAVA_OPTS: -Xms512m -Xmx512m
        ports:
          - 9200:9200
        options: --health-cmd="curl -f http://localhost:9200/_cluster/health" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Wait for services
      run: |
        timeout 60 bash -c 'until curl -f http://localhost:9200/_cluster/health; do sleep 2; done'
        timeout 60 bash -c 'until mysqladmin ping -h localhost -P 3306 -u root -proot; do sleep 2; done'
        timeout 60 bash -c 'until redis-cli -h localhost -p 6379 ping; do sleep 2; done'

    - name: Run unit tests
      run: mvn test -B -Dspring.profiles.active=test
      env:
        SPRING_DATASOURCE_URL: ************************************
        SPRING_DATASOURCE_USERNAME: dms_user
        SPRING_DATASOURCE_PASSWORD: dms_password
        SPRING_REDIS_HOST: localhost
        SPRING_REDIS_PORT: 6379
        SPRING_ELASTICSEARCH_URIS: http://localhost:9200

    - name: Run integration tests
      run: mvn verify -B -Dspring.profiles.active=integration-test
      env:
        SPRING_DATASOURCE_URL: ************************************
        SPRING_DATASOURCE_USERNAME: dms_user
        SPRING_DATASOURCE_PASSWORD: dms_password
        SPRING_REDIS_HOST: localhost
        SPRING_REDIS_PORT: 6379
        SPRING_ELASTICSEARCH_URIS: http://localhost:9200

    - name: Generate test reports
      run: mvn surefire-report:report jacoco:report -B

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: |
          target/surefire-reports/
          target/failsafe-reports/
          target/site/jacoco/

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: target/site/jacoco/jacoco.xml
        flags: unittests
        name: codecov-umbrella

  # Security Scanning
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'

  # Build and Push Docker Image
  build-and-push:
    name: Build & Push Docker Image
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    if: github.event_name == 'push' || github.event_name == 'release'
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Build application
      run: mvn clean package -DskipTests -B

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Docker Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

    - name: Sign container image
      uses: sigstore/cosign-installer@v3
      with:
        cosign-release: 'v2.2.0'
    
    - name: Sign the published Docker image
      env:
        COSIGN_EXPERIMENTAL: 1
      run: echo "${{ steps.meta.outputs.tags }}" | xargs -I {} cosign sign --yes {}@${{ steps.build.outputs.digest }}

  # Deploy to Development
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/develop'
    environment: development
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_DEV }}

    - name: Deploy to Kubernetes
      run: |
        sed -i 's|dms-service:latest|${{ needs.build-and-push.outputs.image-tag }}|g' k8s/deployment.yaml
        kubectl apply -f k8s/ -n dms-dev

    - name: Verify deployment
      run: |
        kubectl rollout status deployment/dms-deployment -n dms-dev --timeout=300s
        kubectl get pods -n dms-dev

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-and-push
    if: startsWith(github.ref, 'refs/heads/release/')
    environment: staging
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}

    - name: Deploy to Kubernetes
      run: |
        sed -i 's|dms-service:latest|${{ needs.build-and-push.outputs.image-tag }}|g' k8s/deployment.yaml
        kubectl apply -f k8s/ -n dms-staging

    - name: Verify deployment
      run: |
        kubectl rollout status deployment/dms-deployment -n dms-staging --timeout=300s
        kubectl get pods -n dms-staging

    - name: Run smoke tests
      run: |
        kubectl run smoke-test --image=curlimages/curl --rm -i --restart=Never -- \
          curl -f http://dms-service.dms-staging.svc.cluster.local:8080/dms/actuator/health

  # Deploy to Production
  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.event_name == 'release'
    environment: production
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_PROD }}

    - name: Blue-Green Deployment
      run: |
        # Update deployment with new image
        sed -i 's|dms-service:latest|${{ needs.build-and-push.outputs.image-tag }}|g' k8s/deployment.yaml
        
        # Apply new deployment
        kubectl apply -f k8s/ -n dms-system
        
        # Wait for rollout
        kubectl rollout status deployment/dms-deployment -n dms-system --timeout=600s
        
        # Verify health
        kubectl run health-check --image=curlimages/curl --rm -i --restart=Never -- \
          curl -f http://dms-service.dms-system.svc.cluster.local:8080/dms/actuator/health

    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: 'DMS Service deployed to production successfully!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
