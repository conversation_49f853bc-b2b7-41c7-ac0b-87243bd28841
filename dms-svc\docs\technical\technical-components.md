# Key Technical Components

## 1. Configuration

```java
@Configuration
@ConfigurationProperties(prefix = "dms.file-processing")
@Data
public class FileProcessingConfig {
    private long directProcessingThreshold = 10 * 1024 * 1024;     // 10MB
    private long asyncProcessingThreshold = 100 * 1024 * 1024;     // 100MB
    private long maxFileSize = 5L * 1024 * 1024 * 1024;            // 5GB
    private int optimalChunkSize = 5 * 1024 * 1024;                // 5MB
    // Additional configuration properties...
}
```

## 2. Processing Strategy Enum

```java
public enum ProcessingStrategy {
    DIRECT,         // Synchronous, direct processing
    ASYNC,          // Asynchronous processing of complete file
    CHUNKED         // Chunked upload for very large files
}
```

## 3. Unified Response Structure

```java
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UploadResponse {
    // Common fields
    private String fileName;
    private Long fileSize;
    private ProcessingStrategy processingStrategy;
    
    // Strategy-specific fields
    private Long documentId;                  // For DIRECT
    private String jobId;                     // For ASYNC
    private String uploadSessionId;           // For CHUNKED
    
    // URLs for further actions
    private String documentUrl;               // For DIRECT
    private String statusCheckUrl;            // For ASYNC and CHUNKED
    private String chunkUploadUrl;            // For CHUNKED
    
    // Additional fields...
}
```

## 4. Smart Upload Controller

```java
@RestController
@RequestMapping("/api/v1/documents")
public class DocumentUploadController {
    
    @PostMapping("/upload")
    public ResponseEntity<UploadResponse> uploadDocument(@ModelAttribute UploadDocumentInput input) {
        // Determine strategy based on file size
        ProcessingStrategy strategy = determineStrategy(input.getFile().getSize());
        
        switch (strategy) {
            case DIRECT:
                return handleDirectUpload(input);
            case ASYNC:
                return handleAsyncUpload(input);
            case CHUNKED:
                return handleChunkedUploadRecommendation(input);
            default:
                throw new IllegalStateException("Unknown processing strategy");
        }
    }
    
    // Additional endpoints for status checking and chunked upload...
}
```

## 5. Async Processing

```java
@Service
@Slf4j
public class AsyncDocumentProcessor {
    
    @Async("documentProcessingExecutor")
    public CompletableFuture<Document> processDocumentAsync(String jobId, UploadDocumentInput input) {
        // Save file to temp location
        // Update job status to PROCESSING
        // Process document in background
        // Update job status to COMPLETED or FAILED
        // Return CompletableFuture with result
    }
    
    // Additional methods for status tracking and cleanup...
}
```

## 6. Chunked Upload Manager

```java
@Service
@Slf4j
public class ChunkedUploadManager {
    
    public String initializeUpload(String fileName, long totalSize, String contentType) {
        // Create upload session
        // Prepare temporary storage
        // Return session ID
    }
    
    public ChunkResult uploadChunk(String sessionId, int chunkNumber, MultipartFile chunk) {
        // Save chunk to temporary storage
        // Track progress
        // Return chunk result
    }
    
    public Document completeUpload(String sessionId, DocumentMetadata metadata) {
        // Combine chunks
        // Process complete file
        // Clean up temporary chunks
        // Return created document
    }
    
    // Additional methods for status tracking and cleanup...
}
```