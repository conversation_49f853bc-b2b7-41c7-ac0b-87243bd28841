# Amazon S3 Storage Configuration Guide

This guide provides complete instructions for configuring and using Amazon S3 storage in the Document Management System (DMS).

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Configuration](#configuration)
4. [AWS S3 Setup](#aws-s3-setup)
5. [Application Configuration](#application-configuration)
6. [Migration Between Storage Providers](#migration-between-storage-providers)
7. [Testing S3 Integration](#testing-s3-integration)
8. [Troubleshooting](#troubleshooting)
9. [Best Practices](#best-practices)

## Overview

The DMS supports multiple storage providers:
- **LOCAL**: Files stored on local filesystem
- **S3**: Files stored in Amazon S3 buckets
- **SHAREPOINT**: SharePoint storage (planned for future release)

Documents can be stored in different storage providers, and the system provides migration tools to move documents between storage providers.

## Prerequisites

Before configuring S3 storage, ensure you have:

1. **AWS Account**: Active Amazon Web Services account
2. **S3 Bucket**: Created and configured S3 bucket
3. **IAM User/Role**: With appropriate S3 permissions
4. **Access Credentials**: AWS Access Key ID and Secret Access Key
5. **Java Dependencies**: AWS SDK dependencies already included in the project

## AWS S3 Setup

### Step 1: Create an S3 Bucket

1. Log in to the AWS Console
2. Navigate to S3 service
3. Click "Create bucket"
4. Configure bucket settings:
   ```
   Bucket name: your-dms-documents-bucket
   AWS Region: us-east-1 (or your preferred region)
   Block all public access: ✓ (recommended for security)
   Bucket Versioning: Enable (optional, for document versioning)
   Default encryption: Enable (recommended)
   ```

### Step 2: Create IAM User

1. Navigate to IAM service in AWS Console
2. Create a new user with programmatic access
3. Attach the following policy (replace `your-dms-documents-bucket` with your bucket name):

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket",
                "s3:GetBucketLocation"
            ],
            "Resource": [
                "arn:aws:s3:::your-dms-documents-bucket",
                "arn:aws:s3:::your-dms-documents-bucket/*"
            ]
        }
    ]
}
```

### Step 3: Generate Access Keys

1. After creating the IAM user, generate Access Key ID and Secret Access Key
2. **Important**: Store these credentials securely and never commit them to version control

## Application Configuration

### Basic S3 Configuration

Add the following properties to your `application.properties`:

```properties
# Storage Configuration
dms.storage.default-provider=S3

# S3 Configuration
dms.storage.s3.access-key=${AWS_ACCESS_KEY_ID:your-access-key}
dms.storage.s3.secret-key=${AWS_SECRET_ACCESS_KEY:your-secret-key}
dms.storage.s3.region=${AWS_REGION:us-east-1}
dms.storage.s3.bucket-name=${S3_BUCKET_NAME:your-dms-documents-bucket}

# Optional S3 Configuration
dms.storage.s3.path-prefix=documents/
dms.storage.s3.server-side-encryption=AES256
dms.storage.s3.storage-class=STANDARD

# Local Storage (still required for migration and fallback)
dms.storage.local.base-path=${DMS_STORAGE_PATH:./storage/documents}
```

### Environment Variables (Recommended)

For production deployments, use environment variables instead of hardcoding credentials:

```bash
export AWS_ACCESS_KEY_ID=your-access-key-id
export AWS_SECRET_ACCESS_KEY=your-secret-access-key
export AWS_REGION=us-east-1
export S3_BUCKET_NAME=your-dms-documents-bucket
export DMS_STORAGE_PATH=/opt/dms/storage
```

### Configuration Properties Reference

| Property | Description | Default | Required |
|----------|-------------|---------|----------|
| `dms.storage.default-provider` | Default storage provider (LOCAL, S3, SHAREPOINT) | LOCAL | No |
| `dms.storage.s3.access-key` | AWS Access Key ID | - | Yes (for S3) |
| `dms.storage.s3.secret-key` | AWS Secret Access Key | - | Yes (for S3) |
| `dms.storage.s3.region` | AWS Region | us-east-1 | No |
| `dms.storage.s3.bucket-name` | S3 Bucket name | - | Yes (for S3) |
| `dms.storage.s3.path-prefix` | Prefix for all object keys | documents/ | No |
| `dms.storage.s3.server-side-encryption` | S3 encryption type | AES256 | No |
| `dms.storage.s3.storage-class` | S3 storage class | STANDARD | No |

## Migration Between Storage Providers

The DMS includes a command-line migration tool to move documents between storage providers.

### Migration Commands

#### Migrate from Local to S3

```bash
# Dry run (preview migration without making changes)
java -jar dms-svc.jar --migrate-to-s3 --dry-run

# Actual migration with verification
java -jar dms-svc.jar --migrate-to-s3 --verify

# Migration with local file cleanup after successful upload
java -jar dms-svc.jar --migrate-to-s3 --verify --cleanup-local

# Migration without verification (faster but less safe)
java -jar dms-svc.jar --migrate-to-s3 --no-verify
```

#### Migrate from S3 to Local

```bash
# Dry run
java -jar dms-svc.jar --migrate-to-local --dry-run

# Actual migration
java -jar dms-svc.jar --migrate-to-local --verify
```

### Migration Options

| Option | Description |
|--------|-------------|
| `--migrate-to-s3` | Migrate documents from LOCAL to S3 |
| `--migrate-to-local` | Migrate documents from S3 to LOCAL |
| `--dry-run` | Preview migration without making changes |
| `--verify` | Verify file integrity using checksums (default: enabled) |
| `--no-verify` | Skip file integrity verification |
| `--cleanup-local` | Delete local files after successful S3 upload |
| `--batch-size N` | Process documents in batches of N (default: 100) |

### Migration Process

1. **Validation**: Checks S3 configuration and connectivity
2. **Discovery**: Finds all documents with the source storage provider
3. **Transfer**: Copies files to the destination storage provider
4. **Verification**: Compares checksums to ensure file integrity (if enabled)
5. **Database Update**: Updates document records with new storage paths
6. **Cleanup**: Optionally removes source files (if --cleanup-local specified)

## Testing S3 Integration

### Test Document Upload

1. Start the application with S3 configuration
2. Use the GraphQL API to upload a document:

```graphql
mutation {
  uploadDocument(input: {
    name: "Test Document"
    description: "Testing S3 upload"
    file: "base64-encoded-file-content"
    fileName: "test.pdf"
    mimeType: "application/pdf"
  }) {
    id
    name
    storagePath
    storageProvider
  }
}
```

### Test Document Download

```graphql
query {
  downloadDocument(id: "document-id") {
    fileName
    mimeType
    content
  }
}
```

### Verify S3 Storage

1. Check the AWS S3 Console
2. Navigate to your bucket
3. Verify files are stored under the correct path structure:
   ```
   documents/
   ├── 2025/
   │   └── 06/
   │       └── Test Document_20250606_123456_789.pdf
   ```

## Troubleshooting

### Common Issues

#### 1. AWS Credentials Issues

**Error**: `Unable to load AWS credentials`

**Solutions**:
- Verify AWS Access Key ID and Secret Access Key are correct
- Ensure environment variables are properly set
- Check IAM user has necessary permissions

#### 2. S3 Bucket Access Issues

**Error**: `Access Denied` when accessing S3 bucket

**Solutions**:
- Verify bucket name is correct
- Check IAM user has permissions for the specific bucket
- Ensure bucket is in the correct AWS region

#### 3. Network Connectivity Issues

**Error**: `Unable to connect to S3`

**Solutions**:
- Check internet connectivity
- Verify AWS region is correct
- Check firewall/proxy settings

#### 4. Large File Upload Issues

**Error**: `Request timeout` or `Connection reset`

**Solutions**:
- Increase connection timeout settings
- Consider implementing multipart upload for large files
- Check network stability

### Debug Mode

Enable debug logging for S3 operations:

```properties
logging.level.com.ascentbusiness.dms_svc.service.S3StorageService=DEBUG
logging.level.software.amazon.awssdk=DEBUG
```

### Health Check

The application provides health check endpoints:

```bash
# Check application health
curl http://localhost:8080/actuator/health

# Check S3 connectivity (if implemented)
curl http://localhost:8080/actuator/health/s3
```

## Best Practices

### Security

1. **Use IAM Roles**: In production, use IAM roles instead of access keys when possible
2. **Principle of Least Privilege**: Grant minimal required permissions
3. **Encrypt at Rest**: Enable S3 server-side encryption
4. **Secure Transit**: Use HTTPS for all S3 communications (enabled by default)
5. **Access Logging**: Enable S3 access logging for audit trails

### Performance

1. **Choose Appropriate Region**: Use S3 region closest to your application
2. **Use Appropriate Storage Class**: 
   - STANDARD for frequently accessed documents
   - STANDARD_IA for infrequently accessed documents
   - GLACIER for archival documents
3. **Implement Caching**: Cache frequently accessed documents locally
4. **Monitor Costs**: Use AWS Cost Explorer to monitor S3 usage costs

### Reliability

1. **Enable Versioning**: Protect against accidental deletions
2. **Cross-Region Replication**: For disaster recovery
3. **Lifecycle Policies**: Automatically transition old documents to cheaper storage classes
4. **Monitoring**: Set up CloudWatch alarms for S3 metrics

### Migration

1. **Test First**: Always run dry-run before actual migration
2. **Backup**: Ensure you have backups before migration
3. **Gradual Migration**: For large datasets, consider migrating in batches
4. **Verify Integrity**: Always enable verification during migration
5. **Monitor**: Watch application logs during migration

### Cost Optimization

1. **Storage Classes**: Use appropriate storage classes based on access patterns
2. **Lifecycle Policies**: Automatically move old documents to cheaper storage
3. **Delete Incomplete Uploads**: Set up policies to clean up incomplete multipart uploads
4. **Request Patterns**: Optimize request patterns to reduce API costs

## Integration Examples

### Spring Configuration

```java
@Configuration
@ConditionalOnProperty(name = "dms.storage.default-provider", havingValue = "S3")
public class S3Configuration {
    
    @Bean
    @Primary
    public StorageService s3StorageService(S3StorageService s3Service) {
        return s3Service;
    }
}
```

### Custom S3 Configuration

```java
@Component
public class CustomS3Config {
    
    @Bean
    public S3Client customS3Client() {
        return S3Client.builder()
            .region(Region.US_EAST_1)
            .credentialsProvider(DefaultCredentialsProvider.create())
            .overrideConfiguration(ClientOverrideConfiguration.builder()
                .apiCallTimeout(Duration.ofMinutes(5))
                .apiCallAttemptTimeout(Duration.ofMinutes(2))
                .build())
            .build();
    }
}
```

This completes the comprehensive S3 storage configuration guide for your Document Management System.
