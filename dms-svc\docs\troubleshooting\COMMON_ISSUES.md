# Common Issues Guide

This guide covers the most frequently encountered issues in the DMS system and their solutions.

## Prerequisites

Before troubleshooting any issues, ensure you have:

### System Requirements
- **Java**: JDK 21 or higher
- **MySQL**: Version 8.0 or higher
- **Redis**: Version 6.0 or higher
- **Elasticsearch**: Version 7.17 or higher (optional, for search functionality)

### Access Requirements
- **Administrative Access**: Required for system-level troubleshooting
- **Database Access**: MySQL credentials with appropriate permissions
- **Log Access**: Ability to read application and system logs
- **Network Access**: Access to all service endpoints and dependencies

### Tools and Utilities
- **cURL**: For API testing and health checks
- **MySQL Client**: For database queries and diagnostics
- **Redis CLI**: For cache inspection and management
- **Text Editor**: For configuration file modifications
- **Process Monitor**: For system resource monitoring

### Knowledge Prerequisites
- **Basic SQL**: For database troubleshooting
- **GraphQL**: For API testing and queries
- **JSON**: For configuration and API request/response handling
- **Command Line**: Basic shell/terminal commands
- **Log Analysis**: Ability to read and interpret log files

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Application Startup Issues](#application-startup-issues)
3. [Authentication and Authorization](#authentication-and-authorization)
4. [Document Upload Problems](#document-upload-problems)
5. [Database Connection Issues](#database-connection-issues)
6. [Performance Issues](#performance-issues)
7. [Configuration Problems](#configuration-problems)
8. [Storage Provider Issues](#storage-provider-issues)
9. [Search Functionality Issues](#search-functionality-issues)

## Application Startup Issues

### Issue: Application fails to start with "Port already in use" error

**Symptoms:**
```
Web server failed to start. Port 9092 was already in use.
```

**Solution:**
1. **Check for running processes:**
   ```bash
   netstat -ano | findstr :9092
   tasklist /FI "PID eq [PID_NUMBER]"
   ```

2. **Kill the process:**
   ```bash
   taskkill /PID [PID_NUMBER] /F
   ```

3. **Alternative: Change port in application.properties:**
   ```properties
   server.port=9093
   ```

### Issue: Database connection failure on startup

**Symptoms:**
```
Failed to configure a DataSource: 'url' attribute is not specified
```

**Solution:**
1. **Verify MySQL is running:**
   ```bash
   mysql -u root -p
   ```

2. **Check application.properties:**
   ```properties
   spring.datasource.url=**********************************
   spring.datasource.username=dms_user
   spring.datasource.password=your_password
   ```

3. **Create database if missing:**
   ```sql
   CREATE DATABASE dms_db;
   CREATE USER 'dms_user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON dms_db.* TO 'dms_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

### Issue: Redis connection failure

**Symptoms:**
```
Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException
```

**Solution:**
1. **Start Redis server:**
   ```bash
   redis-server
   ```

2. **Verify Redis is accessible:**
   ```bash
   redis-cli ping
   ```

3. **Check Redis configuration:**
   ```properties
   spring.redis.host=localhost
   spring.redis.port=6379
   spring.redis.timeout=2000ms
   ```

## Authentication and Authorization

### Issue: JWT token validation fails

**Symptoms:**
```
JWT token is invalid or expired
```

**Solution:**
1. **Generate a new test token:**
   ```bash
   curl -X POST http://localhost:9092/graphql \
     -H "Content-Type: application/json" \
     -d '{"query":"mutation { generateTestToken(input: { userId: \"test-user\", roles: [\"USER\"], permissions: [\"READ\", \"WRITE\"], expirationMinutes: 60 }) { token } }"}'
   ```

2. **Check token expiration:**
   - Tokens expire after the specified time
   - Generate new tokens for testing

3. **Verify JWT secret configuration:**
   ```properties
   jwt.secret=your-secret-key-here
   jwt.expiration=3600000
   ```

### Issue: "Insufficient permissions" error

**Symptoms:**
```
You don't have permission to access this document. READ permission required.
```

**Solution:**
1. **Check user roles in JWT token:**
   - Ensure user has appropriate roles (USER, ADMIN, etc.)
   - Verify permissions (READ, WRITE, DELETE, ADMIN)

2. **Check document permissions:**
   ```sql
   SELECT * FROM document_permissions WHERE document_id = [DOCUMENT_ID];
   ```

3. **Grant permissions if needed:**
   ```graphql
   mutation {
     grantDocumentPermission(input: {
       documentId: "123"
       userId: "<EMAIL>"
       permissionType: READ
     })
   }
   ```

## Document Upload Problems

### Issue: File upload fails with "File too large" error

**Symptoms:**
```
Maximum upload size exceeded; nested exception is java.lang.IllegalStateException
```

**Solution:**
1. **Increase file size limits:**
   ```properties
   spring.servlet.multipart.max-file-size=100MB
   spring.servlet.multipart.max-request-size=100MB
   ```

2. **Check available disk space:**
   ```bash
   df -h
   ```

3. **Verify storage provider configuration:**
   - For S3: Check bucket permissions and quotas
   - For local: Ensure directory has write permissions

### Issue: Duplicate file exception

**Symptoms:**
```
DuplicateFileException: An ACTIVE document with identical content already exists
```

**Solution:**
1. **Use overrideFile parameter:**
   ```graphql
   mutation {
     uploadDocument(input: {
       file: $file
       name: "Document Name"
       overrideFile: true
     })
   }
   ```

2. **Check for existing documents:**
   ```sql
   SELECT * FROM documents WHERE original_file_name = 'filename.pdf' AND status = 'ACTIVE';
   ```

3. **Delete or archive existing document if appropriate**

### Issue: Storage provider not available

**Symptoms:**
```
StorageException: Failed to store file in S3
```

**Solution:**
1. **Check S3 configuration:**
   ```properties
   aws.s3.bucket-name=your-bucket-name
   aws.s3.region=us-east-1
   aws.access-key-id=your-access-key
   aws.secret-access-key=your-secret-key
   ```

2. **Verify AWS credentials:**
   ```bash
   aws s3 ls s3://your-bucket-name
   ```

3. **Test connectivity:**
   ```bash
   curl -I https://s3.amazonaws.com
   ```

## Database Connection Issues

### Issue: Connection pool exhausted

**Symptoms:**
```
HikariPool-1 - Connection is not available, request timed out after 30000ms
```

**Solution:**
1. **Increase connection pool size:**
   ```properties
   spring.datasource.hikari.maximum-pool-size=20
   spring.datasource.hikari.minimum-idle=5
   spring.datasource.hikari.connection-timeout=30000
   ```

2. **Check for connection leaks:**
   ```sql
   SHOW PROCESSLIST;
   ```

3. **Monitor slow queries:**
   ```sql
   SHOW VARIABLES LIKE 'slow_query_log';
   SET GLOBAL slow_query_log = 'ON';
   ```

### Issue: Database deadlocks

**Symptoms:**
```
Deadlock found when trying to get lock; try restarting transaction
```

**Solution:**
1. **Check deadlock information:**
   ```sql
   SHOW ENGINE INNODB STATUS;
   ```

2. **Review transaction isolation:**
   ```properties
   spring.jpa.properties.hibernate.connection.isolation=2
   ```

3. **Optimize query order and indexing**

## Performance Issues

### Issue: Slow response times

**Symptoms:**
- API responses taking > 5 seconds
- High CPU usage
- Memory consumption increasing

**Solution:**
1. **Enable performance monitoring:**
   ```properties
   management.endpoints.web.exposure.include=health,metrics,prometheus
   ```

2. **Check database performance:**
   ```sql
   SELECT * FROM information_schema.processlist WHERE time > 10;
   ```

3. **Monitor JVM metrics:**
   ```bash
   curl http://localhost:9092/actuator/metrics/jvm.memory.used
   ```

4. **Enable caching:**
   ```properties
   spring.cache.type=redis
   spring.cache.redis.time-to-live=3600000
   ```

### Issue: Memory leaks

**Symptoms:**
```
java.lang.OutOfMemoryError: Java heap space
```

**Solution:**
1. **Increase heap size:**
   ```bash
   java -Xmx2g -Xms1g -jar dms-service.jar
   ```

2. **Enable garbage collection logging:**
   ```bash
   java -XX:+PrintGC -XX:+PrintGCDetails -jar dms-service.jar
   ```

3. **Profile memory usage:**
   - Use JProfiler or VisualVM
   - Check for memory leaks in caches
   - Review large object allocations

## Configuration Problems

### Issue: Configuration properties not loading

**Symptoms:**
```
Property 'dms.storage.provider' not found
```

**Solution:**
1. **Check application.properties location:**
   - Should be in `src/main/resources/`
   - Verify file is included in JAR

2. **Verify property syntax:**
   ```properties
   # Correct
   dms.storage.provider=LOCAL
   
   # Incorrect (spaces around =)
   dms.storage.provider = LOCAL
   ```

3. **Check profile-specific properties:**
   ```properties
   spring.profiles.active=development
   ```

### Issue: Environment variable override not working

**Symptoms:**
- Environment variables not overriding application.properties
- Configuration values not changing in different environments

**Solution:**
1. **Use correct environment variable format:**
   ```bash
   # For property: dms.storage.provider
   export DMS_STORAGE_PROVIDER=S3
   
   # For property: spring.datasource.url
   export SPRING_DATASOURCE_URL=********************************
   ```

2. **Verify environment variables are set:**
   ```bash
   echo $DMS_STORAGE_PROVIDER
   ```

3. **Check Spring Boot property binding:**
   ```properties
   # Enable relaxed binding
   spring.config.use-legacy-processing=false
   ```

## Storage Provider Issues

### Issue: Local storage permission denied

**Symptoms:**
```
java.nio.file.AccessDeniedException: /app/storage/documents
```

**Solution:**
1. **Check directory permissions:**
   ```bash
   ls -la /app/storage/
   chmod 755 /app/storage/documents
   ```

2. **Verify user permissions:**
   ```bash
   whoami
   groups
   ```

3. **Create directory if missing:**
   ```bash
   mkdir -p /app/storage/documents
   chown app:app /app/storage/documents
   ```

### Issue: S3 access denied

**Symptoms:**
```
AmazonS3Exception: Access Denied (Service: Amazon S3; Status Code: 403)
```

**Solution:**
1. **Verify IAM permissions:**
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "s3:GetObject",
           "s3:PutObject",
           "s3:DeleteObject"
         ],
         "Resource": "arn:aws:s3:::your-bucket/*"
       }
     ]
   }
   ```

2. **Check bucket policy:**
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Sid": "AllowDMSAccess",
         "Effect": "Allow",
         "Principal": {
           "AWS": "arn:aws:iam::account:user/dms-user"
         },
         "Action": "s3:*",
         "Resource": [
           "arn:aws:s3:::your-bucket",
           "arn:aws:s3:::your-bucket/*"
         ]
       }
     ]
   }
   ```

## Search Functionality Issues

### Issue: Elasticsearch not available

**Symptoms:**
```
NoNodeAvailableException: None of the configured nodes are available
```

**Solution:**
1. **Start Elasticsearch:**
   ```bash
   # Using Docker
   docker run -d --name elasticsearch -p 9200:9200 -e "discovery.type=single-node" elasticsearch:7.17.0
   ```

2. **Check Elasticsearch health:**
   ```bash
   curl http://localhost:9200/_health
   ```

3. **Verify configuration:**
   ```properties
   elasticsearch.host=localhost
   elasticsearch.port=9200
   elasticsearch.scheme=http
   ```

### Issue: Search returns no results

**Symptoms:**
- Search queries return empty results
- Documents not appearing in search

**Solution:**
1. **Check if documents are indexed:**
   ```bash
   curl http://localhost:9200/documents/_search
   ```

2. **Reindex documents:**
   ```bash
   curl -X POST http://localhost:9092/api/admin/reindex
   ```

3. **Verify search query syntax:**
   ```graphql
   query {
     searchDocuments(filter: { name: "test" }) {
       content { id name }
     }
   }
   ```

---

**Last Updated**: July 23, 2024
**Version**: 1.2.0
**Generated**: 2024-07-23T11:45:00+05:30

For additional help, refer to the specific troubleshooting guides or contact the development team.
