# Markdown to Word Conversion - Hybrid Approach

## Overview

This document describes the hybrid approach implemented for Markdown to Word conversion, providing both REST and GraphQL endpoints to accommodate different client preferences and use cases.

## Architecture

### Hybrid Implementation Strategy

Due to multipart GraphQL library compatibility challenges with Spring Boot 3.5, we implemented a **dual-endpoint approach**:

1. **REST Endpoints** - For multipart file uploads (immediate functionality)
2. **GraphQL Endpoints** - For file-path based operations (existing functionality)

This provides maximum flexibility and ensures all conversion scenarios are supported.

## REST API Endpoints

### Convert Markdown to Word (Multipart Upload)

**Endpoint:** `POST /api/v1/conversion/markdown/to-word`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `file` (required): Markdown file (.md or .markdown)
- `scannerType` (optional): Virus scanner type (MOCK, CLAMAV, SOPHOS, WINDOWS_DEFENDER, VIRUS_TOTAL)
  - Default: MOCK

**Example Request:**
```bash
curl -X POST http://localhost:8080/api/v1/conversion/markdown/to-word \
  -F "file=@document.md" \
  -F "scannerType=MOCK"
```

**Response:**
```json
{
  "sessionId": "034405bb-43b3-4455-9a01-9958802e9727",
  "originalFileName": "document.md",
  "convertedFileName": "document_converted.docx",
  "downloadPath": "C:\\Users\\<USER>\\document_converted_034405bb-43b3-4455-9a01-9958802e9727.docx",
  "fileSize": 15234,
  "virusScanResponse": {
    "result": "CLEAN",
    "scannerType": "MOCK",
    "scanId": "63430b41-ed3a-4957-bb1d-277d6547612a"
  },
  "success": true,
  "message": "Markdown to Word conversion completed successfully",
  "completedAt": "2025-06-25T16:32:18.123Z",
  "processingTimeMs": 1965,
  "usedPandoc": false,
  "conversionMethod": "fallback"
}
```

### Get Conversion Status

**Endpoint:** `GET /api/v1/conversion/markdown/status/{sessionId}`

**Response:** HTTP 501 Not Implemented (placeholder for future session storage)

## GraphQL Endpoints

### Convert Markdown to Word (File Path)

**Mutation:** `convertMarkdownToWordFromPath`

**Example:**
```graphql
mutation {
  convertMarkdownToWordFromPath(input: {
    filePath: "/path/to/document.md",
    scannerType: MOCK
  }) {
    sessionId
    originalFileName
    convertedFileName
    downloadPath
    fileSize
    success
    message
    usedPandoc
    conversionMethod
    virusScanResponse {
      result
      scannerType
    }
  }
}
```

### Convert Markdown to Word (Multipart - Future)

**Mutation:** `convertMarkdownToWordMultipart` 

**Status:** Currently experiencing HTTP 400 issues due to multipart-spring-graphql library compatibility. Use REST endpoint for multipart uploads.

## Error Handling

### HTTP Status Codes

- **200 OK**: Conversion successful
- **400 Bad Request**: Invalid file format or missing file
- **413 Payload Too Large**: File size exceeds limit
- **422 Unprocessable Entity**: File failed virus scan
- **500 Internal Server Error**: Conversion failed
- **501 Not Implemented**: Feature not yet available

### Error Response Format

```json
{
  "success": false,
  "message": "Only Markdown files (.md, .markdown) are supported for conversion",
  "errorDetails": "DmsBusinessException: Only Markdown files (.md, .markdown) are supported for conversion",
  "originalFileName": "document.txt",
  "completedAt": "2025-06-25T16:33:55.280Z"
}
```

## Features

### Virus Scanning
- Configurable virus scanner types
- Comprehensive scan logging and audit trails
- File rejection on virus detection

### Conversion Methods
- **Pandoc**: High-quality conversion (when available)
- **Fallback**: Basic conversion using Apache POI
- Automatic fallback when Pandoc unavailable

### Audit Logging
- Complete conversion lifecycle tracking
- Performance metrics
- Security event logging

### File Management
- Automatic file naming with session IDs
- Download directory management
- Temporary file cleanup

## Testing

### REST Integration Tests

Location: `src/test/java/com/ascentbusiness/dms_svc/integration/MarkdownConversionRestIntegrationTest.java`

**Test Coverage:**
- ✅ Valid file conversion
- ✅ Invalid file type handling
- ✅ Empty file handling
- ✅ Scanner type validation
- ✅ Both .md and .markdown extensions
- ✅ Status endpoint behavior

**Run Tests:**
```bash
mvn test -Dtest=**/*MarkdownConversionRestIntegrationTest
```

### GraphQL Integration Tests

Location: `src/test/java/com/ascentbusiness/dms_svc/integration/MarkdownConversionGraphQLIntegrationTest.java`

**Test Coverage:**
- ✅ File path conversion (working)
- ❌ Multipart conversion (HTTP 400 issue)

## Configuration

### Application Properties

```yaml
# Pandoc Configuration
pandoc:
  executable-path: pandoc
  enable-fallback: true
  max-file-size: 10MB
  timeout-seconds: 30

# Virus Scanning
virus-scanning:
  default-scanner: MOCK
  enabled-scanners: [MOCK, CLAMAV]

# File Upload
spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
```

## Usage Recommendations

### When to Use REST Endpoints
- ✅ **Multipart file uploads** from web forms
- ✅ **Direct file upload** scenarios
- ✅ **Simple HTTP clients** that don't support GraphQL
- ✅ **File upload widgets** in frontend applications

### When to Use GraphQL Endpoints
- ✅ **File path operations** on server-side files
- ✅ **Complex query requirements** with related data
- ✅ **GraphQL-first applications**
- ✅ **Batch operations** with multiple queries

## Future Enhancements

### Planned Improvements
1. **Session Storage**: Implement Redis-based session tracking for status queries
2. **Multipart GraphQL**: Resolve library compatibility issues
3. **Async Processing**: Add background processing for large files
4. **Progress Tracking**: Real-time conversion progress updates
5. **Batch Conversion**: Multiple file conversion support

### Migration Path
When multipart GraphQL issues are resolved:
1. Update multipart-spring-graphql library
2. Fix parameter mapping issues
3. Enable multipart GraphQL tests
4. Maintain REST endpoints for backward compatibility

## Troubleshooting

### Common Issues

**Issue**: HTTP 400 on multipart GraphQL requests
**Solution**: Use REST endpoint `/api/v1/conversion/markdown/to-word`

**Issue**: File size limit exceeded
**Solution**: Check `pandoc.max-file-size` configuration

**Issue**: Pandoc not available
**Solution**: Install Pandoc or rely on fallback conversion

**Issue**: Virus scan failures
**Solution**: Check scanner configuration and file content

## Security Considerations

- All uploads undergo virus scanning
- File type validation prevents malicious uploads
- Audit logging tracks all conversion activities
- Temporary files are cleaned up automatically
- User context is preserved throughout the process

## Performance

### Metrics
- Average conversion time: ~2 seconds (fallback method)
- File size limit: 10MB (configurable)
- Concurrent conversions: Supported
- Memory usage: Optimized for large files

### Optimization Tips
- Use Pandoc for better performance and quality
- Configure appropriate file size limits
- Monitor virus scanning performance
- Implement caching for repeated conversions
