package com.ascentbusiness.dms_svc.aspect;

import com.ascentbusiness.dms_svc.util.StructuredLogger;
import com.ascentbusiness.dms_svc.util.TracingUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;

/**
 * Aspect for tracing database operations
 */
@Aspect
@Component
public class DatabaseTracingAspect {

    private final TracingUtil tracingUtil;
    private final StructuredLogger structuredLogger;

    @Autowired
    public DatabaseTracingAspect(TracingUtil tracingUtil, StructuredLogger structuredLogger) {
        this.tracingUtil = tracingUtil;
        this.structuredLogger = structuredLogger;
    }

    /**
     * Trace repository method calls
     */
    @Around("execution(* com.ascentbusiness.dms_svc.repository..*(..))")
    public Object traceRepositoryMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        String operation = className + "." + methodName;

        Instant startTime = Instant.now();

        tracingUtil.addTagToCurrentSpan("db.repository.class", className);
        tracingUtil.addTagToCurrentSpan("db.repository.method", methodName);
        tracingUtil.addTagToCurrentSpan("db.operation.type", inferOperationType(methodName));

        try {
            Object result = joinPoint.proceed();

            Duration duration = Duration.between(startTime, Instant.now());
            tracingUtil.addTagToCurrentSpan("db.operation.status", "SUCCESS");
            tracingUtil.addTagToCurrentSpan("db.operation.duration.ms",
                    String.valueOf(duration.toMillis()));

            // Add result context if available
            if (result != null) {
                addResultContext(result);
            }

            // Log slow queries
            if (duration.toMillis() > 1000) {
                structuredLogger.logWarning("Database Operation", "SLOW_QUERY",
                        "Operation " + operation + " took " + duration.toMillis() + "ms");
            }

            structuredLogger.logPerformance("Database Operation", duration, "SUCCESS",
                    "Repository: " + operation);

            return result;
        } catch (Throwable e) {
            Duration duration = Duration.between(startTime, Instant.now());
            tracingUtil.addErrorToCurrentSpan(e);
            tracingUtil.addTagToCurrentSpan("db.operation.status", "ERROR");
            tracingUtil.addTagToCurrentSpan("db.operation.duration.ms",
                    String.valueOf(duration.toMillis()));

            structuredLogger.logError("Database Operation", "REPOSITORY_ERROR",
                    "DB_001", e, "Repository operation failed: " + operation);

            throw e;
        }
    }

    /**
     * Trace service method calls that involve database operations
     */
    @Around("execution(* com.ascentbusiness.dms_svc.service..*(..)) && " +
            "(execution(* *..save*(..)) || execution(* *..find*(..)) || " +
            "execution(* *..delete*(..)) || execution(* *..update*(..)) || " +
            "execution(* *..create*(..)))")
    public Object traceServiceDatabaseMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        String operation = className + "." + methodName;

        Instant startTime = Instant.now();

        tracingUtil.addTagToCurrentSpan("service.class", className);
        tracingUtil.addTagToCurrentSpan("service.method", methodName);
        tracingUtil.addTagToCurrentSpan("service.operation.type", inferOperationType(methodName));

        try {
            Object result = joinPoint.proceed();

            Duration duration = Duration.between(startTime, Instant.now());
            tracingUtil.addTagToCurrentSpan("service.operation.status", "SUCCESS");
            tracingUtil.addTagToCurrentSpan("service.operation.duration.ms",
                    String.valueOf(duration.toMillis()));

            structuredLogger.logPerformance("Service Database Operation", duration, "SUCCESS",
                    "Service: " + operation);

            return result;
        } catch (Throwable e) {
            Duration duration = Duration.between(startTime, Instant.now());
            tracingUtil.addErrorToCurrentSpan(e);
            tracingUtil.addTagToCurrentSpan("service.operation.status", "ERROR");
            tracingUtil.addTagToCurrentSpan("service.operation.duration.ms",
                    String.valueOf(duration.toMillis()));

            structuredLogger.logError("Service Database Operation", "SERVICE_DB_ERROR",
                    "SVC_DB_001", e, "Service database operation failed: " + operation);

            throw e;
        }
    }

    /**
     * Infer operation type from method name
     */
    private String inferOperationType(String methodName) {
        String lowerMethodName = methodName.toLowerCase();
        
        if (lowerMethodName.startsWith("find") || lowerMethodName.startsWith("get") || 
            lowerMethodName.startsWith("search") || lowerMethodName.startsWith("query")) {
            return "SELECT";
        } else if (lowerMethodName.startsWith("save") || lowerMethodName.startsWith("create") || 
                   lowerMethodName.startsWith("insert")) {
            return "INSERT";
        } else if (lowerMethodName.startsWith("update") || lowerMethodName.startsWith("modify")) {
            return "UPDATE";
        } else if (lowerMethodName.startsWith("delete") || lowerMethodName.startsWith("remove")) {
            return "DELETE";
        } else {
            return "UNKNOWN";
        }
    }

    /**
     * Add result context to span based on result type
     */
    private void addResultContext(Object result) {
        if (result instanceof java.util.Collection<?> collection) {
            tracingUtil.addTagToCurrentSpan("db.result.type", "COLLECTION");
            tracingUtil.addTagToCurrentSpan("db.result.count", String.valueOf(collection.size()));
        } else if (result instanceof java.util.Optional<?> optional) {
            tracingUtil.addTagToCurrentSpan("db.result.type", "OPTIONAL");
            tracingUtil.addTagToCurrentSpan("db.result.present", String.valueOf(optional.isPresent()));
        } else if (result instanceof org.springframework.data.domain.Page<?> page) {
            tracingUtil.addTagToCurrentSpan("db.result.type", "PAGE");
            tracingUtil.addTagToCurrentSpan("db.result.count", String.valueOf(page.getNumberOfElements()));
            tracingUtil.addTagToCurrentSpan("db.result.total", String.valueOf(page.getTotalElements()));
            tracingUtil.addTagToCurrentSpan("db.result.page", String.valueOf(page.getNumber()));
            tracingUtil.addTagToCurrentSpan("db.result.size", String.valueOf(page.getSize()));
        } else if (result != null) {
            tracingUtil.addTagToCurrentSpan("db.result.type", "SINGLE");
            tracingUtil.addTagToCurrentSpan("db.result.class", result.getClass().getSimpleName());
        } else {
            tracingUtil.addTagToCurrentSpan("db.result.type", "NULL");
        }
    }
}
