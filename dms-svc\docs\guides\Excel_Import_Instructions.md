# Excel Import Instructions for DMS GraphQL Test Cases

## Files Created
I have created **16 CSV files** that can be imported into Excel as separate sheets:

### 1. **Summary Sheet**
- `DMS_TestCases_Summary.csv` - Overview and navigation

### 2. **Test Case Sheets (15 categories)**
- `DMS_TestCases_No_Access.csv` - Tests 1-5 (Unauthorized/Invalid tokens)
- `DMS_TestCases_READ_Permission.csv` - Tests 6-12 (READ operations)
- `DMS_TestCases_WRITE_Permission.csv` - Tests 13-20 (WRITE operations)
- `DMS_TestCases_DELETE_Permission.csv` - Tests 21-25 (DELETE operations)
- `DMS_TestCases_ADMIN_Permission.csv` - Tests 26-30 (ADMIN operations)
- `DMS_TestCases_Creator_Privileges.csv` - Tests 31-35 (Creator privileges)
- `DMS_TestCases_Multi_Role.csv` - Tests 36-39 (Multi-role scenarios)
- `DMS_TestCases_Error_Handling.csv` - Tests 40-48 (Error scenarios)
- `DMS_TestCases_Storage_Providers.csv` - Tests 49-53 (Storage providers)
- `DMS_TestCases_Search_Filter.csv` - Tests 54-58 (Search & filtering)
- `DMS_TestCases_Audit_Logs.csv` - Tests 59-62 (Audit operations)
- `DMS_TestCases_Security_Validation.csv` - Tests 63-67 (Security tests)
- `DMS_TestCases_Performance.csv` - Tests 68-69 (Performance tests)
- `DMS_TestCases_Integration.csv` - Tests 70-74 (End-to-end workflows)
- `DMS_TestCases_Boundary_Tests.csv` - Tests 75-77 (Boundary conditions)

## How to Import into Excel

### Option 1: Individual Import
1. Open Excel
2. Go to **Data** > **Get Data** > **From File** > **From Text/CSV**
3. Select each CSV file and import as separate sheets
4. Rename sheets to remove "DMS_TestCases_" prefix

### Option 2: Bulk Import (Recommended)
1. Open Excel
2. Create a new workbook
3. For each CSV file:
   - Right-click on sheet tabs at bottom
   - Select "Insert" > "Worksheet"
   - Go to **Data** > **From Text/CSV**
   - Select the CSV file
   - Choose **Load** to import

### Option 3: Power Query (Advanced)
1. Use Power Query to combine all CSV files
2. Create connections to each CSV file
3. Load each as a separate table/sheet

## Excel Formatting Recommendations

### Column Formatting:
- **Column A (S.No)**: Number format, center aligned
- **Column B (JWT Request)**: Text wrap enabled, width ~30
- **Column C (JWT)**: Text wrap enabled, width ~20
- **Column D (Request)**: Text wrap enabled, width ~40
- **Column E (Response)**: Text wrap enabled, width ~40
- **Column F (Result)**: Center aligned, conditional formatting
- **Column G (Comment)**: Text wrap enabled, width ~30

### Conditional Formatting for Results:
- **PASS**: Green background (#90EE90)
- **FAIL**: Red background (#FFB6C1)
- **ERROR**: Yellow background (#FFFF99)

### Additional Formatting:
- Freeze top row (headers)
- Apply filters to all columns
- Set row height to auto-fit content
- Use Arial 10pt font for readability

## Usage Tips

1. **Navigation**: Use the Summary sheet for quick navigation between test categories
2. **Filtering**: Apply filters to find specific test scenarios
3. **Copy-Paste**: GraphQL queries are formatted for direct copy-paste into GraphiQL
4. **Testing**: Follow the sequence - generate JWT token, then execute the test query/mutation
5. **Results**: Update the Result column based on actual test outcomes

## Total Test Coverage
- **77 comprehensive test cases**
- **6 GraphQL mutations covered**
- **5 GraphQL queries covered** 
- **All permission levels tested**
- **Complete system functionality validated**

## File Locations
All CSV files are located in: `d:/MyDevelopment/dms-svc/`

You can now easily import these into Excel for a much more readable and organized testing experience!
