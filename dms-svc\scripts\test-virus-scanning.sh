#!/bin/bash

# Virus Scanning Test Suite Runner
# This script runs all virus scanning related tests

echo "========================================"
echo "DMS Virus Scanning Test Suite"
echo "========================================"
echo

# Set test environment variables
export SPRING_PROFILES_ACTIVE=test
export DMS_VIRUS_SCANNER_MOCK_ENABLED=true
export DMS_VIRUS_SCANNING_ENABLED=true

echo "Running virus scanning tests..."
echo

# Run virus scanner unit tests
echo "[1/6] Running MockVirusScanner tests..."
mvn test -Dtest=MockVirusScannerTest -q
if [ $? -ne 0 ]; then
    echo "FAILED: MockVirusScanner tests"
    exit 1
fi
echo "PASSED: MockVirusScanner tests"
echo

echo "[2/6] Running VirusScannerFactory tests..."
mvn test -Dtest=VirusScannerFactoryTest -q
if [ $? -ne 0 ]; then
    echo "FAILED: VirusScannerFactory tests"
    exit 1
fi
echo "PASSED: VirusScannerFactory tests"
echo

echo "[3/6] Running VirusScanningService tests..."
mvn test -Dtest=VirusScanningServiceTest -q
if [ $? -ne 0 ]; then
    echo "FAILED: VirusScanningService tests"
    exit 1
fi
echo "PASSED: VirusScanningService tests"
echo

echo "[4/6] Running BulkUploadService tests..."
mvn test -Dtest=BulkUploadServiceTest -q
if [ $? -ne 0 ]; then
    echo "FAILED: BulkUploadService tests"
    exit 1
fi
echo "PASSED: BulkUploadService tests"
echo

echo "[5/6] Running DocumentResolver virus scan tests..."
mvn test -Dtest=DocumentResolverVirusScanTest -q
if [ $? -ne 0 ]; then
    echo "FAILED: DocumentResolver virus scan tests"
    exit 1
fi
echo "PASSED: DocumentResolver virus scan tests"
echo

echo "[6/6] Running virus scanning integration tests..."
mvn test -Dtest=VirusScanningIntegrationTest -q
if [ $? -ne 0 ]; then
    echo "FAILED: Virus scanning integration tests"
    exit 1
fi
echo "PASSED: Virus scanning integration tests"
echo

echo "========================================"
echo "All virus scanning tests completed successfully!"
echo "========================================"
echo

# Run all virus scanning tests together for final verification
echo "Running complete virus scanning test suite..."
mvn test -Dtest="*VirusScanner*,*VirusScanning*,*BulkUpload*,*DocumentResolverVirusScan*" -q
if [ $? -ne 0 ]; then
    echo "FAILED: Complete virus scanning test suite"
    exit 1
fi

echo
echo "========================================"
echo "VIRUS SCANNING TEST SUITE: ALL TESTS PASSED"
echo "========================================"
echo
echo "Test Summary:"
echo "- MockVirusScanner unit tests: PASSED"
echo "- VirusScannerFactory unit tests: PASSED"
echo "- VirusScanningService unit tests: PASSED"
echo "- BulkUploadService unit tests: PASSED"
echo "- DocumentResolver virus scan tests: PASSED"
echo "- Virus scanning integration tests: PASSED"
echo "- Complete test suite verification: PASSED"
echo
echo "Total test categories: 6"
echo "Status: SUCCESS"
echo

echo "Virus scanning test suite completed."
echo
