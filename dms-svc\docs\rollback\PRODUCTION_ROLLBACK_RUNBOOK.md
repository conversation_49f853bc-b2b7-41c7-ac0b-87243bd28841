# Production Rollback Runbook

This runbook provides step-by-step procedures for executing rollbacks in production environments. Follow these procedures exactly during incident response.

## 🚨 Emergency Contacts

- **Incident Commander**: +1-555-INC-CMDR
- **DMS Team Lead**: +1-555-DMS-LEAD  
- **Database Administrator**: +1-555-DBA-HELP
- **Infrastructure Team**: +1-555-INFRA-OPS
- **Emergency Hotline**: +1-555-DMS-HELP

## 📋 Pre-Rollback Checklist

### Immediate Assessment (0-5 minutes)

- [ ] **Incident Severity Confirmed**
  - Critical: Complete service outage, data corruption, security breach
  - High: Significant performance degradation, partial outage
  - Medium: Minor issues affecting some users

- [ ] **Rollback Authorization Obtained**
  - Incident Commander approval
  - Stakeholder notification sent
  - Change control board notified (if required)

- [ ] **Team Assembly**
  - Incident Commander assigned
  - Technical lead identified
  - Database administrator on standby
  - Communication lead assigned

- [ ] **Impact Assessment**
  - Affected users/systems identified
  - Business impact quantified
  - Rollback vs. forward fix decision made

### Technical Readiness (5-10 minutes)

- [ ] **Environment Verification**
  - Production environment access confirmed
  - Backup systems verified
  - Monitoring systems operational

- [ ] **Rollback Target Identified**
  - Last known good version determined
  - Backup availability confirmed
  - Rollback scope defined

- [ ] **Communication Initiated**
  - Status page updated
  - Internal teams notified
  - Customer communication prepared

## 🔄 Rollback Procedures

### Procedure 1: Application Deployment Rollback

**Estimated Time**: 5-15 minutes  
**Risk Level**: Medium  
**Data Loss**: None (if database compatible)

#### Steps:

1. **Create Emergency Backup** (2 minutes)
   ```bash
   # Execute on production server
   cd /app/dms-service
   scripts\rollback\rollback-deployment.bat --create-backup
   ```

2. **Stop Current Service** (1 minute)
   ```bash
   # Stop DMS service
   scripts\rollback\rollback-deployment.bat --stop-service
   ```

3. **Rollback to Target Version** (5-10 minutes)
   ```bash
   # Rollback to specific version
   scripts\rollback\rollback-deployment.bat [VERSION] --confirm
   
   # Example:
   scripts\rollback\rollback-deployment.bat 1.2.3 --confirm
   ```

4. **Validate Rollback** (2-5 minutes)
   ```bash
   # Check service health
   curl -f http://localhost:9092/actuator/health
   
   # Verify functionality
   curl -f http://localhost:9092/actuator/health/db
   curl -f http://localhost:9092/actuator/health/redis
   ```

#### Success Criteria:
- [ ] Service responds to health checks
- [ ] Database connectivity confirmed
- [ ] Redis connectivity confirmed
- [ ] No critical errors in logs
- [ ] Response times < 2 seconds

#### Rollback Criteria:
- Health checks fail after 5 minutes
- Critical errors in application logs
- Database connectivity issues

### Procedure 2: Database Rollback

**Estimated Time**: 10-30 minutes  
**Risk Level**: High  
**Data Loss**: Possible

#### Steps:

1. **Create Database Backup** (2-5 minutes)
   ```bash
   # Create emergency backup
   scripts\rollback\rollback-database.bat backup --create-emergency
   ```

2. **Stop Application Services** (1 minute)
   ```bash
   # Stop all DMS services
   scripts\rollback\rollback-deployment.bat --stop-service
   ```

3. **Execute Database Rollback** (5-20 minutes)
   ```bash
   # Rollback specific number of migrations
   scripts\rollback\rollback-database.bat steps 3 --confirm
   
   # OR rollback to specific tag
   scripts\rollback\rollback-database.bat tag v1.2.3 --confirm
   
   # OR restore from backup
   scripts\rollback\rollback-database.bat backup latest --confirm
   ```

4. **Validate Database State** (2-5 minutes)
   ```bash
   # Test database connectivity
   mysql -h localhost -u dms_user -p dms_db -e "SELECT COUNT(*) FROM documents;"
   
   # Verify data integrity
   mysql -h localhost -u dms_user -p dms_db -e "SELECT COUNT(*) FROM audit_logs;"
   ```

5. **Restart Application** (2-5 minutes)
   ```bash
   # Start services with rolled back database
   scripts\rollback\rollback-deployment.bat --start-service
   ```

#### Success Criteria:
- [ ] Database queries execute successfully
- [ ] Data integrity checks pass
- [ ] Application connects to database
- [ ] No foreign key constraint errors
- [ ] Audit trail maintained

#### Rollback Criteria:
- Database corruption detected
- Data integrity checks fail
- Application cannot connect to database

### Procedure 3: Emergency Complete Rollback

**Estimated Time**: 15-45 minutes  
**Risk Level**: Very High  
**Data Loss**: Likely

#### When to Use:
- Multiple system failures
- Security breach requiring immediate isolation
- Data corruption across multiple components
- When individual rollbacks have failed

#### Steps:

1. **Emergency Authorization** (1-2 minutes)
   ```bash
   # Requires incident commander approval
   # Document incident ID and authorization
   ```

2. **Execute Emergency Rollback** (10-30 minutes)
   ```bash
   # Complete system rollback
   scripts\rollback\emergency-rollback.bat --confirm [INCIDENT_ID]
   
   # Example:
   scripts\rollback\emergency-rollback.bat --confirm INC-2024-001
   ```

3. **Monitor Emergency Rollback** (5-10 minutes)
   ```bash
   # Monitor rollback progress
   tail -f logs\emergency-rollback.log
   
   # Check system status
   curl -f http://localhost:9092/actuator/health
   ```

4. **Validate System Recovery** (5-10 minutes)
   ```bash
   # Comprehensive system validation
   scripts\rollback\test-rollback-procedures.bat emergency --silent
   ```

#### Success Criteria:
- [ ] All services operational
- [ ] Database accessible and consistent
- [ ] Application functionality restored
- [ ] Security systems operational
- [ ] Monitoring systems functional

## 📊 Post-Rollback Procedures

### Immediate Validation (0-15 minutes)

1. **System Health Verification**
   - [ ] All health checks passing
   - [ ] Response times within SLA
   - [ ] Error rates below threshold
   - [ ] Database performance normal

2. **Functionality Testing**
   - [ ] User authentication working
   - [ ] Document upload/download functional
   - [ ] Search functionality operational
   - [ ] API endpoints responding

3. **Data Integrity Verification**
   - [ ] Document counts match expectations
   - [ ] Audit logs continuous
   - [ ] User permissions intact
   - [ ] Metadata consistency verified

### Communication Updates (15-30 minutes)

1. **Internal Communication**
   - [ ] Incident team notified of rollback completion
   - [ ] Engineering team updated
   - [ ] Management briefed on status
   - [ ] Documentation updated

2. **External Communication**
   - [ ] Status page updated
   - [ ] Customer notification sent (if required)
   - [ ] Support team briefed
   - [ ] SLA impact assessed

### Monitoring and Observation (30 minutes - 4 hours)

1. **Enhanced Monitoring**
   - [ ] Increase monitoring frequency
   - [ ] Watch for error patterns
   - [ ] Monitor performance metrics
   - [ ] Track user activity

2. **Log Analysis**
   - [ ] Review rollback logs
   - [ ] Check for anomalies
   - [ ] Verify audit trail
   - [ ] Document lessons learned

## 🔍 Rollback Validation Checklist

### Technical Validation

- [ ] **Service Health**
  - Health endpoints responding
  - All dependencies accessible
  - Performance within normal ranges
  - No critical errors in logs

- [ ] **Database Integrity**
  - All tables accessible
  - Data consistency verified
  - Foreign key constraints intact
  - Backup/restore procedures tested

- [ ] **Application Functionality**
  - User authentication working
  - Core features operational
  - API endpoints responding
  - File operations functional

### Business Validation

- [ ] **User Impact Assessment**
  - User access restored
  - Data availability confirmed
  - Feature functionality verified
  - Performance acceptable

- [ ] **Data Consistency**
  - Document integrity maintained
  - Audit trail continuous
  - User permissions preserved
  - Metadata accuracy verified

## 📝 Documentation Requirements

### During Rollback

1. **Incident Log**
   - Timestamp all actions
   - Record decision points
   - Document any deviations
   - Note team members involved

2. **Technical Log**
   - Commands executed
   - Error messages encountered
   - Performance observations
   - Validation results

### Post-Rollback

1. **Incident Report**
   - Root cause analysis
   - Timeline of events
   - Rollback effectiveness
   - Lessons learned

2. **Technical Report**
   - Rollback procedures used
   - Performance metrics
   - Data impact assessment
   - Recommendations

## ⚠️ Common Pitfalls and Troubleshooting

### Application Rollback Issues

**Problem**: Service fails to start after rollback
- **Cause**: Configuration incompatibility
- **Solution**: Verify configuration files match target version
- **Prevention**: Test rollback procedures in staging

**Problem**: Database connection failures
- **Cause**: Database schema incompatibility
- **Solution**: Rollback database to compatible version
- **Prevention**: Maintain database compatibility matrix

### Database Rollback Issues

**Problem**: Data loss during rollback
- **Cause**: Incomplete backup or migration issues
- **Solution**: Restore from most recent complete backup
- **Prevention**: Verify backup integrity before rollback

**Problem**: Foreign key constraint violations
- **Cause**: Data inconsistency after rollback
- **Solution**: Run data integrity repair scripts
- **Prevention**: Test rollback procedures with production-like data

### Emergency Rollback Issues

**Problem**: Partial rollback completion
- **Cause**: Script failure or resource constraints
- **Solution**: Manual completion of remaining steps
- **Prevention**: Regular testing of emergency procedures

## 📞 Escalation Procedures

### Level 1: Technical Team
- DMS development team
- Database administrators
- Infrastructure team

### Level 2: Management
- Engineering manager
- Product manager
- Operations manager

### Level 3: Executive
- CTO/VP Engineering
- CEO (for critical business impact)
- Legal (for security/compliance issues)

---

**Last Updated**: December 2024  
**Version**: 1.0.0  
**Maintained By**: DMS Operations Team

**Remember**: In production emergencies, speed and accuracy are critical. Follow procedures exactly and communicate frequently.
