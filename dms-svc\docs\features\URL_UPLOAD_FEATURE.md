# URL Upload Feature Documentation

## Overview

The URL Upload feature (Option 4: File Upload API with Path Resolution) provides a unified interface for uploading documents from various sources including local file paths, HTTP/HTTPS URLs, network paths, and file URIs. This feature solves the common problem where file path uploads work in local development but fail in UAT/production environments.

## Problem Solved

**Original Issue**: 
- Local development: File path uploads work because user machine = server machine
- UAT/Production: File path uploads fail because user machine ≠ server machine (AWS EC2)

**Solution**: 
- Automatically detect source type (URL, local path, network path, file URI)
- Handle each source type appropriately
- Provide backward compatibility with existing file path uploads
- Enable cross-environment functionality

## Architecture

```mermaid
graph TD
    A[Client Request] --> B[uploadDocumentFromPathOrUrl]
    B --> C{Source Type Detection}
    C -->|HTTP/HTTPS URL| D[UrlDownloadService.downloadFromUrl]
    C -->|Local File Path| E[UrlDownloadService.downloadFromPath]
    C -->|Network Path| F[UrlDownloadService.downloadFromPath]
    C -->|File URI| G[UrlDownloadService.downloadFromPath]
    D --> H[ByteArrayMultipartFile]
    E --> H
    F --> H
    G --> H
    H --> I[DocumentService.uploadDocument]
    I --> J[Document Created]
```

## Components

### 1. UploadFromPathOrUrlInput DTO
- **Location**: `com.ascentbusiness.dms_svc.dto.UploadFromPathOrUrlInput`
- **Purpose**: Input validation and source type detection
- **Key Methods**:
  - `isUrl()`: Detects HTTP/HTTPS URLs
  - `isLocalPath()`: Detects existing local file paths
  - `isNetworkPath()`: Detects UNC network paths
  - `isFileUri()`: Detects file:// URIs
  - `validate()`: Input validation and security checks

### 2. UrlDownloadService
- **Location**: `com.ascentbusiness.dms_svc.service.UrlDownloadService`
- **Purpose**: Secure downloading from various sources
- **Key Methods**:
  - `downloadFromUrl()`: Downloads from HTTP/HTTPS URLs
  - `downloadFromPath()`: Reads from local/network file paths
- **Security Features**:
  - Domain whitelist/blacklist
  - Port blocking
  - Private IP restrictions
  - Content type validation
  - File size limits
  - Timeout controls

### 3. ByteArrayMultipartFile Utility
- **Location**: `com.ascentbusiness.dms_svc.util.ByteArrayMultipartFile`
- **Purpose**: Converts byte arrays to MultipartFile interface
- **Usage**: Enables downloaded content to work with existing upload pipeline

### 4. UrlUploadConfig
- **Location**: `com.ascentbusiness.dms_svc.config.UrlUploadConfig`
- **Purpose**: Configuration management for URL uploads
- **Properties**: See Configuration section below

## GraphQL API

### Mutation

```graphql
mutation UploadFromPathOrUrl($input: UploadFromPathOrUrlInput!) {
  uploadDocumentFromPathOrUrl(input: $input) {
    id
    name
    version
    status
    storageProvider
    filePath
    fileSize
    mimeType
    createdBy
    createdDate
    lastModifiedDate
  }
}
```

### Input Type

```graphql
input UploadFromPathOrUrlInput {
  sourcePath: String!                           # Required - Source path, URL, or network path
  name: String                                  # Optional - Display name (extracted from source if not provided)
  description: String                           # Optional - Description
  storageProvider: StorageProvider              # Optional - Storage provider (defaults to system default)
  keywords: [String!]                           # Optional - Keywords/tags
  accessRoles: [DocumentAccessRoleInput!]       # Optional - Access roles
  allowDuplicates: Boolean = false              # Optional - Allow duplicate documents
  downloadTimeoutMs: Int                        # Optional - Timeout for URL downloads
  maxDownloadSizeBytes: Long                    # Optional - Max file size for downloads
}
```

## Usage Examples

### 1. Local File Path (Development)
```graphql
mutation {
  uploadDocumentFromPathOrUrl(input: {
    sourcePath: "C:\\Users\\<USER>\\report.pdf"
    name: "Monthly Report"
    description: "Monthly financial report"
  }) {
    id
    name
    fileSize
  }
}
```

### 2. HTTP URL (UAT/Production)
```graphql
mutation {
  uploadDocumentFromPathOrUrl(input: {
    sourcePath: "https://example.com/files/document.pdf"
    name: "External Document"
    downloadTimeoutMs: 60000
    maxDownloadSizeBytes: 52428800
  }) {
    id
    name
    fileSize
  }
}
```

### 3. Network Path (Shared Storage)
```graphql
mutation {
  uploadDocumentFromPathOrUrl(input: {
    sourcePath: "\\\\shared-server\\uploads\\file.docx"
    name: "Shared Document"
  }) {
    id
    name
    fileSize
  }
}
```

### 4. File URI
```graphql
mutation {
  uploadDocumentFromPathOrUrl(input: {
    sourcePath: "file:///opt/documents/contract.pdf"
    name: "Contract Document"
  }) {
    id
    name
    fileSize
  }
}
```

## Configuration

### Application Properties

```properties
# URL Upload Configuration
dms.upload.url.enabled=true
dms.upload.url.timeout-ms=30000
dms.upload.url.max-file-size=*********
dms.upload.url.max-redirects=5
dms.upload.url.user-agent=DMS-Service/1.0

# Security Settings
dms.upload.url.allowed-domains=company.com,sharepoint.com,s3.amazonaws.com
dms.upload.url.validate-ssl=true
dms.upload.url.allow-private-ips=false
dms.upload.url.blocked-ports=22,23,25,53,135,139,445,1433,1521,3306,3389,5432,6379

# Content Type Validation
dms.upload.url.allowed-content-types=
dms.upload.url.blocked-content-types=text/html,application/javascript,text/javascript

# Network Path Support
dms.upload.network-path.enabled=true
dms.upload.network-path.timeout-ms=60000
```

### Environment-Specific Configuration

#### Development (application-dev.properties)
```properties
# Allow all domains and private IPs for development
dms.upload.url.allowed-domains=
dms.upload.url.allow-private-ips=true
dms.upload.url.validate-ssl=false
```

#### UAT (application-uat.properties)
```properties
# Restrict to specific domains for UAT
dms.upload.url.allowed-domains=company.com,test-server.com
dms.upload.url.allow-private-ips=false
dms.upload.url.validate-ssl=true
```

#### Production
```properties
# Strict security for production
dms.upload.url.allowed-domains=company.com,sharepoint.company.com
dms.upload.url.allow-private-ips=false
dms.upload.url.validate-ssl=true
dms.upload.url.blocked-content-types=text/html,application/javascript,text/javascript,application/x-executable
```

## Security Features

### 1. URL Validation
- **Protocol Restriction**: Only HTTP/HTTPS allowed
- **Domain Whitelist**: Configurable allowed domains
- **Port Blocking**: Block dangerous ports (SSH, database, etc.)
- **Private IP Protection**: Prevent SSRF attacks

### 2. Content Validation
- **Content Type Filtering**: Block dangerous MIME types
- **File Size Limits**: Prevent resource exhaustion
- **Timeout Controls**: Prevent hanging requests

### 3. Path Security
- **Path Traversal Protection**: Block `..` and `~` sequences
- **File Access Validation**: Verify file exists and is readable

## Error Handling

### Common Error Codes

| Error Code | Description | Resolution |
|------------|-------------|------------|
| `URL_UPLOAD_DISABLED` | URL uploads are disabled | Enable in configuration |
| `DOMAIN_NOT_ALLOWED` | Domain not in whitelist | Add domain to allowed list |
| `PORT_BLOCKED` | Port is blocked | Use standard HTTP/HTTPS ports |
| `PRIVATE_IP_NOT_ALLOWED` | Private IP access blocked | Enable private IPs or use public URL |
| `FILE_NOT_FOUND` | File doesn't exist | Verify file path |
| `FILE_TOO_LARGE` | File exceeds size limit | Increase limit or use smaller file |
| `CONTENT_TYPE_NOT_ALLOWED` | MIME type blocked | Remove from blocked list |
| `FILE_SOURCE_INACCESSIBLE` | Cannot access source | Check permissions and connectivity |

### Example Error Response
```json
{
  "errors": [
    {
      "message": "Domain not allowed: blocked.com",
      "extensions": {
        "errorCode": "DOMAIN_NOT_ALLOWED",
        "errorData": {
          "domain": "blocked.com"
        }
      }
    }
  ]
}
```

## Performance Considerations

### 1. File Size Limits
- Default: 100MB per file
- Configurable via `dms.upload.url.max-file-size`
- Consider network bandwidth and timeout settings

### 2. Timeout Configuration
- URL downloads: 30 seconds default
- Network paths: 60 seconds default
- Adjust based on network conditions

### 3. Concurrent Downloads
- Service handles concurrent requests
- Consider rate limiting for URL downloads
- Monitor memory usage for large files

## Migration Guide

### From Existing File Path Uploads

1. **No Code Changes Required**: Existing `uploadDocumentFromPath` calls continue to work
2. **Gradual Migration**: Replace calls with `uploadDocumentFromPathOrUrl` as needed
3. **Enhanced Functionality**: New method supports URLs in addition to paths

### Client-Side Changes

```javascript
// Old way (local development only)
const uploadFromPath = `
  mutation {
    uploadDocumentFromPath(input: {
      sourceFilePath: "C:\\\\Documents\\\\file.pdf"
      name: "Document"
    }) { id name }
  }
`;

// New way (works everywhere)
const uploadFromPathOrUrl = `
  mutation {
    uploadDocumentFromPathOrUrl(input: {
      sourcePath: "https://server.com/files/file.pdf"
      name: "Document"
    }) { id name }
  }
`;
```

## Testing

### Unit Tests
- **Location**: `dms-svc/src/test/java/com/ascentbusiness/dms_svc/service/UrlDownloadServiceTest.java`
- **Coverage**: Input validation, security checks, file operations
- **Run**: `mvn test -Dtest=UrlDownloadServiceTest`

### Integration Testing

```bash
# Test local file upload
curl -X POST http://localhost:9093/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "query": "mutation { uploadDocumentFromPathOrUrl(input: { sourcePath: \"/tmp/test.pdf\", name: \"Test Document\" }) { id name } }"
  }'

# Test URL upload
curl -X POST http://localhost:9093/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "query": "mutation { uploadDocumentFromPathOrUrl(input: { sourcePath: \"https://example.com/test.pdf\", name: \"URL Document\" }) { id name } }"
  }'
```

## Monitoring and Logging

### Log Messages
- `INFO`: Successful uploads with source type and file size
- `WARN`: Security violations and blocked requests
- `ERROR`: Failed downloads and processing errors

### Metrics to Monitor
- Upload success/failure rates by source type
- Average download times for URLs
- Security violation frequency
- File size distribution

### Example Log Output
```
2024-01-15 10:30:15.123 INFO  [correlation-id-123] UrlDownloadService - Downloading file from URL: https://example.com/file.pdf
2024-01-15 10:30:16.456 INFO  [correlation-id-123] DocumentResolver - Successfully uploaded document from source: https://example.com/file.pdf -> Document ID: 12345
```

## Troubleshooting

### Common Issues

1. **"Domain not allowed" errors**
   - Check `dms.upload.url.allowed-domains` configuration
   - Ensure domain is properly formatted (no protocol)

2. **Timeout errors**
   - Increase `dms.upload.url.timeout-ms`
   - Check network connectivity to target URL

3. **File not found errors**
   - Verify file path exists and is accessible
   - Check file permissions

4. **SSL certificate errors**
   - Set `dms.upload.url.validate-ssl=false` for development
   - Ensure valid SSL certificates in production

### Debug Mode
Enable debug logging for detailed troubleshooting:
```properties
logging.level.com.ascentbusiness.dms_svc.service.UrlDownloadService=DEBUG
```

## Future Enhancements

1. **Additional Protocols**: FTP, SFTP support
2. **Authentication**: Support for authenticated URLs
3. **Caching**: Cache frequently accessed URLs
4. **Async Processing**: Background processing for large files
5. **Progress Tracking**: Real-time upload progress
6. **Retry Logic**: Automatic retry for failed downloads

## Support

For issues or questions regarding the URL Upload feature:
1. Check this documentation
2. Review application logs
3. Verify configuration settings
4. Contact the DMS development team