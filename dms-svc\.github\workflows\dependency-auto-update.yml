name: Automated Dependency Updates

# This workflow automatically checks for dependency updates, tests compatibility,
# and creates pull requests for safe updates

on:
  # Run weekly on Monday mornings
  schedule:
    - cron: '0 9 * * 1'  # Every Monday at 9 AM UTC
  
  # Allow manual triggering
  workflow_dispatch:
    inputs:
      update_type:
        description: 'Type of updates to apply'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - all
      create_pr:
        description: 'Create pull request for updates'
        required: true
        default: true
        type: boolean

env:
  JAVA_VERSION: '21'
  MAVEN_OPTS: '-Xmx2g -XX:+UseG1GC'

jobs:
  check-dependency-updates:
    name: Check for Dependency Updates
    runs-on: ubuntu-latest

    permissions:
      contents: read
      pull-requests: write
      security-events: write

    outputs:
      has-updates: ${{ steps.check-updates.outputs.has-updates }}
      update-summary: ${{ steps.check-updates.outputs.update-summary }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0
      
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          cache: maven
      
      - name: Check for dependency updates
        id: check-updates
        run: |
          # Check for available updates
          mvn versions:display-dependency-updates -DoutputFile=dependency-updates.txt
          mvn versions:display-plugin-updates -DoutputFile=plugin-updates.txt
          
          # Parse updates and create summary
          if grep -q "The following dependencies" dependency-updates.txt || grep -q "The following plugin updates" plugin-updates.txt; then
            echo "has-updates=true" >> $GITHUB_OUTPUT
            
            # Create update summary
            echo "update-summary<<EOF" >> $GITHUB_OUTPUT
            echo "## Available Dependency Updates" >> $GITHUB_OUTPUT
            echo "" >> $GITHUB_OUTPUT
            
            if grep -q "The following dependencies" dependency-updates.txt; then
              echo "### Dependency Updates:" >> $GITHUB_OUTPUT
              grep -A 20 "The following dependencies" dependency-updates.txt | head -20 >> $GITHUB_OUTPUT
              echo "" >> $GITHUB_OUTPUT
            fi
            
            if grep -q "The following plugin updates" plugin-updates.txt; then
              echo "### Plugin Updates:" >> $GITHUB_OUTPUT
              grep -A 20 "The following plugin updates" plugin-updates.txt | head -20 >> $GITHUB_OUTPUT
            fi
            echo "EOF" >> $GITHUB_OUTPUT
          else
            echo "has-updates=false" >> $GITHUB_OUTPUT
            echo "update-summary=No dependency updates available." >> $GITHUB_OUTPUT
          fi
      
      - name: Upload update reports
        uses: actions/upload-artifact@v4
        with:
          name: dependency-update-reports
          path: |
            dependency-updates.txt
            plugin-updates.txt
          retention-days: 30

  apply-dependency-updates:
    name: Apply Dependency Updates
    runs-on: ubuntu-latest
    needs: check-dependency-updates
    if: needs.check-dependency-updates.outputs.has-updates == 'true'

    permissions:
      contents: write
      pull-requests: write
      security-events: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0
      
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          cache: maven
      
      - name: Configure Git
        run: |
          git config --global user.name 'dependency-updater[bot]'
          git config --global user.email 'dependency-updater[bot]@users.noreply.github.com'
      
      - name: Create update branch
        run: |
          BRANCH_NAME="automated-dependency-updates-$(date +%Y%m%d)"
          git checkout -b $BRANCH_NAME
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
      
      - name: Apply dependency updates
        run: |
          # Backup original POM
          cp pom.xml pom.xml.backup
          
          # Apply updates based on input type
          UPDATE_TYPE="${{ github.event.inputs.update_type || 'patch' }}"
          
          case $UPDATE_TYPE in
            "patch")
              echo "Applying patch updates only..."
              mvn versions:use-latest-releases -DallowSnapshots=false -DallowIncrementalUpdates=false -DallowMinorUpdates=false -DallowMajorUpdates=false
              ;;
            "minor")
              echo "Applying patch and minor updates..."
              mvn versions:use-latest-releases -DallowSnapshots=false -DallowIncrementalUpdates=true -DallowMinorUpdates=true -DallowMajorUpdates=false
              ;;
            "all")
              echo "Applying all available updates..."
              mvn versions:use-latest-releases -DallowSnapshots=false -DallowIncrementalUpdates=true -DallowMinorUpdates=true -DallowMajorUpdates=true
              ;;
          esac
          
          # Update plugin versions
          mvn versions:use-latest-versions -DallowSnapshots=false
      
      - name: Generate update summary
        run: |
          # Compare changes
          if ! diff -q pom.xml pom.xml.backup > /dev/null; then
            echo "## Changes Applied" > update-summary.md
            echo "" >> update-summary.md
            echo "The following dependency updates were applied:" >> update-summary.md
            echo "" >> update-summary.md
            echo '```diff' >> update-summary.md
            diff -u pom.xml.backup pom.xml | head -50 >> update-summary.md
            echo '```' >> update-summary.md
            echo "HAS_CHANGES=true" >> $GITHUB_ENV
          else
            echo "No changes were applied." > update-summary.md
            echo "HAS_CHANGES=false" >> $GITHUB_ENV
          fi
      
      - name: Run security scan on updated dependencies
        if: env.HAS_CHANGES == 'true'
        run: |
          # Run OWASP dependency check
          mvn org.owasp:dependency-check-maven:check -DfailBuildOnCVSS=7 -DsuppressionsLocation=.github/security/dependency-check-suppressions.xml
          
          # Check for critical vulnerabilities
          if [ -f target/dependency-check-report.html ]; then
            CRITICAL_COUNT=$(grep -c "CRITICAL" target/dependency-check-report.html || echo "0")
            HIGH_COUNT=$(grep -c "HIGH" target/dependency-check-report.html || echo "0")
            
            if [ "$CRITICAL_COUNT" -gt 0 ] || [ "$HIGH_COUNT" -gt 0 ]; then
              echo "SECURITY_ISSUES=true" >> $GITHUB_ENV
              echo "## ⚠️ Security Issues Detected" >> update-summary.md
              echo "- Critical vulnerabilities: $CRITICAL_COUNT" >> update-summary.md
              echo "- High vulnerabilities: $HIGH_COUNT" >> update-summary.md
              echo "" >> update-summary.md
              echo "Please review the security report before merging." >> update-summary.md
            else
              echo "SECURITY_ISSUES=false" >> $GITHUB_ENV
              echo "## ✅ Security Scan Passed" >> update-summary.md
              echo "No critical or high severity vulnerabilities detected." >> update-summary.md
            fi
          fi
      
      - name: Run compatibility tests
        if: env.HAS_CHANGES == 'true'
        run: |
          echo "## 🧪 Compatibility Testing" >> update-summary.md
          echo "" >> update-summary.md
          
          # Run unit tests
          if mvn clean test -Dspring.profiles.active=test; then
            echo "✅ Unit tests: PASSED" >> update-summary.md
            echo "UNIT_TESTS_PASSED=true" >> $GITHUB_ENV
          else
            echo "❌ Unit tests: FAILED" >> update-summary.md
            echo "UNIT_TESTS_PASSED=false" >> $GITHUB_ENV
          fi
          
          # Run integration tests
          if mvn failsafe:integration-test failsafe:verify -Dspring.profiles.active=integration-test; then
            echo "✅ Integration tests: PASSED" >> update-summary.md
            echo "INTEGRATION_TESTS_PASSED=true" >> $GITHUB_ENV
          else
            echo "❌ Integration tests: FAILED" >> update-summary.md
            echo "INTEGRATION_TESTS_PASSED=false" >> $GITHUB_ENV
          fi
      
      - name: Commit changes
        if: env.HAS_CHANGES == 'true'
        run: |
          git add pom.xml
          git commit -m "deps: automated dependency updates $(date +%Y-%m-%d)
          
          - Applied ${{ github.event.inputs.update_type || 'patch' }} level updates
          - Security scan: ${{ env.SECURITY_ISSUES == 'true' && 'Issues detected' || 'Passed' }}
          - Unit tests: ${{ env.UNIT_TESTS_PASSED == 'true' && 'Passed' || 'Failed' }}
          - Integration tests: ${{ env.INTEGRATION_TESTS_PASSED == 'true' && 'Passed' || 'Failed' }}
          
          Automated by dependency-auto-update workflow"
      
      - name: Push changes
        if: env.HAS_CHANGES == 'true'
        run: |
          git push origin $BRANCH_NAME
      
      - name: Create Pull Request
        if: env.HAS_CHANGES == 'true' && (github.event.inputs.create_pr != 'false')
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const updateSummary = fs.readFileSync('update-summary.md', 'utf8');
            
            const securityIssues = process.env.SECURITY_ISSUES === 'true';
            const unitTestsPassed = process.env.UNIT_TESTS_PASSED === 'true';
            const integrationTestsPassed = process.env.INTEGRATION_TESTS_PASSED === 'true';
            
            const allTestsPassed = unitTestsPassed && integrationTestsPassed;
            const hasIssues = securityIssues || !allTestsPassed;
            
            const title = hasIssues 
              ? `🚨 Automated Dependency Updates - Review Required (${new Date().toISOString().split('T')[0]})`
              : `✅ Automated Dependency Updates (${new Date().toISOString().split('T')[0]})`;
            
            const labels = ['dependencies', 'automated'];
            if (securityIssues) labels.push('security');
            if (!allTestsPassed) labels.push('tests-failing');
            if (!hasIssues) labels.push('auto-merge-candidate');
            
            const body = `
            # Automated Dependency Updates
            
            This pull request contains automated dependency updates generated by the dependency management workflow.
            
            ${updateSummary}
            
            ## Review Checklist
            - [ ] Review dependency changes
            - [ ] Check security scan results
            - [ ] Verify test results
            - [ ] Validate application functionality
            ${hasIssues ? '- [ ] Address any issues before merging' : '- [ ] Ready for merge'}
            
            ## Merge Guidelines
            ${hasIssues 
              ? '⚠️ **Manual review required** - This PR has security issues or test failures that need attention.'
              : '✅ **Auto-merge candidate** - All checks passed, safe for automatic merging.'
            }
            
            ---
            *This PR was automatically created by the dependency-auto-update workflow.*
            `;
            
            const pr = await github.rest.pulls.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              head: process.env.BRANCH_NAME,
              base: 'main',
              body: body
            });
            
            // Add labels
            await github.rest.issues.addLabels({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: pr.data.number,
              labels: labels
            });
            
            // Add reviewers if there are issues
            if (hasIssues) {
              await github.rest.pulls.requestReviewers({
                owner: context.repo.owner,
                repo: context.repo.repo,
                pull_number: pr.data.number,
                reviewers: ['dms-maintainer']
              });
            }
            
            console.log(`Created PR #${pr.data.number}: ${title}`);
      
      - name: Upload artifacts
        if: env.HAS_CHANGES == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: dependency-update-artifacts
          path: |
            update-summary.md
            target/dependency-check-report.html
            target/surefire-reports/
            target/failsafe-reports/
          retention-days: 30

  notify-completion:
    name: Notify Completion
    runs-on: ubuntu-latest
    needs: [check-dependency-updates, apply-dependency-updates]
    if: always()
    
    steps:
      - name: Send notification
        uses: actions/github-script@v7
        with:
          script: |
            const hasUpdates = '${{ needs.check-dependency-updates.outputs.has-updates }}' === 'true';
            const updateSummary = `${{ needs.check-dependency-updates.outputs.update-summary }}`;
            
            if (hasUpdates) {
              console.log('Dependency updates were processed.');
              console.log('Update summary:', updateSummary);
            } else {
              console.log('No dependency updates were available.');
            }
