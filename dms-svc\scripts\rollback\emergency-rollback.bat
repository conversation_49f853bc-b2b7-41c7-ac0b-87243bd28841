@echo off
REM ============================================================================
REM DMS Emergency Rollback Script
REM ============================================================================
REM This script performs complete emergency rollback of the DMS system including
REM application deployment, database, configuration, and all related services.
REM
REM EMERGENCY USE ONLY - This script should only be used in critical situations
REM where immediate rollback is required to restore service availability.
REM
REM Features:
REM - Complete system rollback (application + database + config)
REM - Automated backup creation before rollback
REM - Service health monitoring and validation
REM - Comprehensive logging and incident reporting
REM - Emergency contact notification
REM
REM Prerequisites:
REM - Administrative privileges
REM - Database access credentials
REM - Emergency rollback authorization
REM ============================================================================

setlocal enabledelayedexpansion

REM Set script variables
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..\..
set BACKUP_DIR=%PROJECT_ROOT%\backups\emergency
set LOG_FILE=%PROJECT_ROOT%\logs\emergency-rollback.log
set INCIDENT_FILE=%PROJECT_ROOT%\logs\emergency-incident-%TIMESTAMP%.txt
set TIMESTAMP=%DATE:~-4,4%%DATE:~-10,2%%DATE:~-7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

REM Emergency contact information
set EMERGENCY_CONTACT=<EMAIL>
set INCIDENT_HOTLINE=+1-555-DMS-HELP

echo ============================================================================
echo DMS EMERGENCY ROLLBACK SCRIPT
echo ============================================================================
echo ⚠️  WARNING: EMERGENCY ROLLBACK PROCEDURE ⚠️
echo.
echo This script will perform a complete system rollback including:
echo - Application deployment rollback
echo - Database rollback to last known good state
echo - Configuration rollback
echo - Service restart and validation
echo.
echo Timestamp: %TIMESTAMP%
echo Project Root: %PROJECT_ROOT%
echo Emergency Backup: %BACKUP_DIR%
echo Log File: %LOG_FILE%
echo.
echo Emergency Contact: %EMERGENCY_CONTACT%
echo Incident Hotline: %INCIDENT_HOTLINE%
echo ============================================================================

REM Parse command line arguments
set CONFIRM_FLAG=%1
set ROLLBACK_VERSION=%2
set INCIDENT_ID=%3

REM Check for help request
if "%1"=="--help" goto :show_help
if "%1"=="-h" goto :show_help
if "%1"=="/?" goto :show_help

REM Emergency authorization check
if not "%CONFIRM_FLAG%"=="--confirm" (
    echo.
    echo 🚨 EMERGENCY ROLLBACK AUTHORIZATION REQUIRED 🚨
    echo.
    echo This is an emergency procedure that will:
    echo 1. Stop all DMS services immediately
    echo 2. Rollback application to last known good version
    echo 3. Rollback database to last backup
    echo 4. Reset all configuration to previous state
    echo 5. Restart services and validate functionality
    echo.
    echo ⚠️  THIS OPERATION MAY RESULT IN DATA LOSS ⚠️
    echo.
    echo Please ensure you have:
    echo - Authorization from incident commander
    echo - Incident ID for tracking
    echo - Emergency contact information ready
    echo.
    set /p EMERGENCY_AUTH="Type 'EMERGENCY AUTHORIZED' to proceed: "
    if not "!EMERGENCY_AUTH!"=="EMERGENCY AUTHORIZED" (
        echo [ABORT] Emergency rollback not authorized
        echo [ABORT] Contact emergency hotline: %INCIDENT_HOTLINE%
        goto :end
    )
    
    set /p INCIDENT_ID="Enter incident ID: "
    if "!INCIDENT_ID!"=="" (
        echo [ERROR] Incident ID required for emergency rollback
        goto :error_exit
    )
)

REM Create necessary directories
if not exist "%PROJECT_ROOT%\logs" mkdir "%PROJECT_ROOT%\logs"
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

REM Initialize emergency logging
echo ============================================================================ > "%LOG_FILE%"
echo DMS EMERGENCY ROLLBACK LOG >> "%LOG_FILE%"
echo ============================================================================ >> "%LOG_FILE%"
echo Start Time: %DATE% %TIME% >> "%LOG_FILE%"
echo Incident ID: %INCIDENT_ID% >> "%LOG_FILE%"
echo Authorized By: %USERNAME% >> "%LOG_FILE%"
echo Rollback Version: %ROLLBACK_VERSION% >> "%LOG_FILE%"
echo ============================================================================ >> "%LOG_FILE%"

echo [%TIME%] EMERGENCY ROLLBACK INITIATED - Incident ID: %INCIDENT_ID%
echo [%TIME%] EMERGENCY ROLLBACK INITIATED - Incident ID: %INCIDENT_ID% >> "%LOG_FILE%"

REM Create emergency incident report
call :create_incident_report

REM Step 1: Emergency system assessment
echo.
echo [STEP 1/7] Emergency System Assessment
echo ======================================
echo [%TIME%] Assessing current system state...
echo [%TIME%] STEP 1: Emergency system assessment >> "%LOG_FILE%"

REM Check service status
curl -f -s http://localhost:9092/actuator/health > nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [INFO] DMS service is responding
    echo [INFO] DMS service is responding >> "%LOG_FILE%"
    set SERVICE_STATUS=RESPONDING
) else (
    echo [WARNING] DMS service is not responding
    echo [WARNING] DMS service is not responding >> "%LOG_FILE%"
    set SERVICE_STATUS=NOT_RESPONDING
)

REM Check database connectivity
mysql -h localhost -P 3306 -u dms_user -pdms_password -e "SELECT 1;" > nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [INFO] Database is accessible
    echo [INFO] Database is accessible >> "%LOG_FILE%"
    set DB_STATUS=ACCESSIBLE
) else (
    echo [WARNING] Database is not accessible
    echo [WARNING] Database is not accessible >> "%LOG_FILE%"
    set DB_STATUS=NOT_ACCESSIBLE
)

echo [INFO] System assessment completed
echo [INFO] System assessment completed >> "%LOG_FILE%"

REM Step 2: Emergency backup creation
echo.
echo [STEP 2/7] Emergency Backup Creation
echo ====================================
echo [%TIME%] Creating emergency backup...
echo [%TIME%] STEP 2: Emergency backup creation >> "%LOG_FILE%"

set EMERGENCY_BACKUP=%BACKUP_DIR%\emergency_%TIMESTAMP%
mkdir "%EMERGENCY_BACKUP%" 2>nul

REM Backup current application
if exist "%PROJECT_ROOT%\target\dms-svc-*.jar" (
    copy "%PROJECT_ROOT%\target\dms-svc-*.jar" "%EMERGENCY_BACKUP%\" >> "%LOG_FILE%" 2>&1
    echo [INFO] Application JAR backed up
    echo [INFO] Application JAR backed up >> "%LOG_FILE%"
)

REM Backup current configuration
if exist "%PROJECT_ROOT%\src\main\resources\application.properties" (
    copy "%PROJECT_ROOT%\src\main\resources\application.properties" "%EMERGENCY_BACKUP%\application.properties" >> "%LOG_FILE%" 2>&1
    echo [INFO] Configuration backed up
    echo [INFO] Configuration backed up >> "%LOG_FILE%"
)

REM Backup database if accessible
if "%DB_STATUS%"=="ACCESSIBLE" (
    echo [INFO] Creating database backup...
    mysqldump -h localhost -P 3306 -u dms_user -pdms_password --single-transaction --routines --triggers dms_db > "%EMERGENCY_BACKUP%\database_backup.sql" 2>> "%LOG_FILE%"
    if %ERRORLEVEL% equ 0 (
        echo [INFO] Database backup created
        echo [INFO] Database backup created >> "%LOG_FILE%"
    ) else (
        echo [WARNING] Database backup failed
        echo [WARNING] Database backup failed >> "%LOG_FILE%"
    )
)

echo [INFO] Emergency backup completed: %EMERGENCY_BACKUP%
echo [INFO] Emergency backup completed: %EMERGENCY_BACKUP% >> "%LOG_FILE%"

REM Step 3: Service shutdown
echo.
echo [STEP 3/7] Service Shutdown
echo ===========================
echo [%TIME%] Stopping all DMS services...
echo [%TIME%] STEP 3: Service shutdown >> "%LOG_FILE%"

REM Force stop all Java processes related to DMS
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "dms-svc"') do (
    echo [INFO] Stopping DMS process: %%i
    echo [INFO] Stopping DMS process: %%i >> "%LOG_FILE%"
    taskkill /pid %%i /f >> "%LOG_FILE%" 2>&1
)

REM Wait for services to stop
timeout /t 10 /nobreak > nul

echo [INFO] Service shutdown completed
echo [INFO] Service shutdown completed >> "%LOG_FILE%"

REM Step 4: Database rollback
echo.
echo [STEP 4/7] Database Rollback
echo ============================
echo [%TIME%] Rolling back database...
echo [%TIME%] STEP 4: Database rollback >> "%LOG_FILE%"

if "%DB_STATUS%"=="ACCESSIBLE" (
    REM Find latest database backup
    set LATEST_DB_BACKUP=
    for /f "delims=" %%f in ('dir /b /o-d "%PROJECT_ROOT%\backups\database\*.sql" 2^>nul') do (
        if not defined LATEST_DB_BACKUP set LATEST_DB_BACKUP=%%f
    )
    
    if defined LATEST_DB_BACKUP (
        echo [INFO] Restoring from backup: !LATEST_DB_BACKUP!
        echo [INFO] Restoring from backup: !LATEST_DB_BACKUP! >> "%LOG_FILE%"
        
        mysql -h localhost -P 3306 -u dms_user -pdms_password -e "DROP DATABASE IF EXISTS dms_db;" >> "%LOG_FILE%" 2>&1
        mysql -h localhost -P 3306 -u dms_user -pdms_password -e "CREATE DATABASE dms_db;" >> "%LOG_FILE%" 2>&1
        mysql -h localhost -P 3306 -u dms_user -pdms_password dms_db < "%PROJECT_ROOT%\backups\database\!LATEST_DB_BACKUP!" >> "%LOG_FILE%" 2>&1
        
        if %ERRORLEVEL% equ 0 (
            echo [SUCCESS] Database rollback completed
            echo [SUCCESS] Database rollback completed >> "%LOG_FILE%"
        ) else (
            echo [ERROR] Database rollback failed
            echo [ERROR] Database rollback failed >> "%LOG_FILE%"
            set ROLLBACK_ERRORS=true
        )
    ) else (
        echo [WARNING] No database backup found for rollback
        echo [WARNING] No database backup found for rollback >> "%LOG_FILE%"
    )
) else (
    echo [WARNING] Database not accessible, skipping database rollback
    echo [WARNING] Database not accessible, skipping database rollback >> "%LOG_FILE%"
)

REM Step 5: Application rollback
echo.
echo [STEP 5/7] Application Rollback
echo ===============================
echo [%TIME%] Rolling back application...
echo [%TIME%] STEP 5: Application rollback >> "%LOG_FILE%"

if "%ROLLBACK_VERSION%"=="" (
    REM Find latest application backup
    set LATEST_APP_BACKUP=
    for /f "delims=" %%d in ('dir /b /o-d "%PROJECT_ROOT%\backups\deployments" 2^>nul') do (
        if not defined LATEST_APP_BACKUP set LATEST_APP_BACKUP=%%d
    )
    set ROLLBACK_VERSION=!LATEST_APP_BACKUP!
)

if defined ROLLBACK_VERSION (
    set APP_BACKUP_DIR=%PROJECT_ROOT%\backups\deployments\%ROLLBACK_VERSION%
    if exist "!APP_BACKUP_DIR!" (
        echo [INFO] Restoring application from: %ROLLBACK_VERSION%
        echo [INFO] Restoring application from: %ROLLBACK_VERSION% >> "%LOG_FILE%"
        
        if exist "!APP_BACKUP_DIR!\dms-svc-*.jar" (
            copy "!APP_BACKUP_DIR!\dms-svc-*.jar" "%PROJECT_ROOT%\target\" >> "%LOG_FILE%" 2>&1
        )
        
        if exist "!APP_BACKUP_DIR!\application.properties" (
            copy "!APP_BACKUP_DIR!\application.properties" "%PROJECT_ROOT%\src\main\resources\" >> "%LOG_FILE%" 2>&1
        )
        
        echo [SUCCESS] Application rollback completed
        echo [SUCCESS] Application rollback completed >> "%LOG_FILE%"
    ) else (
        echo [ERROR] Application backup not found: %ROLLBACK_VERSION%
        echo [ERROR] Application backup not found: %ROLLBACK_VERSION% >> "%LOG_FILE%"
        set ROLLBACK_ERRORS=true
    )
) else (
    echo [ERROR] No application backup available for rollback
    echo [ERROR] No application backup available for rollback >> "%LOG_FILE%"
    set ROLLBACK_ERRORS=true
)

REM Step 6: Service restart
echo.
echo [STEP 6/7] Service Restart
echo ==========================
echo [%TIME%] Restarting DMS services...
echo [%TIME%] STEP 6: Service restart >> "%LOG_FILE%"

cd /d "%PROJECT_ROOT%"
for %%f in (target\dms-svc-*.jar) do set JAR_FILE=%%f

if defined JAR_FILE (
    echo [INFO] Starting service with: %JAR_FILE%
    echo [INFO] Starting service with: %JAR_FILE% >> "%LOG_FILE%"
    
    start "DMS Emergency Service" java -jar "%JAR_FILE%" >> "%LOG_FILE%" 2>&1
    
    REM Wait for service to start
    echo [INFO] Waiting for service startup...
    set STARTUP_TIMEOUT=120
    set STARTUP_COUNTER=0
    
    :wait_for_emergency_startup
    timeout /t 5 /nobreak > nul
    set /a STARTUP_COUNTER+=5
    
    curl -f -s http://localhost:9092/actuator/health > nul 2>&1
    if %ERRORLEVEL% equ 0 (
        echo [SUCCESS] Service started successfully
        echo [SUCCESS] Service started successfully >> "%LOG_FILE%"
        goto :emergency_validation
    )
    
    if %STARTUP_COUNTER% geq %STARTUP_TIMEOUT% (
        echo [ERROR] Service failed to start within %STARTUP_TIMEOUT% seconds
        echo [ERROR] Service failed to start within %STARTUP_TIMEOUT% seconds >> "%LOG_FILE%"
        set ROLLBACK_ERRORS=true
        goto :emergency_validation
    )
    
    echo [INFO] Waiting for service startup... (%STARTUP_COUNTER%/%STARTUP_TIMEOUT% seconds)
    goto :wait_for_emergency_startup
) else (
    echo [ERROR] No JAR file found to start
    echo [ERROR] No JAR file found to start >> "%LOG_FILE%"
    set ROLLBACK_ERRORS=true
)

:emergency_validation
REM Step 7: Emergency validation
echo.
echo [STEP 7/7] Emergency Validation
echo ===============================
echo [%TIME%] Validating emergency rollback...
echo [%TIME%] STEP 7: Emergency validation >> "%LOG_FILE%"

REM Test service health
curl -f -s http://localhost:9092/actuator/health > health_check.json 2>&1
if %ERRORLEVEL% equ 0 (
    findstr "UP" health_check.json > nul 2>&1
    if %ERRORLEVEL% equ 0 (
        echo [SUCCESS] Service health check: PASSED
        echo [SUCCESS] Service health check: PASSED >> "%LOG_FILE%"
        set HEALTH_STATUS=PASSED
    ) else (
        echo [ERROR] Service health check: FAILED
        echo [ERROR] Service health check: FAILED >> "%LOG_FILE%"
        set HEALTH_STATUS=FAILED
        set ROLLBACK_ERRORS=true
    )
) else (
    echo [ERROR] Service not responding to health checks
    echo [ERROR] Service not responding to health checks >> "%LOG_FILE%"
    set HEALTH_STATUS=NOT_RESPONDING
    set ROLLBACK_ERRORS=true
)

del health_check.json 2>nul

REM Test database connectivity
mysql -h localhost -P 3306 -u dms_user -pdms_password dms_db -e "SELECT COUNT(*) FROM documents;" > nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [SUCCESS] Database connectivity: PASSED
    echo [SUCCESS] Database connectivity: PASSED >> "%LOG_FILE%"
    set DB_CONNECTIVITY=PASSED
) else (
    echo [ERROR] Database connectivity: FAILED
    echo [ERROR] Database connectivity: FAILED >> "%LOG_FILE%"
    set DB_CONNECTIVITY=FAILED
    set ROLLBACK_ERRORS=true
)

REM Generate emergency rollback report
call :generate_emergency_report

REM Final status
echo.
echo ============================================================================
if "%ROLLBACK_ERRORS%"=="true" (
    echo 🚨 EMERGENCY ROLLBACK COMPLETED WITH ERRORS 🚨
    echo.
    echo Some components failed to rollback properly.
    echo Manual intervention may be required.
    echo.
    echo Next Steps:
    echo 1. Review error log: %LOG_FILE%
    echo 2. Contact emergency support: %EMERGENCY_CONTACT%
    echo 3. Escalate to incident commander
    echo 4. Consider manual recovery procedures
) else (
    echo ✅ EMERGENCY ROLLBACK COMPLETED SUCCESSFULLY ✅
    echo.
    echo System has been rolled back to previous stable state.
    echo.
    echo Next Steps:
    echo 1. Monitor system performance
    echo 2. Verify user functionality
    echo 3. Update incident status
    echo 4. Plan forward fix strategy
)
echo.
echo Incident ID: %INCIDENT_ID%
echo Emergency Report: %INCIDENT_FILE%
echo Emergency Contact: %EMERGENCY_CONTACT%
echo ============================================================================

goto :end

:create_incident_report
(
echo EMERGENCY INCIDENT REPORT
echo =========================
echo Incident ID: %INCIDENT_ID%
echo Timestamp: %DATE% %TIME%
echo Authorized By: %USERNAME%
echo Computer: %COMPUTERNAME%
echo.
echo INCIDENT DETAILS:
echo - Emergency rollback initiated
echo - System state: Under investigation
echo - Impact: Service disruption
echo - Severity: Critical
echo.
echo EMERGENCY CONTACTS:
echo - Primary: %EMERGENCY_CONTACT%
echo - Hotline: %INCIDENT_HOTLINE%
echo.
echo ROLLBACK ACTIONS:
echo - Application rollback: In progress
echo - Database rollback: In progress
echo - Configuration rollback: In progress
echo - Service restart: In progress
echo.
echo STATUS: IN PROGRESS
) > "%INCIDENT_FILE%"
exit /b 0

:generate_emergency_report
(
echo.
echo EMERGENCY ROLLBACK SUMMARY:
echo - Service Health: %HEALTH_STATUS%
echo - Database Connectivity: %DB_CONNECTIVITY%
echo - Emergency Backup: %EMERGENCY_BACKUP%
echo - Rollback Version: %ROLLBACK_VERSION%
echo.
echo VALIDATION RESULTS:
echo - Service Status: %HEALTH_STATUS%
echo - Database Status: %DB_CONNECTIVITY%
echo - Overall Status: %ROLLBACK_STATUS%
echo.
echo RECOVERY INFORMATION:
echo - Emergency backup location: %EMERGENCY_BACKUP%
echo - Log file: %LOG_FILE%
echo - Incident report: %INCIDENT_FILE%
echo.
echo STATUS: COMPLETED
echo Completion Time: %DATE% %TIME%
) >> "%INCIDENT_FILE%"
exit /b 0

:show_help
echo.
echo DMS Emergency Rollback Script
echo.
echo Usage: %~nx0 [--confirm] [rollback_version] [incident_id]
echo        %~nx0 --help
echo.
echo Parameters:
echo   --confirm         - Skip authorization prompts (for automated use)
echo   rollback_version  - Specific version to rollback to (optional)
echo   incident_id       - Emergency incident tracking ID (optional)
echo   --help, -h, /?    - Show this help message
echo.
echo Examples:
echo   %~nx0                                    # Interactive emergency rollback
echo   %~nx0 --confirm v1.2.3 INC-2024-001     # Automated rollback to v1.2.3
echo   %~nx0 --help                             # Show help information
echo.
echo Emergency Contacts:
echo   Email: %EMERGENCY_CONTACT%
echo   Hotline: %INCIDENT_HOTLINE%
echo.
echo ⚠️  WARNING: This script performs complete system rollback including:
echo   - Application deployment rollback
echo   - Database rollback to last known good state
echo   - Configuration rollback
echo   - Service restart and validation
echo.
echo   Use only in critical emergency situations!
echo.
goto :end

:error_exit
echo [%TIME%] Emergency rollback script failed. Check log file: %LOG_FILE%
echo [%TIME%] Contact emergency support immediately: %EMERGENCY_CONTACT%
exit /b 1

:end
echo [%TIME%] Emergency rollback script completed.
endlocal
pause
