@echo off
REM Generate HTML Test Report from Surefire XML Results
REM This script creates a simple HTML report from the XML test results

setlocal enabledelayedexpansion

set "REPORT_DIR=%~dp0..\target\site"
set "XML_DIR=%~dp0..\target\surefire-reports"
set "HTML_FILE=%REPORT_DIR%\surefire-report.html"

REM Create target/site directory if it doesn't exist
if not exist "%REPORT_DIR%" mkdir "%REPORT_DIR%"

echo Generating HTML test report from XML results...

REM Start HTML file
echo ^<!DOCTYPE html^> > "%HTML_FILE%"
echo ^<html^> >> "%HTML_FILE%"
echo ^<head^> >> "%HTML_FILE%"
echo ^<title^>Surefire Test Report^</title^> >> "%HTML_FILE%"
echo ^<style^> >> "%HTML_FILE%"
echo body { font-family: Arial, sans-serif; margin: 20px; } >> "%HTML_FILE%"
echo table { border-collapse: collapse; width: 100%%; } >> "%HTML_FILE%"
echo th, td { border: 1px solid #ddd; padding: 8px; text-align: left; } >> "%HTML_FILE%"
echo th { background-color: #f2f2f2; } >> "%HTML_FILE%"
echo .success { color: green; } >> "%HTML_FILE%"
echo .failure { color: red; } >> "%HTML_FILE%"
echo .error { color: orange; } >> "%HTML_FILE%"
echo .skipped { color: blue; } >> "%HTML_FILE%"
echo .summary { background-color: #f9f9f9; padding: 10px; margin: 10px 0; } >> "%HTML_FILE%"
echo ^</style^> >> "%HTML_FILE%"
echo ^</head^> >> "%HTML_FILE%"
echo ^<body^> >> "%HTML_FILE%"
echo ^<h1^>Surefire Test Report^</h1^> >> "%HTML_FILE%"

REM Initialize counters
set /a TOTAL_TESTS=0
set /a TOTAL_FAILURES=0
set /a TOTAL_ERRORS=0
set /a TOTAL_SKIPPED=0
set /a TOTAL_SUCCESS=0

REM Count test results from XML files
for %%f in ("%XML_DIR%\TEST-*.xml") do (
    REM Use PowerShell to parse XML and extract test counts
    for /f "tokens=*" %%i in ('powershell -Command "try { [xml]$xml = Get-Content \"%%f\"; $ts = $xml.testsuite; Write-Output \"$($ts.tests),$($ts.failures),$($ts.errors),$($ts.skipped)\" } catch { Write-Output \"0,0,0,0\" }"') do (
        for /f "tokens=1,2,3,4 delims=," %%a in ("%%i") do (
            set /a TOTAL_TESTS+=%%a
            set /a TOTAL_FAILURES+=%%b
            set /a TOTAL_ERRORS+=%%c
            set /a TOTAL_SKIPPED+=%%d
        )
    )
)

set /a TOTAL_SUCCESS=TOTAL_TESTS-TOTAL_FAILURES-TOTAL_ERRORS-TOTAL_SKIPPED

REM Add summary section
echo ^<div class="summary"^> >> "%HTML_FILE%"
echo ^<h2^>Summary^</h2^> >> "%HTML_FILE%"
echo ^<p^>^<strong^>Total Tests:^</strong^> !TOTAL_TESTS!^</p^> >> "%HTML_FILE%"
echo ^<p^>^<strong class="success"^>Success:^</strong^> !TOTAL_SUCCESS!^</p^> >> "%HTML_FILE%"
echo ^<p^>^<strong class="failure"^>Failures:^</strong^> !TOTAL_FAILURES!^</p^> >> "%HTML_FILE%"
echo ^<p^>^<strong class="error"^>Errors:^</strong^> !TOTAL_ERRORS!^</p^> >> "%HTML_FILE%"
echo ^<p^>^<strong class="skipped"^>Skipped:^</strong^> !TOTAL_SKIPPED!^</p^> >> "%HTML_FILE%"
echo ^</div^> >> "%HTML_FILE%"

REM Add test results table
echo ^<h2^>Test Results^</h2^> >> "%HTML_FILE%"
echo ^<table^> >> "%HTML_FILE%"
echo ^<tr^>^<th^>Test Class^</th^>^<th^>Tests^</th^>^<th^>Failures^</th^>^<th^>Errors^</th^>^<th^>Skipped^</th^>^<th^>Time^</th^>^<th^>Status^</th^>^</tr^> >> "%HTML_FILE%"

REM Process each XML file
for %%f in ("%XML_DIR%\TEST-*.xml") do (
    REM Use PowerShell to parse XML and extract test information
    powershell -Command "try { [xml]$xml = Get-Content \"%%f\"; $ts = $xml.testsuite; $name = $ts.name; $tests = $ts.tests; $failures = $ts.failures; $errors = $ts.errors; $skipped = $ts.skipped; $time = $ts.time; $status = if ($failures -eq 0 -and $errors -eq 0) { 'SUCCESS' } else { 'FAILURE' }; $class = if ($failures -eq 0 -and $errors -eq 0) { 'success' } else { 'failure' }; Write-Output \"^<tr^>^<td^>$name^</td^>^<td^>$tests^</td^>^<td^>$failures^</td^>^<td^>$errors^</td^>^<td^>$skipped^</td^>^<td^>$time^</td^>^<td class=`\"$class`\"^>$status^</td^>^</tr^>\" } catch { Write-Output \"^<tr^>^<td^>Error parsing %%f^</td^>^<td^>-^</td^>^<td^>-^</td^>^<td^>-^</td^>^<td^>-^</td^>^<td^>-^</td^>^<td class=`\"error`\"^>ERROR^</td^>^</tr^>\" }" >> "%HTML_FILE%"
)

echo ^</table^> >> "%HTML_FILE%"

REM Add footer
echo ^<hr^> >> "%HTML_FILE%"
echo ^<p^>^<small^>Generated on !DATE! at !TIME!^</small^>^</p^> >> "%HTML_FILE%"
echo ^</body^> >> "%HTML_FILE%"
echo ^</html^> >> "%HTML_FILE%"

echo ✓ HTML test report generated: %HTML_FILE%
echo.
echo You can open the report at: file:///%HTML_FILE:\=/%

endlocal
