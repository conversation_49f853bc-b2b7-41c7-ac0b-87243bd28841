global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: '${SMTP_PASSWORD}'

# Templates for alert notifications
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Routing configuration
route:
  group_by: ['alertname', 'service', 'severity']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  routes:
    # Critical alerts - immediate notification
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 15m
      routes:
        # Database critical issues
        - match:
            category: database
          receiver: 'database-critical'
        # Application down
        - match:
            alertname: DMSApplicationDown
          receiver: 'application-down'
        # Compliance violations
        - match:
            category: compliance
          receiver: 'compliance-critical'

    # Warning alerts - standard notification
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 30s
      repeat_interval: 2h
      routes:
        # Performance warnings
        - match:
            category: performance
          receiver: 'performance-warnings'
        # Security warnings
        - match:
            category: security
          receiver: 'security-warnings'

    # Business alerts
    - match:
        category: business
      receiver: 'business-alerts'
      group_wait: 1m
      repeat_interval: 4h

    # Maintenance window - suppress alerts
    - match:
        alertname: MaintenanceMode
      receiver: 'null'

# Inhibition rules to suppress redundant alerts
inhibit_rules:
  # Suppress all alerts if application is down
  - source_match:
      alertname: DMSApplicationDown
    target_match_re:
      alertname: DMS.*
    equal: ['service']

  # Suppress memory warnings if critical memory alert is firing
  - source_match:
      alertname: DMSCriticalHeapMemoryUsage
    target_match:
      alertname: DMSHighHeapMemoryUsage
    equal: ['service', 'instance']

  # Suppress database query warnings if database is down
  - source_match:
      alertname: DMSDatabaseDown
    target_match:
      alertname: DMSSlowDatabaseQueries
    equal: ['service']

# Notification receivers
receivers:
  # Default receiver for unmatched alerts
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: '[DMS] Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          {{ end }}

  # Critical alerts - multiple channels
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[DMS CRITICAL] {{ .GroupLabels.alertname }}'
        body: |
          🚨 CRITICAL ALERT 🚨
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          Runbook: {{ .Annotations.runbook_url }}
          
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#dms-alerts'
        title: '🚨 DMS Critical Alert'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          {{ .Annotations.description }}
          
          *Severity:* {{ .Labels.severity }}
          *Service:* {{ .Labels.service }}
          *Runbook:* {{ .Annotations.runbook_url }}
          {{ end }}
        color: 'danger'
    pagerduty_configs:
      - routing_key: '${PAGERDUTY_INTEGRATION_KEY}'
        description: '{{ .GroupLabels.alertname }}: {{ .CommonAnnotations.summary }}'
        severity: 'critical'
        details:
          service: '{{ .GroupLabels.service }}'
          alertname: '{{ .GroupLabels.alertname }}'
          description: '{{ .CommonAnnotations.description }}'

  # Database critical alerts
  - name: 'database-critical'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[DMS DATABASE CRITICAL] {{ .GroupLabels.alertname }}'
        body: |
          🔥 DATABASE CRITICAL ALERT 🔥
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Database Issue: {{ .Labels.category }}
          Instance: {{ .Labels.instance }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}

  # Application down alerts
  - name: 'application-down'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[DMS DOWN] Application Unavailable'
        body: |
          🚨 DMS APPLICATION DOWN 🚨
          
          The DMS service is currently unavailable.
          
          {{ range .Alerts }}
          Instance: {{ .Labels.instance }}
          Time Down: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#incidents'
        title: '🚨 DMS Application Down'
        text: 'DMS service is unavailable. Immediate attention required!'
        color: 'danger'

  # Compliance critical alerts
  - name: 'compliance-critical'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[DMS COMPLIANCE] Critical Violation Detected'
        body: |
          ⚠️ COMPLIANCE VIOLATION DETECTED ⚠️
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Violation Type: {{ .Labels.type }}
          Severity: {{ .Labels.severity }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}

  # Warning alerts
  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[DMS WARNING] {{ .GroupLabels.alertname }}'
        body: |
          ⚠️ Warning Alert
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Category: {{ .Labels.category }}
          Instance: {{ .Labels.instance }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}

  # Performance warnings
  - name: 'performance-warnings'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#dms-performance'
        title: '⚠️ DMS Performance Warning'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          {{ .Annotations.description }}
          
          *Category:* {{ .Labels.category }}
          *Instance:* {{ .Labels.instance }}
          {{ end }}
        color: 'warning'

  # Security warnings
  - name: 'security-warnings'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[DMS SECURITY] {{ .GroupLabels.alertname }}'
        body: |
          🔒 Security Warning
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Security Event: {{ .Labels.category }}
          Instance: {{ .Labels.instance }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}

  # Business alerts
  - name: 'business-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[DMS BUSINESS] {{ .GroupLabels.alertname }}'
        body: |
          📊 Business Metric Alert
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Business Impact: {{ .Labels.category }}
          Instance: {{ .Labels.instance }}
          {{ end }}

  # Null receiver for suppressed alerts
  - name: 'null'
