# Simple comprehensive health check for DMS Service
$baseUrl = "http://localhost:9093"
$healthyCount = 0
$totalTests = 0

Write-Host "=== DMS Service Health Check ===" -ForegroundColor Cyan
Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Gray

# Test 1: Application Health
Write-Host "`nTesting Application Health..." -ForegroundColor Yellow
$totalTests++
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/actuator/health" -TimeoutSec 10
    if ($response.status -eq "UP") {
        Write-Host "Application Health: HEALTHY (Status: $($response.status))" -ForegroundColor Green
        $healthyCount++
    } else {
        Write-Host "Application Health: UNHEALTHY (Status: $($response.status))" -ForegroundColor Red
    }
} catch {
    Write-Host "Application Health: ERROR - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Database Connectivity
Write-Host "`nTesting Database Connectivity..." -ForegroundColor Yellow
$totalTests++
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/actuator/health" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "Database Connectivity: HEALTHY (Health endpoint accessible)" -ForegroundColor Green
        $healthyCount++
    } else {
        Write-Host "Database Connectivity: UNHEALTHY" -ForegroundColor Red
    }
} catch {
    Write-Host "Database Connectivity: ERROR - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: GraphQL Endpoint
Write-Host "`nTesting GraphQL Endpoint..." -ForegroundColor Yellow
$totalTests++
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/graphql" -Method Get -TimeoutSec 10
    if ($response.StatusCode -eq 401) {
        Write-Host "GraphQL Endpoint: HEALTHY (401 Unauthorized - security working)" -ForegroundColor Green
        $healthyCount++
    } else {
        Write-Host "GraphQL Endpoint: UNEXPECTED (Status: $($response.StatusCode))" -ForegroundColor Yellow
    }
} catch {
    Write-Host "GraphQL Endpoint: ERROR - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Security Configuration
Write-Host "`nTesting Security Configuration..." -ForegroundColor Yellow
$totalTests++
try {
    $query = '{"query":"query { __schema { queryType { name } } }"}'
    Invoke-RestMethod -Uri "$baseUrl/graphql" -Method Post -ContentType "application/json" -Body $query -TimeoutSec 10
    Write-Host "Security Configuration: FAIL (Should require authentication)" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode.value__ -eq 401) {
        Write-Host "Security Configuration: HEALTHY (401 Unauthorized)" -ForegroundColor Green
        $healthyCount++
    } else {
        Write-Host "Security Configuration: ERROR - $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 5: Application Performance
Write-Host "`nTesting Application Performance..." -ForegroundColor Yellow
$totalTests++
try {
    $startTime = Get-Date
    $response = Invoke-WebRequest -Uri "$baseUrl/actuator/health" -TimeoutSec 10
    $endTime = Get-Date
    $responseTime = ($endTime - $startTime).TotalMilliseconds
    
    if ($responseTime -lt 5000) {
        Write-Host "Application Performance: HEALTHY (Response time: $([math]::Round($responseTime, 2))ms)" -ForegroundColor Green
        $healthyCount++
    } else {
        Write-Host "Application Performance: SLOW (Response time: $([math]::Round($responseTime, 2))ms)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Application Performance: ERROR - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Actuator Endpoints
Write-Host "`nTesting Actuator Endpoints..." -ForegroundColor Yellow
$totalTests++
try {
    $endpoints = @("/actuator", "/actuator/health", "/actuator/info")
    $successCount = 0
    
    foreach ($endpoint in $endpoints) {
        try {
            $response = Invoke-WebRequest -Uri "$baseUrl$endpoint" -TimeoutSec 5
            if ($response.StatusCode -eq 200) {
                $successCount++
            }
        } catch {
            # Continue checking other endpoints
        }
    }
    
    if ($successCount -eq $endpoints.Count) {
        Write-Host "Actuator Endpoints: HEALTHY (All $($endpoints.Count) endpoints accessible)" -ForegroundColor Green
        $healthyCount++
    } else {
        Write-Host "Actuator Endpoints: PARTIAL ($successCount/$($endpoints.Count) endpoints accessible)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Actuator Endpoints: ERROR - $($_.Exception.Message)" -ForegroundColor Red
}

# Generate Summary
Write-Host "`n=== Health Check Summary ===" -ForegroundColor Cyan
Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Healthy: $healthyCount" -ForegroundColor Green
Write-Host "Issues: $($totalTests - $healthyCount)" -ForegroundColor $(if ($totalTests - $healthyCount -eq 0) { "Green" } else { "Red" })

$healthPercentage = [math]::Round(($healthyCount / $totalTests) * 100, 1)
Write-Host "Health Score: $healthPercentage%" -ForegroundColor $(if ($healthPercentage -ge 80) { "Green" } elseif ($healthPercentage -ge 60) { "Yellow" } else { "Red" })

if ($healthyCount -eq $totalTests) {
    Write-Host "`nOVERALL STATUS: ALL SYSTEMS HEALTHY!" -ForegroundColor Green
} elseif ($healthPercentage -ge 80) {
    Write-Host "`nOVERALL STATUS: MOSTLY HEALTHY" -ForegroundColor Yellow
} else {
    Write-Host "`nOVERALL STATUS: NEEDS ATTENTION" -ForegroundColor Red
}

Write-Host "`nHealth check completed at $(Get-Date)" -ForegroundColor Gray
