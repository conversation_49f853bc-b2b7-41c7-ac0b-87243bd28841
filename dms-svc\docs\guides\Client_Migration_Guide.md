# Client Migration Guide: REST to GraphQL

## Overview

This guide provides step-by-step instructions for migrating client applications from REST endpoints to GraphQL operations in the DMS Service. The migration ensures improved performance, better data fetching, and enhanced developer experience.

## Table of Contents

1. [Migration Benefits](#migration-benefits)
2. [Before You Start](#before-you-start)
3. [REST to GraphQL Mapping](#rest-to-graphql-mapping)
4. [Client Implementation Examples](#client-implementation-examples)
5. [Authentication Changes](#authentication-changes)
6. [Error Handling Updates](#error-handling-updates)
7. [Performance Optimizations](#performance-optimizations)
8. [Testing Your Migration](#testing-your-migration)
9. [Troubleshooting](#troubleshooting)

## Migration Benefits

### Why Migrate to GraphQL?

✅ **Reduced Over-fetching**: Get exactly the data you need  
✅ **Single Endpoint**: One URL for all operations  
✅ **Strong Type System**: Better development experience  
✅ **Real-time Capabilities**: Built-in subscription support  
✅ **Better Performance**: Optimized query execution  
✅ **Enhanced Monitoring**: Detailed operation tracking  

### Performance Improvements
- **40+ req/sec** sustained throughput
- **<500ms** average response time for complex queries
- **Reduced Network Calls**: Combine multiple REST calls into single GraphQL query
- **Optimized Data Transfer**: Only requested fields are returned

## Before You Start

### Prerequisites
- GraphQL client library (Apollo Client, Relay, or similar)
- Updated authentication tokens (JWT)
- Understanding of GraphQL query syntax
- Access to GraphQL playground: `http://localhost:8080/graphql`

### Client Libraries

#### JavaScript/TypeScript
```bash
npm install @apollo/client graphql
# or
npm install graphql-request
```

#### Java
```xml
<dependency>
    <groupId>com.graphql-java</groupId>
    <artifactId>graphql-spring-boot-starter</artifactId>
    <version>11.1.0</version>
</dependency>
```

#### Python
```bash
pip install gql[all]
```

## REST to GraphQL Mapping

### Document Operations

#### Document Upload
**REST (OLD):**
```http
POST /api/documents/upload
Content-Type: multipart/form-data

file: [binary data]
name: "Document Name"
description: "Document Description"
```

**GraphQL (NEW):**
```graphql
mutation uploadDocument($input: EnhancedDocumentUploadInput!) {
  uploadDocumentEnhanced(input: $input) {
    success
    uploadId
    fileName
    document {
      id
      name
      mimeType
      uploadedAt
    }
  }
}
```

#### Document Retrieval
**REST (OLD):**
```http
GET /api/documents/{id}
```

**GraphQL (NEW):**
```graphql
query getDocument($id: ID!) {
  getDocument(id: $id) {
    id
    name
    originalFileName
    mimeType
    fileSize
    uploadedBy
    uploadedAt
    description
    tags
  }
}
```

#### Document Search
**REST (OLD):**
```http
GET /api/documents/search?query=test&mimeType=pdf&page=0&size=20
```

**GraphQL (NEW):**
```graphql
query searchDocuments($query: String!, $filters: DocumentSearchFilters, $pagination: PaginationInput) {
  searchDocuments(query: $query, filters: $filters, pagination: $pagination) {
    totalCount
    documents {
      id
      name
      relevanceScore
      highlights
    }
  }
}
```

### Audit Operations

#### Get Audit Logs
**REST (OLD):**
```http
GET /api/audit/logs?userId=<EMAIL>&action=UPLOAD&page=0&size=20
```

**GraphQL (NEW):**
```graphql
query getAuditLogs($filter: AuditLogFilter, $pagination: PaginationInput) {
  getAuditLogs(filter: $filter, pagination: $pagination) {
    totalCount
    auditLogs {
      id
      action
      userId
      timestamp
      success
      details
    }
  }
}
```

### Webhook Operations

#### Create Webhook
**REST (OLD):**
```http
POST /api/webhooks
Content-Type: application/json

{
  "name": "My Webhook",
  "url": "https://example.com/webhook",
  "httpMethod": "POST"
}
```

**GraphQL (NEW):**
```graphql
mutation createWebhookEndpoint($input: WebhookEndpointInput!) {
  createWebhookEndpoint(input: $input) {
    success
    webhookEndpoint {
      id
      name
      url
      isActive
    }
  }
}
```

## Client Implementation Examples

### JavaScript/Apollo Client

#### Setup
```javascript
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

const httpLink = createHttpLink({
  uri: 'http://localhost:8080/graphql',
});

const authLink = setContext((_, { headers }) => {
  const token = localStorage.getItem('jwt-token');
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
    }
  }
});

const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache()
});
```

#### Document Upload Example
```javascript
import { gql, useMutation } from '@apollo/client';

const UPLOAD_DOCUMENT = gql`
  mutation uploadDocument($input: EnhancedDocumentUploadInput!) {
    uploadDocumentEnhanced(input: $input) {
      success
      fileName
      document {
        id
        name
        mimeType
      }
    }
  }
`;

function DocumentUpload() {
  const [uploadDocument, { loading, error, data }] = useMutation(UPLOAD_DOCUMENT);

  const handleUpload = async (file, name, description) => {
    try {
      const result = await uploadDocument({
        variables: {
          input: {
            name,
            description,
            file: file, // File object from input
            tags: ["uploaded", "client"]
          }
        }
      });
      console.log('Upload successful:', result.data.uploadDocumentEnhanced);
    } catch (err) {
      console.error('Upload failed:', err);
    }
  };

  return (
    <div>
      {loading && <p>Uploading...</p>}
      {error && <p>Error: {error.message}</p>}
      {data && <p>Success: {data.uploadDocumentEnhanced.fileName}</p>}
    </div>
  );
}
```

#### Document Query Example
```javascript
import { gql, useQuery } from '@apollo/client';

const GET_DOCUMENT = gql`
  query getDocument($id: ID!) {
    getDocument(id: $id) {
      id
      name
      mimeType
      fileSize
      uploadedAt
      description
    }
  }
`;

function DocumentView({ documentId }) {
  const { loading, error, data } = useQuery(GET_DOCUMENT, {
    variables: { id: documentId }
  });

  if (loading) return <p>Loading...</p>;
  if (error) return <p>Error: {error.message}</p>;

  const document = data.getDocument;
  return (
    <div>
      <h2>{document.name}</h2>
      <p>Type: {document.mimeType}</p>
      <p>Size: {document.fileSize} bytes</p>
      <p>Uploaded: {new Date(document.uploadedAt).toLocaleDateString()}</p>
      <p>Description: {document.description}</p>
    </div>
  );
}
```

### Java Client Example

```java
import com.graphql.client.GraphQLClient;
import com.graphql.client.GraphQLRequest;
import com.graphql.client.GraphQLResponse;

public class DocumentService {
    private final GraphQLClient client;

    public DocumentService() {
        this.client = GraphQLClient.builder()
                .url("http://localhost:8080/graphql")
                .header("Authorization", "Bearer " + getJwtToken())
                .build();
    }

    public Document getDocument(String documentId) {
        String query = """
            query getDocument($id: ID!) {
              getDocument(id: $id) {
                id
                name
                mimeType
                fileSize
                uploadedAt
              }
            }
            """;

        GraphQLRequest request = GraphQLRequest.builder()
                .query(query)
                .variable("id", documentId)
                .build();

        GraphQLResponse response = client.execute(request);
        return response.getData("getDocument", Document.class);
    }

    public UploadResult uploadDocument(String name, String description, byte[] fileContent) {
        String mutation = """
            mutation uploadDocument($input: EnhancedDocumentUploadInput!) {
              uploadDocumentEnhanced(input: $input) {
                success
                fileName
                document {
                  id
                  name
                }
              }
            }
            """;

        Map<String, Object> input = Map.of(
                "name", name,
                "description", description,
                "file", Base64.getEncoder().encodeToString(fileContent)
        );

        GraphQLRequest request = GraphQLRequest.builder()
                .query(mutation)
                .variable("input", input)
                .build();

        GraphQLResponse response = client.execute(request);
        return response.getData("uploadDocumentEnhanced", UploadResult.class);
    }
}
```

### Python Client Example

```python
from gql import gql, Client
from gql.transport.requests import RequestsHTTPTransport

# Setup client
transport = RequestsHTTPTransport(
    url="http://localhost:8080/graphql",
    headers={"Authorization": f"Bearer {jwt_token}"}
)
client = Client(transport=transport, fetch_schema_from_transport=True)

# Document query
def get_document(document_id):
    query = gql("""
        query getDocument($id: ID!) {
          getDocument(id: $id) {
            id
            name
            mimeType
            fileSize
            uploadedAt
          }
        }
    """)
    
    result = client.execute(query, variable_values={"id": document_id})
    return result["getDocument"]

# Document upload
def upload_document(name, description, file_content):
    mutation = gql("""
        mutation uploadDocument($input: EnhancedDocumentUploadInput!) {
          uploadDocumentEnhanced(input: $input) {
            success
            fileName
            document {
              id
              name
            }
          }
        }
    """)
    
    variables = {
        "input": {
            "name": name,
            "description": description,
            "file": base64.b64encode(file_content).decode('utf-8')
        }
    }
    
    result = client.execute(mutation, variable_values=variables)
    return result["uploadDocumentEnhanced"]
```

## Authentication Changes

### JWT Token Usage
GraphQL uses the same JWT authentication as REST, but with a single endpoint:

**Before (REST):**
```http
GET /api/documents/123
Authorization: Bearer <jwt-token>
```

**After (GraphQL):**
```http
POST /graphql
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "query": "query { getDocument(id: \"123\") { name } }"
}
```

### Role-Based Access
- **PUBLIC**: Test case operations, health checks
- **USER**: Document operations, basic queries  
- **ADMIN**: Audit operations, system management

## Error Handling Updates

### GraphQL Error Format
```json
{
  "errors": [
    {
      "message": "Document not found",
      "locations": [{"line": 2, "column": 3}],
      "path": ["getDocument"],
      "extensions": {
        "code": "DOCUMENT_NOT_FOUND",
        "timestamp": "2024-06-29T10:30:00Z"
      }
    }
  ],
  "data": null
}
```

### Client Error Handling
```javascript
// Apollo Client
const { loading, error, data } = useQuery(GET_DOCUMENT);

if (error) {
  error.graphQLErrors.forEach(({ message, locations, path }) => {
    console.log(`GraphQL error: Message: ${message}, Location: ${locations}, Path: ${path}`);
  });
  
  if (error.networkError) {
    console.log(`Network error: ${error.networkError}`);
  }
}
```

## Performance Optimizations

### Query Optimization
```graphql
# Good: Request only needed fields
query getDocument($id: ID!) {
  getDocument(id: $id) {
    id
    name
    mimeType
  }
}

# Avoid: Requesting unnecessary fields
query getDocument($id: ID!) {
  getDocument(id: $id) {
    id
    name
    mimeType
    fileSize
    uploadedAt
    description
    tags
    # ... all fields
  }
}
```

### Batch Queries
```graphql
# Combine multiple operations
query getBatchData($docId: ID!, $auditFilter: AuditLogFilter) {
  document: getDocument(id: $docId) {
    id
    name
    mimeType
  }
  auditLogs: getAuditLogs(filter: $auditFilter) {
    totalCount
    auditLogs {
      id
      action
      timestamp
    }
  }
}
```

## Testing Your Migration

### Unit Testing
```javascript
import { MockedProvider } from '@apollo/client/testing';

const mocks = [
  {
    request: {
      query: GET_DOCUMENT,
      variables: { id: '123' }
    },
    result: {
      data: {
        getDocument: {
          id: '123',
          name: 'Test Document',
          mimeType: 'application/pdf'
        }
      }
    }
  }
];

test('renders document', async () => {
  render(
    <MockedProvider mocks={mocks} addTypename={false}>
      <DocumentView documentId="123" />
    </MockedProvider>
  );
  
  await waitFor(() => {
    expect(screen.getByText('Test Document')).toBeInTheDocument();
  });
});
```

### Integration Testing
```bash
# Test GraphQL endpoint
curl -X POST http://localhost:8080/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "query": "query { testCaseHealthCheck { status service } }"
  }'
```

## Troubleshooting

### Common Issues

#### 1. Authentication Errors
```json
{
  "errors": [
    {
      "message": "Authentication required",
      "extensions": { "code": "AUTHENTICATION_REQUIRED" }
    }
  ]
}
```
**Solution**: Ensure JWT token is included in Authorization header.

#### 2. Query Syntax Errors
```json
{
  "errors": [
    {
      "message": "Validation error of type FieldUndefined",
      "extensions": { "code": "VALIDATION_ERROR" }
    }
  ]
}
```
**Solution**: Check field names against GraphQL schema.

#### 3. File Upload Issues
**Problem**: Multipart uploads not working  
**Solution**: Use proper multipart format or base64 encoding for small files.

### Debug Tools
- **GraphQL Playground**: `http://localhost:8080/graphql`
- **Schema Introspection**: Query `__schema` for available operations
- **Apollo DevTools**: Browser extension for debugging

## Migration Checklist

- [ ] Install GraphQL client library
- [ ] Update authentication to use single GraphQL endpoint
- [ ] Replace REST calls with GraphQL queries/mutations
- [ ] Update error handling for GraphQL format
- [ ] Optimize queries to request only needed fields
- [ ] Test all operations with new GraphQL implementation
- [ ] Update documentation and team training
- [ ] Monitor performance and adjust as needed

---

**Migration Support**: Contact the development team for assistance  
**Last Updated**: June 29, 2024  
**GraphQL Endpoint**: `http://localhost:8080/graphql`
