@echo off
REM ============================================================================
REM DMS Dependency Management Script
REM ============================================================================
REM This script provides comprehensive dependency management capabilities including
REM vulnerability scanning, updates, compatibility testing, and reporting.
REM
REM Features:
REM - Dependency vulnerability scanning
REM - Automated dependency updates
REM - Compatibility testing
REM - License compliance checking
REM - Security advisory monitoring
REM - Dependency tree analysis
REM
REM Prerequisites:
REM - Java 21+ installed and in PATH
REM - Maven 3.8+ installed and in PATH
REM - Internet connection for vulnerability databases
REM ============================================================================

setlocal enabledelayedexpansion

REM Set script variables
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set REPORTS_DIR=%PROJECT_ROOT%\target\dependency-reports
set LOG_FILE=%PROJECT_ROOT%\target\dependency-management.log
set TIMESTAMP=%DATE:~-4,4%%DATE:~-10,2%%DATE:~-7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

echo ============================================================================
echo DMS Dependency Management Script
echo ============================================================================
echo Project Root: %PROJECT_ROOT%
echo Reports Directory: %REPORTS_DIR%
echo Log File: %LOG_FILE%
echo Timestamp: %TIMESTAMP%
echo ============================================================================

REM Parse command line arguments
set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=help

REM Create necessary directories
if not exist "%PROJECT_ROOT%\target" mkdir "%PROJECT_ROOT%\target"
if not exist "%REPORTS_DIR%" mkdir "%REPORTS_DIR%"

REM Change to project directory
cd /d "%PROJECT_ROOT%"

REM Route to appropriate function
if "%COMMAND%"=="scan" goto :scan_vulnerabilities
if "%COMMAND%"=="update" goto :update_dependencies
if "%COMMAND%"=="test" goto :test_compatibility
if "%COMMAND%"=="licenses" goto :check_licenses
if "%COMMAND%"=="tree" goto :analyze_tree
if "%COMMAND%"=="report" goto :generate_report
if "%COMMAND%"=="all" goto :run_all
if "%COMMAND%"=="help" goto :show_help
goto :show_help

:scan_vulnerabilities
echo [%TIME%] Starting vulnerability scan...
echo [%TIME%] Starting vulnerability scan... >> "%LOG_FILE%"

REM OWASP Dependency Check
echo [%TIME%] Running OWASP Dependency Check...
call mvn org.owasp:dependency-check-maven:check ^
    -DfailBuildOnCVSS=7 ^
    -DsuppressionsLocation=.github/security/dependency-check-suppressions.xml ^
    -DretireJsAnalyzerEnabled=false ^
    -DnodeAnalyzerEnabled=false ^
    -DassemblyAnalyzerEnabled=false ^
    -DoutputDirectory="%REPORTS_DIR%" >> "%LOG_FILE%" 2>&1

if %ERRORLEVEL% neq 0 (
    echo [ERROR] OWASP Dependency Check failed. Check log file: %LOG_FILE%
    goto :error_exit
)

REM Maven dependency analysis
echo [%TIME%] Running Maven dependency analysis...
call mvn dependency:analyze -DfailOnWarning=false > "%REPORTS_DIR%\dependency-analysis.txt" 2>&1

REM Check for unused dependencies
echo [%TIME%] Checking for unused dependencies...
call mvn dependency:analyze -DignoreNonCompile=true > "%REPORTS_DIR%\unused-dependencies.txt" 2>&1

echo [%TIME%] Vulnerability scan completed successfully.
echo [%TIME%] Reports available in: %REPORTS_DIR%
goto :end

:update_dependencies
echo [%TIME%] Starting dependency updates...
echo [%TIME%] Starting dependency updates... >> "%LOG_FILE%"

REM Check for dependency updates
echo [%TIME%] Checking for available updates...
call mvn versions:display-dependency-updates > "%REPORTS_DIR%\available-updates.txt" 2>&1
call mvn versions:display-plugin-updates > "%REPORTS_DIR%\plugin-updates.txt" 2>&1

REM Show current versions
echo [%TIME%] Current dependency versions:
call mvn dependency:tree -Dverbose=false -DoutputFile="%REPORTS_DIR%\current-dependencies.txt"

REM Interactive update process
echo.
echo Available dependency updates:
type "%REPORTS_DIR%\available-updates.txt" | findstr "The following dependencies"
echo.
set /p UPDATE_CONFIRM="Do you want to proceed with automatic updates? (y/N): "

if /i "%UPDATE_CONFIRM%"=="y" (
    echo [%TIME%] Applying automatic updates...
    
    REM Update patch versions only (safer)
    call mvn versions:use-latest-releases -DallowSnapshots=false -DgenerateBackupPoms=true >> "%LOG_FILE%" 2>&1
    
    if %ERRORLEVEL% neq 0 (
        echo [ERROR] Dependency update failed. Check log file: %LOG_FILE%
        goto :error_exit
    )
    
    echo [%TIME%] Dependencies updated successfully.
    echo [%TIME%] Running compatibility tests...
    goto :test_compatibility
) else (
    echo [%TIME%] Dependency updates skipped by user.
)
goto :end

:test_compatibility
echo [%TIME%] Starting compatibility testing...
echo [%TIME%] Starting compatibility testing... >> "%LOG_FILE%"

REM Run unit tests
echo [%TIME%] Running unit tests...
call mvn clean test -Dspring.profiles.active=test >> "%LOG_FILE%" 2>&1

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Unit tests failed. Dependency updates may have compatibility issues.
    echo [ERROR] Check log file: %LOG_FILE%
    goto :error_exit
)

REM Run integration tests
echo [%TIME%] Running integration tests...
call mvn failsafe:integration-test failsafe:verify -Dspring.profiles.active=integration-test >> "%LOG_FILE%" 2>&1

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Integration tests failed. Dependency updates may have compatibility issues.
    echo [ERROR] Check log file: %LOG_FILE%
    goto :error_exit
)

echo [%TIME%] Compatibility testing completed successfully.
echo [%TIME%] All tests passed - dependency updates are compatible.
goto :end

:check_licenses
echo [%TIME%] Starting license compliance check...
echo [%TIME%] Starting license compliance check... >> "%LOG_FILE%"

REM Generate license report
echo [%TIME%] Generating license report...
call mvn org.codehaus.mojo:license-maven-plugin:aggregate-third-party-report >> "%LOG_FILE%" 2>&1

if %ERRORLEVEL% neq 0 (
    echo [ERROR] License report generation failed. Check log file: %LOG_FILE%
    goto :error_exit
)

REM Copy license report to reports directory
if exist "target\site\aggregate-third-party-report.html" (
    copy "target\site\aggregate-third-party-report.html" "%REPORTS_DIR%\license-report.html" > nul
)

REM Check for prohibited licenses
echo [%TIME%] Checking for prohibited licenses...
set PROHIBITED_FOUND=false

REM Define prohibited licenses
set PROHIBITED_LICENSES=GPL-2.0 GPL-3.0 AGPL-3.0 LGPL-2.1 LGPL-3.0

for %%L in (%PROHIBITED_LICENSES%) do (
    findstr /C:"%%L" "%REPORTS_DIR%\license-report.html" > nul 2>&1
    if !ERRORLEVEL! equ 0 (
        echo [WARNING] Prohibited license found: %%L
        set PROHIBITED_FOUND=true
    )
)

if "%PROHIBITED_FOUND%"=="true" (
    echo [ERROR] Prohibited licenses detected. Review license report.
    goto :error_exit
) else (
    echo [%TIME%] License compliance check passed.
)
goto :end

:analyze_tree
echo [%TIME%] Starting dependency tree analysis...
echo [%TIME%] Starting dependency tree analysis... >> "%LOG_FILE%"

REM Generate dependency tree
echo [%TIME%] Generating dependency tree...
call mvn dependency:tree -DoutputFile="%REPORTS_DIR%\dependency-tree.txt" -DoutputType=text >> "%LOG_FILE%" 2>&1

REM Generate dependency tree with conflicts
echo [%TIME%] Analyzing dependency conflicts...
call mvn dependency:tree -Dverbose=true -DoutputFile="%REPORTS_DIR%\dependency-conflicts.txt" >> "%LOG_FILE%" 2>&1

REM Check for duplicate dependencies
echo [%TIME%] Checking for duplicate dependencies...
call mvn dependency:analyze-duplicate > "%REPORTS_DIR%\duplicate-dependencies.txt" 2>&1

REM Generate effective POM
echo [%TIME%] Generating effective POM...
call mvn help:effective-pom -Doutput="%REPORTS_DIR%\effective-pom.xml" >> "%LOG_FILE%" 2>&1

echo [%TIME%] Dependency tree analysis completed.
echo [%TIME%] Reports available in: %REPORTS_DIR%
goto :end

:generate_report
echo [%TIME%] Generating comprehensive dependency report...
echo [%TIME%] Generating comprehensive dependency report... >> "%LOG_FILE%"

REM Create HTML report
(
echo ^<!DOCTYPE html^>
echo ^<html lang="en"^>
echo ^<head^>
echo     ^<meta charset="UTF-8"^>
echo     ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^>
echo     ^<title^>DMS Dependency Management Report^</title^>
echo     ^<style^>
echo         body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
echo         .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1^); }
echo         .header { text-align: center; margin-bottom: 30px; }
echo         .header h1 { color: #2c3e50; margin-bottom: 10px; }
echo         .section { margin-bottom: 30px; }
echo         .section h2 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
echo         .status-good { color: #27ae60; font-weight: bold; }
echo         .status-warning { color: #f39c12; font-weight: bold; }
echo         .status-error { color: #e74c3c; font-weight: bold; }
echo         .report-link { display: inline-block; margin: 5px; padding: 8px 15px; background: #3498db; color: white; text-decoration: none; border-radius: 4px; }
echo         .report-link:hover { background: #2980b9; }
echo     ^</style^>
echo ^</head^>
echo ^<body^>
echo     ^<div class="container"^>
echo         ^<div class="header"^>
echo             ^<h1^>DMS Dependency Management Report^</h1^>
echo             ^<p^>Generated on %DATE% at %TIME%^</p^>
echo         ^</div^>
) > "%REPORTS_DIR%\dependency-report.html"

REM Add sections based on available reports
echo         ^<div class="section"^> >> "%REPORTS_DIR%\dependency-report.html"
echo             ^<h2^>Available Reports^</h2^> >> "%REPORTS_DIR%\dependency-report.html"

if exist "%REPORTS_DIR%\dependency-check-report.html" (
    echo             ^<a href="dependency-check-report.html" class="report-link"^>Vulnerability Scan Report^</a^> >> "%REPORTS_DIR%\dependency-report.html"
)

if exist "%REPORTS_DIR%\license-report.html" (
    echo             ^<a href="license-report.html" class="report-link"^>License Compliance Report^</a^> >> "%REPORTS_DIR%\dependency-report.html"
)

if exist "%REPORTS_DIR%\dependency-tree.txt" (
    echo             ^<a href="dependency-tree.txt" class="report-link"^>Dependency Tree^</a^> >> "%REPORTS_DIR%\dependency-report.html"
)

echo         ^</div^> >> "%REPORTS_DIR%\dependency-report.html"
echo     ^</div^> >> "%REPORTS_DIR%\dependency-report.html"
echo ^</body^> >> "%REPORTS_DIR%\dependency-report.html"
echo ^</html^> >> "%REPORTS_DIR%\dependency-report.html"

echo [%TIME%] Comprehensive report generated: %REPORTS_DIR%\dependency-report.html
goto :end

:run_all
echo [%TIME%] Running complete dependency management workflow...
call :scan_vulnerabilities
call :check_licenses
call :analyze_tree
call :generate_report
echo [%TIME%] Complete workflow finished successfully.
goto :end

:show_help
echo.
echo DMS Dependency Management Script
echo.
echo Usage: %~nx0 [COMMAND]
echo.
echo Commands:
echo   scan      - Run vulnerability scanning
echo   update    - Check and apply dependency updates
echo   test      - Run compatibility testing
echo   licenses  - Check license compliance
echo   tree      - Analyze dependency tree
echo   report    - Generate comprehensive report
echo   all       - Run complete workflow
echo   help      - Show this help message
echo.
echo Examples:
echo   %~nx0 scan          # Run vulnerability scan
echo   %~nx0 update        # Update dependencies
echo   %~nx0 all           # Run complete workflow
echo.
goto :end

:error_exit
echo [%TIME%] Script execution failed. Check log file: %LOG_FILE%
exit /b 1

:end
echo [%TIME%] Script execution completed.
endlocal
pause
