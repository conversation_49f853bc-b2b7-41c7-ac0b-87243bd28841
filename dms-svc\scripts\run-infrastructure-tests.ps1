# PowerShell script for running comprehensive infrastructure tests on Windows
# This script runs all infrastructure-related tests including unit tests, integration tests, and validation tests

param(
    [switch]$UnitOnly,
    [switch]$IntegrationOnly,
    [switch]$InfrastructureOnly,
    [switch]$DockerOnly,
    [switch]$KubernetesOnly,
    [switch]$CiCdOnly,
    [switch]$NoReports,
    [switch]$FailFast,
    [switch]$Help
)

# Script configuration
$ErrorActionPreference = "Continue"
$ProjectRoot = Split-Path -Parent $PSScriptRoot
$LogFile = "C:\temp\dms-infrastructure-tests-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"

# Test configuration
$RunUnitTests = $env:RUN_UNIT_TESTS -ne "false"
$RunIntegrationTests = $env:RUN_INTEGRATION_TESTS -ne "false"
$RunInfrastructureTests = $env:RUN_INFRASTRUCTURE_TESTS -ne "false"
$RunDockerTests = $env:RUN_DOCKER_TESTS -ne "false"
$RunKubernetesTests = $env:RUN_KUBERNETES_TESTS -eq "true"
$RunCiCdTests = $env:RUN_CICD_TESTS -ne "false"
$GenerateReports = $env:GENERATE_REPORTS -ne "false"
$FailFastMode = $env:FAIL_FAST -eq "true"

# Test result tracking
$TotalTestSuites = 0
$PassedTestSuites = 0
$FailedTestSuites = 0
$SkippedTestSuites = 0

# Logging functions
function Write-LogInfo {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [INFO] $Message"
    Write-Host $logMessage -ForegroundColor Blue
    Add-Content -Path $LogFile -Value $logMessage
}

function Write-LogSuccess {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [SUCCESS] $Message"
    Write-Host $logMessage -ForegroundColor Green
    Add-Content -Path $LogFile -Value $logMessage
}

function Write-LogWarning {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [WARNING] $Message"
    Write-Host $logMessage -ForegroundColor Yellow
    Add-Content -Path $LogFile -Value $logMessage
}

function Write-LogError {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [ERROR] $Message"
    Write-Host $logMessage -ForegroundColor Red
    Add-Content -Path $LogFile -Value $logMessage
}

# Function to run a test suite and track results
function Invoke-TestSuite {
    param(
        [string]$SuiteName,
        [scriptblock]$TestCommand,
        [bool]$Required = $true
    )
    
    $script:TotalTestSuites++
    
    Write-LogInfo "Running test suite: $SuiteName"
    
    try {
        $result = & $TestCommand
        if ($LASTEXITCODE -ne 0 -and $LASTEXITCODE -ne $null) {
            throw "Test command exited with code $LASTEXITCODE"
        }
        Write-LogSuccess "✓ $SuiteName completed successfully"
        $script:PassedTestSuites++
        return $true
    }
    catch {
        if ($Required) {
            Write-LogError "✗ $SuiteName failed: $($_.Exception.Message)"
            $script:FailedTestSuites++
            if ($FailFastMode) {
                Write-LogError "Failing fast due to test failure"
                exit 1
            }
            return $false
        }
        else {
            Write-LogWarning "⚠ $SuiteName skipped (optional): $($_.Exception.Message)"
            $script:SkippedTestSuites++
            return $true
        }
    }
}

# Show help
function Show-Help {
    Write-Host @"
Usage: .\run-infrastructure-tests.ps1 [OPTIONS]

Run comprehensive infrastructure tests for DMS Service

Options:
    -UnitOnly           Run only unit tests
    -IntegrationOnly    Run only integration tests
    -InfrastructureOnly Run only infrastructure validation tests
    -DockerOnly         Run only Docker-related tests
    -KubernetesOnly     Run only Kubernetes-related tests
    -CiCdOnly          Run only CI/CD validation tests
    -NoReports         Skip test report generation
    -FailFast          Stop on first test failure
    -Help              Show this help message

Environment Variables:
    RUN_UNIT_TESTS=true|false           Enable/disable unit tests
    RUN_INTEGRATION_TESTS=true|false    Enable/disable integration tests
    RUN_INFRASTRUCTURE_TESTS=true|false Enable/disable infrastructure tests
    RUN_DOCKER_TESTS=true|false         Enable/disable Docker tests
    RUN_KUBERNETES_TESTS=true|false     Enable/disable Kubernetes tests
    RUN_CICD_TESTS=true|false          Enable/disable CI/CD tests
    GENERATE_REPORTS=true|false         Enable/disable report generation
    FAIL_FAST=true|false               Stop on first failure

Examples:
    .\run-infrastructure-tests.ps1                    # Run all tests
    .\run-infrastructure-tests.ps1 -UnitOnly          # Run only unit tests
    .\run-infrastructure-tests.ps1 -InfrastructureOnly # Run only infrastructure tests
    `$env:RUN_KUBERNETES_TESTS="true"; .\run-infrastructure-tests.ps1  # Enable Kubernetes tests
    .\run-infrastructure-tests.ps1 -FailFast          # Stop on first failure
"@
}

# Parse command line arguments
function Set-TestConfiguration {
    if ($UnitOnly) {
        $script:RunUnitTests = $true
        $script:RunIntegrationTests = $false
        $script:RunInfrastructureTests = $false
        $script:RunDockerTests = $false
        $script:RunKubernetesTests = $false
        $script:RunCiCdTests = $false
    }
    elseif ($IntegrationOnly) {
        $script:RunUnitTests = $false
        $script:RunIntegrationTests = $true
        $script:RunInfrastructureTests = $false
        $script:RunDockerTests = $false
        $script:RunKubernetesTests = $false
        $script:RunCiCdTests = $false
    }
    elseif ($InfrastructureOnly) {
        $script:RunUnitTests = $false
        $script:RunIntegrationTests = $false
        $script:RunInfrastructureTests = $true
        $script:RunDockerTests = $false
        $script:RunKubernetesTests = $false
        $script:RunCiCdTests = $false
    }
    elseif ($DockerOnly) {
        $script:RunUnitTests = $false
        $script:RunIntegrationTests = $false
        $script:RunInfrastructureTests = $false
        $script:RunDockerTests = $true
        $script:RunKubernetesTests = $false
        $script:RunCiCdTests = $false
    }
    elseif ($KubernetesOnly) {
        $script:RunUnitTests = $false
        $script:RunIntegrationTests = $false
        $script:RunInfrastructureTests = $false
        $script:RunDockerTests = $false
        $script:RunKubernetesTests = $true
        $script:RunCiCdTests = $false
    }
    elseif ($CiCdOnly) {
        $script:RunUnitTests = $false
        $script:RunIntegrationTests = $false
        $script:RunInfrastructureTests = $false
        $script:RunDockerTests = $false
        $script:RunKubernetesTests = $false
        $script:RunCiCdTests = $true
    }
    
    if ($NoReports) {
        $script:GenerateReports = $false
    }
    
    if ($FailFast) {
        $script:FailFastMode = $true
    }
}

# Check prerequisites
function Test-Prerequisites {
    Write-LogInfo "Checking prerequisites..."
    
    # Check if Maven is installed
    try {
        mvn --version | Out-Null
    }
    catch {
        Write-LogError "Maven is not installed or not in PATH"
        exit 1
    }
    
    # Check if Java is installed
    try {
        java -version 2>&1 | Out-Null
    }
    catch {
        Write-LogError "Java is not installed or not in PATH"
        exit 1
    }
    
    # Check if we're in the correct directory
    if (-not (Test-Path (Join-Path $ProjectRoot "pom.xml"))) {
        Write-LogError "Not in a Maven project directory"
        exit 1
    }
    
    # Check Docker if Docker tests are enabled
    if ($RunDockerTests) {
        try {
            docker --version | Out-Null
            docker info | Out-Null
        }
        catch {
            Write-LogWarning "Docker not available, disabling Docker tests"
            $script:RunDockerTests = $false
        }
    }
    
    # Check kubectl if Kubernetes tests are enabled
    if ($RunKubernetesTests) {
        try {
            kubectl version --client | Out-Null
        }
        catch {
            Write-LogWarning "kubectl not found, disabling Kubernetes tests"
            $script:RunKubernetesTests = $false
        }
    }
    
    Write-LogSuccess "Prerequisites check completed"
}

# Run unit tests
function Invoke-UnitTests {
    if (-not $RunUnitTests) {
        Write-LogInfo "Skipping unit tests"
        return
    }
    
    Write-LogInfo "=== Running Unit Tests ==="
    
    Invoke-TestSuite "Unit Tests" {
        Push-Location $ProjectRoot
        try {
            mvn test -Dgroups='!integration,!infrastructure'
        }
        finally {
            Pop-Location
        }
    }
}

# Run integration tests
function Invoke-IntegrationTests {
    if (-not $RunIntegrationTests) {
        Write-LogInfo "Skipping integration tests"
        return
    }
    
    Write-LogInfo "=== Running Integration Tests ==="
    
    Invoke-TestSuite "Integration Tests" {
        Push-Location $ProjectRoot
        try {
            mvn test -Dgroups=integration
        }
        finally {
            Pop-Location
        }
    }
}

# Run infrastructure tests
function Invoke-InfrastructureTests {
    if (-not $RunInfrastructureTests) {
        Write-LogInfo "Skipping infrastructure tests"
        return
    }
    
    Write-LogInfo "=== Running Infrastructure Tests ==="
    
    # Set environment variables for infrastructure tests
    $env:DOCKER_TEST_ENABLED = $RunDockerTests.ToString().ToLower()
    $env:KUBERNETES_TEST_ENABLED = $RunKubernetesTests.ToString().ToLower()
    $env:CICD_TEST_ENABLED = $RunCiCdTests.ToString().ToLower()
    
    Invoke-TestSuite "Infrastructure Configuration Tests" {
        Push-Location $ProjectRoot
        try {
            mvn test -Dtest=ConfigurationValidationTest
        }
        finally {
            Pop-Location
        }
    }
    
    Invoke-TestSuite "Infrastructure Test Suite" {
        Push-Location $ProjectRoot
        try {
            mvn test -Dtest=InfrastructureTestSuite
        }
        finally {
            Pop-Location
        }
    }
}

# Run Docker tests
function Invoke-DockerTests {
    if (-not $RunDockerTests) {
        Write-LogInfo "Skipping Docker tests"
        return
    }
    
    Write-LogInfo "=== Running Docker Tests ==="
    
    $env:DOCKER_TEST_ENABLED = "true"
    
    Invoke-TestSuite "Docker Container Tests" {
        Push-Location $ProjectRoot
        try {
            mvn test -Dtest=DockerContainerTest
        }
        finally {
            Pop-Location
        }
    }
    
    # Run PowerShell-based Docker tests
    Invoke-TestSuite "Docker Infrastructure Validation" {
        & "$PSScriptRoot\test-deployment.ps1" -DockerOnly
    } -Required $false
}

# Run Kubernetes tests
function Invoke-KubernetesTests {
    if (-not $RunKubernetesTests) {
        Write-LogInfo "Skipping Kubernetes tests"
        return
    }
    
    Write-LogInfo "=== Running Kubernetes Tests ==="
    
    $env:KUBERNETES_TEST_ENABLED = "true"
    
    Invoke-TestSuite "Kubernetes Deployment Tests" {
        Push-Location $ProjectRoot
        try {
            mvn test -Dtest=KubernetesDeploymentTest
        }
        finally {
            Pop-Location
        }
    } -Required $false
    
    # Run PowerShell-based Kubernetes tests
    Invoke-TestSuite "Kubernetes Infrastructure Validation" {
        & "$PSScriptRoot\test-deployment.ps1" -KubernetesOnly
    } -Required $false
}

# Run CI/CD tests
function Invoke-CiCdTests {
    if (-not $RunCiCdTests) {
        Write-LogInfo "Skipping CI/CD tests"
        return
    }
    
    Write-LogInfo "=== Running CI/CD Tests ==="
    
    $env:CICD_TEST_ENABLED = "true"
    
    Invoke-TestSuite "CI/CD Pipeline Tests" {
        Push-Location $ProjectRoot
        try {
            mvn test -Dtest=CiCdPipelineTest
        }
        finally {
            Pop-Location
        }
    }
    
    # Run PowerShell-based CI/CD tests
    Invoke-TestSuite "CI/CD Infrastructure Validation" {
        & "$PSScriptRoot\test-deployment.ps1" -CiCdOnly
    }
}

# Generate test reports
function New-TestReports {
    if (-not $GenerateReports) {
        Write-LogInfo "Skipping report generation"
        return
    }
    
    Write-LogInfo "=== Generating Test Reports ==="
    
    Invoke-TestSuite "Surefire Reports" {
        Push-Location $ProjectRoot
        try {
            mvn surefire-report:report
        }
        finally {
            Pop-Location
        }
    } -Required $false
    
    Invoke-TestSuite "JaCoCo Coverage Report" {
        Push-Location $ProjectRoot
        try {
            mvn jacoco:report
        }
        finally {
            Pop-Location
        }
    } -Required $false
    
    # Copy reports to a consolidated location
    $reportsDir = Join-Path $ProjectRoot "target\infrastructure-test-reports"
    if (-not (Test-Path $reportsDir)) {
        New-Item -ItemType Directory -Path $reportsDir -Force | Out-Null
    }
    
    $siteDir = Join-Path $ProjectRoot "target\site"
    if (Test-Path $siteDir) {
        Copy-Item -Path "$siteDir\*" -Destination $reportsDir -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    $surefireDir = Join-Path $ProjectRoot "target\surefire-reports"
    if (Test-Path $surefireDir) {
        Copy-Item -Path $surefireDir -Destination $reportsDir -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    Write-LogInfo "Test reports available in: $reportsDir"
}

# Main execution function
function Main {
    # Create log directory if it doesn't exist
    $logDir = Split-Path $LogFile -Parent
    if (-not (Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }
    
    Write-LogInfo "Starting DMS Infrastructure Test Runner..."
    Write-LogInfo "Log file: $LogFile"
    Write-LogInfo "Project root: $ProjectRoot"
    
    Set-TestConfiguration
    
    Write-LogInfo "Test Configuration:"
    Write-LogInfo "  Unit Tests: $RunUnitTests"
    Write-LogInfo "  Integration Tests: $RunIntegrationTests"
    Write-LogInfo "  Infrastructure Tests: $RunInfrastructureTests"
    Write-LogInfo "  Docker Tests: $RunDockerTests"
    Write-LogInfo "  Kubernetes Tests: $RunKubernetesTests"
    Write-LogInfo "  CI/CD Tests: $RunCiCdTests"
    Write-LogInfo "  Generate Reports: $GenerateReports"
    Write-LogInfo "  Fail Fast: $FailFastMode"
    
    Test-Prerequisites
    
    # Run test suites
    Invoke-UnitTests
    Invoke-IntegrationTests
    Invoke-InfrastructureTests
    Invoke-DockerTests
    Invoke-KubernetesTests
    Invoke-CiCdTests
    
    # Generate reports
    New-TestReports
    
    # Print summary
    Write-LogInfo "=== Test Execution Summary ==="
    Write-LogInfo "Total Test Suites: $TotalTestSuites"
    Write-LogSuccess "Passed: $PassedTestSuites"
    Write-LogError "Failed: $FailedTestSuites"
    Write-LogWarning "Skipped: $SkippedTestSuites"
    
    # Calculate success rate
    if ($TotalTestSuites -gt 0) {
        $SuccessRate = [math]::Round(($PassedTestSuites * 100) / $TotalTestSuites, 2)
        Write-LogInfo "Success Rate: $SuccessRate%"
    }
    
    # Exit with appropriate code
    if ($FailedTestSuites -eq 0) {
        Write-LogSuccess "All infrastructure tests completed successfully!"
        exit 0
    }
    else {
        Write-LogError "Some infrastructure tests failed. Check the log file for details: $LogFile"
        exit 1
    }
}

# Handle script parameters
if ($Help) {
    Show-Help
    exit 0
}

# Run main function
Main
