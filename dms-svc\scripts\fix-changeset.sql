-- Fix the Liquibase changeset issue by marking it as executed
-- This is needed because the table was partially created in a previous failed attempt

-- First, check if the table exists
SELECT COUNT(*) as table_exists 
FROM information_schema.tables 
WHERE table_schema = 'dms_db' 
AND table_name = 'storage_configurations';

-- If table exists, mark the changeset as executed
INSERT INTO dms_db.databasechangelog (
    ID, 
    AUTHOR, 
    FIL<PERSON>AM<PERSON>, 
    DATEEXECUTED, 
    ORDEREXECUTED, 
    EXECTYPE, 
    MD5SUM, 
    DESCRIPTION, 
    COMMENTS, 
    TAG, 
    LIQUIBASE, 
    CONTEXTS, 
    LABELS, 
    DEPLOYMENT_ID
) VALUES (
    '014-create-storage-configurations-table',
    'dms',
    'db/changelog/014-create-storage-configurations-table.sql',
    NOW(),
    (SELECT COALESCE(MAX(ORDEREXECUTED), 0) + 1 FROM dms_db.databasechangelog d),
    'EXECUTED',
    '8:manual-fix',
    'Manual fix for storage configurations table',
    'Manually marked as executed due to table existence',
    NULL,
    '4.20.0',
    'default',
    NULL,
    '970717518'
) ON DUPLICATE KEY UPDATE 
    DATEEXECUTED = NOW(),
    EXECTYPE = 'EXECUTED';

-- Insert sample data for testing dynamic configuration
INSERT INTO dms_db.storage_configurations (
    provider_type,
    is_active,
    is_default,
    configuration_json,
    description,
    priority,
    health_check_enabled,
    created_by
) VALUES 
(
    'LOCAL',
    TRUE,
    TRUE,
    '{"basePath": "./storage/documents", "createDirectories": true, "filePermissions": "644", "maxFileSize": 104857600}',
    'Local file system storage for development and testing',
    100,
    TRUE,
    'system'
),
(
    'S3',
    FALSE,
    FALSE,
    '{"bucketName": "grc-dms-bucket", "region": "ap-south-1", "accessKey": "********************", "secretKey": "kTB4e4KR5dX00Ramk52ySZM0NOzlwNQ728wAQgYE", "endpoint": "", "pathStyleAccess": false, "storageClass": "STANDARD", "serverSideEncryption": true}',
    'AWS S3 storage for production use',
    200,
    TRUE,
    'system'
),
(
    'SHAREPOINT',
    FALSE,
    FALSE,
    '{"tenantId": "8c28f5eb-e6dc-46a5-8e12-da46dcea72e2", "clientId": "14c8a4cf-d35e-4938-b140-a1422c99f45a", "clientSecret": "****************************************", "siteUrl": "https://ascenttechnologyconsulting.sharepoint.com/sites/Ascent-Test", "documentLibrary": "Documents", "driveId": "", "useApplicationPermissions": true}',
    'Microsoft SharePoint storage for enterprise collaboration',
    300,
    TRUE,
    'system'
) ON DUPLICATE KEY UPDATE
    configuration_json = VALUES(configuration_json),
    description = VALUES(description),
    last_modified_date = CURRENT_TIMESTAMP,
    last_modified_by = 'system';

-- Verify the data
SELECT 
    id,
    provider_type,
    is_active,
    is_default,
    description,
    priority,
    health_check_enabled,
    created_date
FROM dms_db.storage_configurations
ORDER BY priority;
