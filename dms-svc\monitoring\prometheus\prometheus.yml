# Prometheus configuration for DMS Service
global:
  scrape_interval: 15s
  evaluation_interval: 15s

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "alerts.yml"

# A scrape configuration containing exactly one endpoint to scrape:
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # DMS Application metrics
  - job_name: 'dms-app'
    static_configs:
      - targets: ['dms-app:9464']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s

  # MySQL metrics (if mysql_exporter is available)
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql:3306']
    scrape_interval: 30s

  # Redis metrics (if redis_exporter is available)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # Elasticsearch metrics
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']
    metrics_path: '/_prometheus/metrics'
    scrape_interval: 30s