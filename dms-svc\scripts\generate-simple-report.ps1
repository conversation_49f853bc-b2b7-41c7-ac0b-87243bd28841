# Simple HTML Test Report Generator
$xmlDir = "..\target\surefire-reports"
$outputDir = "..\target\site"
$outputFile = "surefire-report.html"

$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$xmlPath = Join-Path $scriptDir $xmlDir
$outputPath = Join-Path $scriptDir $outputDir
$htmlFile = Join-Path $outputPath $outputFile

if (!(Test-Path $outputPath)) {
    New-Item -ItemType Directory -Path $outputPath -Force | Out-Null
}

Write-Host "Generating HTML test report..."
Write-Host "XML Directory: $xmlPath"
Write-Host "Output File: $htmlFile"

$totalTests = 0
$totalFailures = 0
$totalErrors = 0
$totalSkipped = 0
$testResults = @()

$xmlFiles = Get-ChildItem -Path $xmlPath -Filter "TEST-*.xml" -ErrorAction SilentlyContinue

if ($xmlFiles.Count -eq 0) {
    Write-Warning "No test XML files found"
    exit 1
}

foreach ($xmlFile in $xmlFiles) {
    try {
        [xml]$xml = Get-Content $xmlFile.FullName
        $testsuite = $xml.testsuite
        
        $tests = [int]$testsuite.tests
        $failures = [int]$testsuite.failures
        $errors = [int]$testsuite.errors
        $skipped = [int]$testsuite.skipped
        $time = $testsuite.time
        $name = $testsuite.name
        
        $totalTests += $tests
        $totalFailures += $failures
        $totalErrors += $errors
        $totalSkipped += $skipped
        
        $status = if ($failures -eq 0 -and $errors -eq 0) { "SUCCESS" } else { "FAILURE" }
        $cssClass = if ($failures -eq 0 -and $errors -eq 0) { "success" } else { "failure" }
        
        $testResults += [PSCustomObject]@{
            Name = $name
            Tests = $tests
            Failures = $failures
            Errors = $errors
            Skipped = $skipped
            Time = $time
            Status = $status
            CssClass = $cssClass
        }
    }
    catch {
        Write-Warning "Error parsing file: $($xmlFile.Name)"
    }
}

$totalSuccess = $totalTests - $totalFailures - $totalErrors - $totalSkipped

# Create HTML content
$html = @"
<!DOCTYPE html>
<html>
<head>
    <title>Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .success { color: green; }
        .failure { color: red; }
        .summary { background-color: #f9f9f9; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Test Report</h1>
    <div class="summary">
        <h2>Summary</h2>
        <p>Total Tests: $totalTests</p>
        <p>Success: $totalSuccess</p>
        <p>Failures: $totalFailures</p>
        <p>Errors: $totalErrors</p>
        <p>Skipped: $totalSkipped</p>
    </div>
    <h2>Test Results</h2>
    <table>
        <tr><th>Test Class</th><th>Tests</th><th>Failures</th><th>Errors</th><th>Skipped</th><th>Time</th><th>Status</th></tr>
"@

foreach ($result in $testResults) {
    $html += "<tr><td>$($result.Name)</td><td>$($result.Tests)</td><td>$($result.Failures)</td><td>$($result.Errors)</td><td>$($result.Skipped)</td><td>$($result.Time)</td><td class=`"$($result.CssClass)`">$($result.Status)</td></tr>"
}

$html += @"
    </table>
    <hr>
    <p><small>Generated on $(Get-Date)</small></p>
</body>
</html>
"@

$html | Out-File -FilePath $htmlFile -Encoding UTF8

Write-Host "Report generated: $htmlFile"
