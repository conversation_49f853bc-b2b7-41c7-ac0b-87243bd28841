# MOVED: JavaDoc Style Guide

⚠️ **This document has been moved to the root documentation folder.**

Please refer to: [JavaDoc Style Guide](../JAVADOC_STYLE_GUIDE.md)

---

## Relocation Notice

The JavaDoc Style Guide has been moved to the main documentation directory for better organization and accessibility. The consolidated version provides:

- Complete JavaDoc standards and guidelines
- Class and method documentation requirements
- Field documentation best practices
- Tags and annotations reference
- Examples and templates
- Tools and automation guidance

All content from this document is available in the main JavaDoc style guide.

---

**Moved**: January 2025  
**Status**: Relocated - Use main style guide  
**New Location**: [../JAVADOC_STYLE_GUIDE.md](../JAVADOC_STYLE_GUIDE.md)
