# DMS Troubleshooting Guide

This directory contains comprehensive troubleshooting guides for the Document Management Service (DMS). These guides provide step-by-step solutions for common issues, error resolution, performance problems, and maintenance procedures.

## 📚 Troubleshooting Guides

### Core System Issues
- **[Common Issues Guide](COMMON_ISSUES.md)** - Most frequently encountered problems and solutions
- **[Error Resolution Guide](ERROR_RESOLUTION.md)** - Detailed error code explanations and fixes
- **[Startup Issues Guide](STARTUP_ISSUES.md)** - Application startup and initialization problems
- **[Database Issues Guide](DATABASE_ISSUES.md)** - Database connectivity and query problems

### Performance & Monitoring
- **[Performance Issues Guide](PERFORMANCE_ISSUES.md)** - Performance troubleshooting and optimization
- **[Memory Issues Guide](MEMORY_ISSUES.md)** - Memory leaks, OutOfMemory errors, and optimization
- **[Monitoring & Logging Guide](MONITORING_LOGGING.md)** - Log analysis and monitoring setup

### Configuration & Deployment
- **[Configuration Issues Guide](CONFIGURATION_ISSUES.md)** - Configuration problems and validation
- **[Deployment Issues Guide](DEPLOYMENT_ISSUES.md)** - Deployment failures and environment issues
- **[Storage Issues Guide](STORAGE_ISSUES.md)** - File storage and provider configuration problems

### Integration & External Services
- **[Authentication Issues Guide](AUTHENTICATION_ISSUES.md)** - JWT, security, and permission problems
- **[External Services Guide](EXTERNAL_SERVICES.md)** - Redis, Elasticsearch, and third-party integrations
- **[API Issues Guide](API_ISSUES.md)** - GraphQL and REST API troubleshooting

### Business Features
- **[Document Operations Guide](DOCUMENT_OPERATIONS.md)** - Document upload, download, and management issues
- **[Search Issues Guide](SEARCH_ISSUES.md)** - Elasticsearch and search functionality problems
- **[Workflow Issues Guide](WORKFLOW_ISSUES.md)** - Workflow and business process problems

## Emergency Procedures

### Critical Issues
1. **Service Down**: [Service Recovery Procedures](EMERGENCY_PROCEDURES.md#service-down)
2. **Data Corruption**: [Data Recovery Procedures](EMERGENCY_PROCEDURES.md#data-corruption)
3. **Security Breach**: [Security Incident Response](EMERGENCY_PROCEDURES.md#security-breach)
4. **Performance Degradation**: [Performance Recovery](EMERGENCY_PROCEDURES.md#performance-degradation)

### Quick Reference
- **Health Check**: `curl http://localhost:9092/actuator/health`
- **Log Location**: `logs/dms-service.log`
- **Configuration**: `src/main/resources/application.properties`
- **Database**: MySQL on port 3306
- **Redis**: Redis on port 6379
- **Elasticsearch**: Elasticsearch on port 9200

## 🔍 Diagnostic Tools

### Built-in Diagnostics
- **Health Endpoints**: `/actuator/health`, `/actuator/health/db`, `/actuator/health/redis`
- **Metrics**: `/actuator/prometheus`, `/actuator/metrics`
- **Info**: `/actuator/info`, `/actuator/env`
- **Trace**: Correlation ID tracking in logs

### External Tools
- **Database**: MySQL Workbench, phpMyAdmin
- **Cache**: Redis CLI, Redis Desktop Manager
- **Search**: Kibana, Elasticsearch Head
- **Monitoring**: Grafana, Prometheus

## 📋 Prerequisites

Before starting troubleshooting, ensure you have:

### System Access
- **Administrative Access**: Root or sudo access to the server
- **Database Access**: MySQL credentials with appropriate permissions
- **Application Logs**: Access to application log files and directories
- **Service Management**: Ability to start/stop/restart services

### Required Tools
- **Command Line Tools**: curl, grep, tail, ps, netstat
- **Database Tools**: MySQL client, phpMyAdmin, or MySQL Workbench
- **Monitoring Tools**: htop, iostat, netstat, ss
- **Network Tools**: ping, traceroute, nslookup, dig

### Environment Information
- **Application Version**: Current DMS service version
- **Java Version**: JDK/JRE version and configuration
- **Database Version**: MySQL version and configuration
- **Operating System**: OS version and system specifications
- **Network Configuration**: Firewall rules, port configurations

### Documentation Access
- **API Documentation**: Access to GraphQL and REST API docs
- **Configuration Files**: Application properties and environment configs
- **Deployment Information**: Deployment procedures and environment setup
- **Contact Information**: Development team and support contacts

## Troubleshooting Process

### Step 1: Identify the Problem
1. **Gather Information**
   - What is the exact error message?
   - When did the problem start?
   - What was the user trying to do?
   - Is it affecting all users or specific users?

2. **Check System Status**
   - Verify service health endpoints
   - Check system resources (CPU, memory, disk)
   - Review recent deployments or changes

3. **Collect Logs**
   - Application logs with correlation IDs
   - Database logs and slow query logs
   - System logs and error logs

### Step 2: Analyze the Issue
1. **Review Error Messages**
   - Look up error codes in the Error Resolution Guide
   - Check for patterns in error messages
   - Identify root cause vs. symptoms

2. **Check Dependencies**
   - Database connectivity and performance
   - Redis cache availability
   - Elasticsearch cluster health
   - External service availability

3. **Validate Configuration**
   - Application properties
   - Environment variables
   - Database connection settings
   - Storage provider configuration

### Step 3: Apply Solutions
1. **Follow Specific Guides**
   - Use the appropriate troubleshooting guide
   - Follow step-by-step procedures
   - Document any deviations or customizations

2. **Test Solutions**
   - Verify the fix resolves the issue
   - Test related functionality
   - Monitor for recurring problems

3. **Document Resolution**
   - Record the solution in incident logs
   - Update troubleshooting guides if needed
   - Share knowledge with the team

## Common Commands

### Service Management
```bash
# Check service status
curl http://localhost:9092/actuator/health

# Restart service (development)
mvn spring-boot:run

# View logs
tail -f logs/dms-service.log

# Check Java processes
jps -v
```

### Database Operations
```bash
# Connect to MySQL
mysql -u dms_user -p dms_db

# Check database status
SHOW PROCESSLIST;
SHOW ENGINE INNODB STATUS;

# Check table sizes
SELECT table_name, ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables WHERE table_schema = 'dms_db';
```

### Cache Operations
```bash
# Connect to Redis
redis-cli

# Check Redis status
INFO

# Clear cache
FLUSHDB

# Monitor Redis commands
MONITOR
```

### Search Operations
```bash
# Check Elasticsearch health
curl http://localhost:9200/_health

# List indices
curl http://localhost:9200/_cat/indices

# Check cluster status
curl http://localhost:9200/_cluster/health
```

## Getting Help

### Internal Resources
- **Development Team**: Contact for code-related issues
- **DevOps Team**: Contact for infrastructure and deployment issues
- **Database Team**: Contact for database performance and optimization

### External Resources
- **Spring Boot Documentation**: https://spring.io/projects/spring-boot
- **MySQL Documentation**: https://dev.mysql.com/doc/
- **Redis Documentation**: https://redis.io/documentation
- **Elasticsearch Documentation**: https://www.elastic.co/guide/

### Support Channels
- **Slack**: #dms-support channel
- **Email**: <EMAIL>
- **Ticket System**: Create tickets for complex issues
- **Emergency**: On-call rotation for critical issues

## 📈 Continuous Improvement

### Feedback Loop
- **Issue Tracking**: Track all issues and resolutions
- **Pattern Analysis**: Identify recurring problems
- **Guide Updates**: Keep troubleshooting guides current
- **Training**: Regular training on new issues and solutions

### Metrics and Monitoring
- **MTTR**: Mean Time To Resolution tracking
- **Issue Categories**: Categorize and trend issues
- **Prevention**: Proactive measures to prevent issues
- **Automation**: Automate common troubleshooting tasks

---

**Last Updated**: July 23, 2024
**Version**: 1.2.0
**Generated**: 2024-07-23T11:45:00+05:30
**Maintained By**: DMS Development Team

For immediate assistance with critical issues, contact the on-call engineer or escalate through the established incident response procedures.
