# OpenTelemetry Troubleshooting Guide

## Common Issues and Solutions

### 1. Missing Prometheus Exporter Dependency

**Error:**
```
otel.metrics.exporter set to "prometheus" but opentelemetry-exporter-prometheus not found on classpath
```

**Solution:**
The OpenTelemetry Prometheus exporter dependency is not available in the standard OpenTelemetry distribution. Use console exporter instead:

```yaml
# In docker-compose.yml
environment:
  - OTEL_METRICS_EXPORTER=console
```

**Alternative Solution:**
Use Spring Boot Actuator's built-in Prometheus endpoint:
```properties
# In application.properties
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.metrics.export.prometheus.enabled=true
```

### 2. Configuration Priority

OpenTelemetry configuration follows this priority order:
1. Environment variables (highest priority)
2. System properties
3. Configuration file properties (lowest priority)

**Example:**
- `application.properties`: `otel.metrics.exporter=console`
- Docker environment: `OTEL_METRICS_EXPORTER=prometheus`
- **Result:** Prometheus exporter will be used (environment variable overrides)

### 3. Supported Exporters

**Available Exporters:**
- `console` - Logs metrics to console (always available)
- `otlp` - OpenTelemetry Protocol (requires OTLP endpoint)
- `zipkin` - Zipkin tracing (requires Zipkin dependency)

**Not Available:**
- `prometheus` - Not included in standard OpenTelemetry Java distribution

### 4. Docker Configuration Best Practices

**Development Environment:**
```yaml
environment:
  - OTEL_TRACES_EXPORTER=zipkin
  - OTEL_EXPORTER_ZIPKIN_ENDPOINT=http://zipkin:9411/api/v2/spans
  - OTEL_METRICS_EXPORTER=console
  - OTEL_LOGS_EXPORTER=console
```

**Production Environment:**
```yaml
environment:
  - OTEL_TRACES_EXPORTER=${OTEL_TRACES_EXPORTER:-otlp}
  - OTEL_EXPORTER_OTLP_ENDPOINT=${OTEL_EXPORTER_OTLP_ENDPOINT}
  - OTEL_METRICS_EXPORTER=${OTEL_METRICS_EXPORTER:-console}
  - OTEL_LOGS_EXPORTER=${OTEL_LOGS_EXPORTER:-console}
```

### 5. Metrics Collection Alternatives

**Option 1: Spring Boot Actuator + Prometheus**
```properties
management.endpoints.web.exposure.include=prometheus
management.metrics.export.prometheus.enabled=true
```
Access metrics at: `http://localhost:8080/actuator/prometheus`

**Option 2: Micrometer Registry**
```xml
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-registry-prometheus</artifactId>
</dependency>
```

### 6. Debugging Steps

1. **Check Dependencies:**
   ```bash
   mvn dependency:tree | grep opentelemetry
   ```

2. **Verify Configuration:**
   ```bash
   docker-compose config
   ```

3. **Check Application Logs:**
   ```bash
   docker-compose logs dms-app | grep -i otel
   ```

4. **Test Endpoints:**
   ```bash
   curl http://localhost:9093/actuator/health
   curl http://localhost:9093/actuator/prometheus
   ```

### 7. Quick Fix for Deployment

If you encounter the Prometheus exporter error during deployment:

1. **Immediate Fix:**
   ```bash
   # Update environment variable
   export OTEL_METRICS_EXPORTER=console
   docker-compose up -d
   ```

2. **Permanent Fix:**
   Update `docker-compose.yml`:
   ```yaml
   environment:
     - OTEL_METRICS_EXPORTER=console
   ```

### 8. Monitoring Setup

**Prometheus Configuration:**
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'dms-service'
    static_configs:
      - targets: ['dms-app:9093']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s
```

**Grafana Dashboard:**
- Import dashboard ID: 4701 (JVM Micrometer)
- Import dashboard ID: 6756 (Spring Boot Statistics)

## Related Documentation

- [OpenTelemetry Java Documentation](https://opentelemetry.io/docs/instrumentation/java/)
- [Spring Boot Actuator Reference](https://docs.spring.io/spring-boot/docs/current/reference/html/actuator.html)
- [Micrometer Documentation](https://micrometer.io/docs)