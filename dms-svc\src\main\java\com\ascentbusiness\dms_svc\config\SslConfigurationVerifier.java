package com.ascentbusiness.dms_svc.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * SSL Configuration Verifier for UAT environment.
 * This component logs SSL configuration details to help debug SSL issues.
 */
@Component
@Profile("uat")
public class SslConfigurationVerifier {

    private static final Logger logger = LoggerFactory.getLogger(SslConfigurationVerifier.class);

    @Value("${server.ssl.enabled:false}")
    private boolean sslEnabled;

    @Value("${server.ssl.key-store:}")
    private String keyStore;

    @Value("${server.ssl.key-store-type:}")
    private String keyStoreType;

    @Value("${server.ssl.key-alias:}")
    private String keyAlias;

    @Value("${server.port}")
    private int serverPort;

    @Value("${dms.application.base-url}")
    private String baseUrl;

    @EventListener(ApplicationReadyEvent.class)
    public void verifySSLConfiguration() {
        logger.info("=== SSL Configuration Verification ===");
        logger.info("SSL Enabled: {}", sslEnabled);
        logger.info("Server Port: {}", serverPort);
        logger.info("Base URL: {}", baseUrl);
        logger.info("Key Store: {}", keyStore);
        logger.info("Key Store Type: {}", keyStoreType);
        logger.info("Key Alias: {}", keyAlias);
        
        if (sslEnabled) {
            logger.info("✅ SSL is ENABLED - Service should start with HTTPS");
            logger.info("🔗 Expected Service URL: https://localhost:{}", serverPort);
        } else {
            logger.warn("⚠️  SSL is DISABLED - Service will start with HTTP");
            logger.warn("🔗 Service URL: http://localhost:{}", serverPort);
        }
        
        logger.info("=====================================");
    }
}