@echo off
REM Wrapper script to run individual tests while preserving existing test reports
REM Usage: run-test-with-preserved-reports.bat <test-class-name> [additional-maven-args]
REM Example: run-test-with-preserved-reports.bat ComplianceClassificationBasicTest
REM Example: run-test-with-preserved-reports.bat "*SecurityTest" -Dspring.profiles.active=test

setlocal enabledelayedexpansion

if "%1"=="" (
    echo Usage: run-test-with-preserved-reports.bat ^<test-class-name^> [additional-maven-args]
    echo.
    echo Examples:
    echo   run-test-with-preserved-reports.bat ComplianceClassificationBasicTest
    echo   run-test-with-preserved-reports.bat "*SecurityTest" -Dspring.profiles.active=test
    echo   run-test-with-preserved-reports.bat "**/*IntegrationTest"
    echo.
    echo This script preserves the existing surefire-report.html when running individual tests
    exit /b 1
)

set "TEST_CLASS=%1"
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."
set "REPORT_FILE=%PROJECT_ROOT%\target\site\surefire-report.html"

REM Shift to get additional arguments
shift
set "ADDITIONAL_ARGS="
:parse_args
if "%1"=="" goto :args_done
set "ADDITIONAL_ARGS=%ADDITIONAL_ARGS% %1"
shift
goto :parse_args
:args_done

echo ========================================
echo Running Individual Test with Report Preservation
echo ========================================
echo Test Class: %TEST_CLASS%
echo Additional Args: %ADDITIONAL_ARGS%
echo.

REM Step 1: Backup existing reports
echo [1/4] Backing up existing test reports...
call "%SCRIPT_DIR%preserve-test-reports.bat" backup

REM Step 2: Run the individual test
echo [2/4] Running test: %TEST_CLASS%
echo Command: mvn test -Dtest="%TEST_CLASS%" %ADDITIONAL_ARGS%
echo.

cd /d "%PROJECT_ROOT%"
call mvn test -Dtest="%TEST_CLASS%" %ADDITIONAL_ARGS%
set "TEST_RESULT=%ERRORLEVEL%"

REM Step 3: Check if report was deleted and restore if needed
echo [3/4] Checking test report status...
if not exist "%REPORT_FILE%" (
    echo ⚠ Test report was deleted, attempting to restore...
    call "%SCRIPT_DIR%preserve-test-reports.bat" restore
    
    REM If restore didn't work, try to regenerate from XML
    if not exist "%REPORT_FILE%" (
        echo ⚠ Restore failed, attempting to regenerate from XML results...
        call "%SCRIPT_DIR%preserve-test-reports.bat" regenerate
    )
) else (
    echo ✓ Test report preserved
)

REM Step 4: Update the report to include new test results
echo [4/4] Updating test report with latest results...
if exist "%PROJECT_ROOT%\target\surefire-reports\TEST-*.xml" (
    echo Regenerating comprehensive test report...
    call "%SCRIPT_DIR%preserve-test-reports.bat" regenerate
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Test report updated with latest results
    ) else (
        echo ⚠ Failed to update test report, but original report preserved
    )
) else (
    echo ⚠ No new XML test results found
)

echo.
echo ========================================
echo Test Execution Summary
echo ========================================
if %TEST_RESULT% EQU 0 (
    echo ✓ Test: %TEST_CLASS% - PASSED
) else (
    echo ✗ Test: %TEST_CLASS% - FAILED
)

if exist "%REPORT_FILE%" (
    echo ✓ HTML Test Report: Available
    echo   Location: %REPORT_FILE%
    echo   URL: file:///%PROJECT_ROOT:\=/%/target/site/surefire-report.html
) else (
    echo ✗ HTML Test Report: Not Available
)

echo.
echo Individual test results:
if exist "%PROJECT_ROOT%\target\surefire-reports" (
    echo   XML Reports: %PROJECT_ROOT%\target\surefire-reports\
    dir /b "%PROJECT_ROOT%\target\surefire-reports\TEST-*.xml" 2>nul | findstr /v "^$" >nul
    if %ERRORLEVEL% EQU 0 (
        echo   Latest XML files:
        for /f %%f in ('dir /b /o:d "%PROJECT_ROOT%\target\surefire-reports\TEST-*.xml" 2^>nul') do (
            echo     - %%f
        )
    )
)

echo ========================================

REM Clean up backup files
call "%SCRIPT_DIR%preserve-test-reports.bat" cleanup

exit /b %TEST_RESULT%
