# DMS Service User Guides

This directory contains user guides, configuration instructions, and troubleshooting documentation for the DMS service.

## 📁 Directory Contents

### Configuration Guides
- `AWS_S3_Configuration_Summary.md` - AWS S3 storage provider configuration
- `Excel_Import_Instructions.md` - Instructions for importing data from Excel files

### Testing Guides
- `test-document-with-dynamic-storage.md` - Guide for testing document operations with dynamic storage
- `test-dynamic-config.md` - Guide for testing dynamic configuration features

### Error Handling & Troubleshooting
- `Expired_JWT_Error_Handling_Fix_Summary.md` - Handling expired JWT token errors
- `JWT_Error_Handling_Fix_Summary.md` - General JWT error handling
- `JWT_Logging_Fix_Summary.md` - JWT logging configuration and troubleshooting

### General Help
- `HELP.md` - General help and getting started information

## 📖 Guide Categories

### 🔧 Configuration Guides

#### AWS S3 Configuration
**File**: `AWS_S3_Configuration_Summary.md`
**Purpose**: Complete guide for configuring AWS S3 as a storage provider
**Covers**:
- S3 bucket setup
- IAM permissions
- Application configuration
- Security best practices

#### Excel Import Instructions
**File**: `Excel_Import_Instructions.md`
**Purpose**: Step-by-step instructions for importing data from Excel files
**Covers**:
- Supported Excel formats
- Data mapping requirements
- Import process
- Error handling

### 🧪 Testing Guides

#### Dynamic Storage Testing
**File**: `test-document-with-dynamic-storage.md`
**Purpose**: Guide for testing document operations with different storage providers
**Covers**:
- Storage provider switching
- Document upload/download testing
- Performance testing
- Error scenarios

#### Dynamic Configuration Testing
**File**: `test-dynamic-config.md`
**Purpose**: Guide for testing dynamic configuration features
**Covers**:
- Configuration hot-reloading
- Environment-specific settings
- Configuration validation
- Rollback procedures

### 🔍 Troubleshooting Guides

#### JWT Error Handling
**Files**: 
- `JWT_Error_Handling_Fix_Summary.md`
- `Expired_JWT_Error_Handling_Fix_Summary.md`
- `JWT_Logging_Fix_Summary.md`

**Purpose**: Comprehensive JWT troubleshooting and error resolution
**Covers**:
- Common JWT errors and solutions
- Token expiration handling
- Logging configuration
- Security best practices
- Debugging techniques

## 🚀 Quick Start

### For New Users
1. **Start with**: `HELP.md` for general getting started information
2. **Configuration**: Review relevant configuration guides for your setup
3. **Testing**: Use testing guides to verify your configuration
4. **Troubleshooting**: Refer to error handling guides when issues arise

### For Administrators
1. **Storage Setup**: Follow `AWS_S3_Configuration_Summary.md` for cloud storage
2. **Data Import**: Use `Excel_Import_Instructions.md` for bulk data operations
3. **Security**: Review JWT guides for authentication setup
4. **Testing**: Validate setup using testing guides

### For Developers
1. **Testing**: Use dynamic testing guides for development workflows
2. **Configuration**: Understand dynamic configuration for development environments
3. **Debugging**: Leverage JWT logging guides for troubleshooting
4. **Integration**: Follow configuration guides for service integration

## 📋 Prerequisites

### General Requirements
- DMS service installed and configured
- Appropriate access permissions
- Network connectivity to required services

### Specific Requirements by Guide
- **AWS S3**: AWS account with appropriate permissions
- **Excel Import**: Excel files in supported formats
- **JWT**: Valid JWT configuration and certificates
- **Testing**: Development or test environment access

## 🔗 Related Documentation

### Core Documentation
- `/docs/` - Main documentation directory
- `/docs/api/` - API documentation
- `/docs/testing/` - Testing documentation
- `/docs/deployment/` - Deployment guides

### Implementation Details
- `/docs/implementation-summaries/` - Technical implementation summaries
- `/src/main/` - Source code and configuration
- `/tests/` - Test cases and scripts

### Operational Documentation
- `/scripts/` - Operational scripts and utilities
- `/monitoring/` - Monitoring and observability
- `/docs/troubleshooting/` - Detailed troubleshooting guides

## 📝 Contributing to Guides

When adding new guides:

1. **Follow naming conventions**: Use descriptive names with appropriate suffixes
2. **Include comprehensive content**: Cover purpose, prerequisites, steps, and troubleshooting
3. **Add examples**: Provide practical examples and code snippets
4. **Update this README**: Add the new guide to the appropriate category
5. **Cross-reference**: Link to related documentation where appropriate

### Guide Template Structure
```markdown
# Guide Title

## Overview
Brief description of what this guide covers

## Prerequisites
- List of requirements
- Dependencies
- Access requirements

## Step-by-Step Instructions
1. Detailed steps
2. With examples
3. And expected outcomes

## Troubleshooting
Common issues and solutions

## Related Documentation
Links to related guides and documentation
```

## 🔍 Finding the Right Guide

### By Use Case
- **Setting up storage**: AWS S3 Configuration Summary
- **Importing data**: Excel Import Instructions
- **Authentication issues**: JWT Error Handling guides
- **Testing setup**: Dynamic testing guides
- **General help**: HELP.md

### By Role
- **System Administrator**: Configuration and troubleshooting guides
- **Developer**: Testing and dynamic configuration guides
- **End User**: General help and basic configuration guides
- **DevOps**: All guides for comprehensive system understanding

## 📊 Guide Maintenance

These guides are regularly updated to reflect:
- Latest feature additions
- Configuration changes
- Best practices updates
- Community feedback
- Security updates

For the most current information, always refer to the latest version of each guide.
