name: Dependency Security Scan

# This workflow performs comprehensive security scanning of dependencies
# including vulnerability detection, license compliance, and security advisories

on:
  # Run on every push to main branch
  push:
    branches: [ main, develop ]
  
  # Run on pull requests
  pull_request:
    branches: [ main, develop ]
  
  # Run daily security scan
  schedule:
    - cron: '0 6 * * *'  # Daily at 6 AM UTC
  
  # Allow manual triggering
  workflow_dispatch:
    inputs:
      scan_type:
        description: 'Type of security scan'
        required: true
        default: 'full'
        type: choice
        options:
          - full
          - vulnerabilities-only
          - licenses-only

env:
  JAVA_VERSION: '21'
  MAVEN_OPTS: '-Xmx2g -XX:+UseG1GC'

jobs:
  dependency-vulnerability-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    
    permissions:
      contents: read
      security-events: write
      pull-requests: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          cache: maven
      
      - name: Cache Maven dependencies
        uses: actions/cache@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-
      
      # OWASP Dependency Check - Comprehensive vulnerability scanning
      - name: Run OWASP Dependency Check
        run: |
          mvn org.owasp:dependency-check-maven:check \
            -DfailBuildOnCVSS=7 \
            -DsuppressionsLocation=.github/security/dependency-check-suppressions.xml \
            -DretireJsAnalyzerEnabled=false \
            -DnodeAnalyzerEnabled=false \
            -DassemblyAnalyzerEnabled=false
      
      - name: Upload OWASP Dependency Check results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: dependency-check-report
          path: target/dependency-check-report.html
          retention-days: 30
      
      # Snyk vulnerability scanning
      - name: Run Snyk to check for vulnerabilities
        uses: snyk/actions/maven@master
        continue-on-error: true
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=medium --file=pom.xml
      
      - name: Upload Snyk results to GitHub Code Scanning
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: snyk.sarif
      
      # Maven dependency tree analysis
      - name: Generate dependency tree
        run: |
          mvn dependency:tree -DoutputFile=dependency-tree.txt -DoutputType=text
          mvn dependency:analyze -DfailOnWarning=false > dependency-analysis.txt 2>&1 || true
      
      - name: Upload dependency analysis
        uses: actions/upload-artifact@v4
        with:
          name: dependency-analysis
          path: |
            dependency-tree.txt
            dependency-analysis.txt
          retention-days: 30
      
      # License compliance check
      - name: License compliance scan
        run: |
          mvn org.codehaus.mojo:license-maven-plugin:aggregate-third-party-report \
            -Dlicense.excludedScopes=test,provided
      
      - name: Upload license report
        uses: actions/upload-artifact@v4
        with:
          name: license-report
          path: target/site/aggregate-third-party-report.html
          retention-days: 30
      
      # Security advisory check using GitHub's database
      - name: GitHub Security Advisory check
        run: |
          # Create security report
          cat > security-report.md << 'EOF'
          # DMS Dependency Security Report
          
          Generated on: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
          
          ## Scan Summary
          - OWASP Dependency Check: Completed
          - Snyk Vulnerability Scan: Completed
          - License Compliance: Completed
          - GitHub Security Advisories: Completed
          
          ## Critical Findings
          EOF
          
          # Check for high/critical vulnerabilities in OWASP report
          if [ -f target/dependency-check-report.html ]; then
            CRITICAL_COUNT=$(grep -c "CRITICAL" target/dependency-check-report.html || echo "0")
            HIGH_COUNT=$(grep -c "HIGH" target/dependency-check-report.html || echo "0")
            echo "- Critical vulnerabilities: $CRITICAL_COUNT" >> security-report.md
            echo "- High vulnerabilities: $HIGH_COUNT" >> security-report.md
          fi
          
          # Add recommendations
          cat >> security-report.md << 'EOF'
          
          ## Recommendations
          1. Review all CRITICAL and HIGH severity vulnerabilities
          2. Update dependencies with available patches
          3. Consider alternative libraries for unmaintained dependencies
          4. Implement security monitoring for new vulnerabilities
          
          ## Next Steps
          - [ ] Review vulnerability report
          - [ ] Create issues for critical vulnerabilities
          - [ ] Update affected dependencies
          - [ ] Test application after updates
          - [ ] Deploy security patches
          EOF
      
      - name: Upload security report
        uses: actions/upload-artifact@v4
        with:
          name: security-report
          path: security-report.md
          retention-days: 90
      
      # Create GitHub issue for critical vulnerabilities
      - name: Create security issue for critical vulnerabilities
        if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            
            // Check if there are critical vulnerabilities
            let hasCritical = false;
            try {
              const reportContent = fs.readFileSync('target/dependency-check-report.html', 'utf8');
              hasCritical = reportContent.includes('CRITICAL') || reportContent.includes('HIGH');
            } catch (error) {
              console.log('Could not read dependency check report');
            }
            
            if (hasCritical) {
              const issueBody = `
              # 🚨 Critical Security Vulnerabilities Detected
              
              The automated dependency security scan has detected critical or high-severity vulnerabilities in the DMS project dependencies.
              
              ## Scan Details
              - **Scan Date**: ${new Date().toISOString()}
              - **Workflow Run**: [${context.runId}](${context.payload.repository.html_url}/actions/runs/${context.runId})
              - **Branch**: ${context.ref}
              
              ## Action Required
              1. Review the [OWASP Dependency Check Report](${context.payload.repository.html_url}/actions/runs/${context.runId})
              2. Identify affected dependencies and available patches
              3. Update dependencies or implement mitigations
              4. Test the application thoroughly after updates
              5. Deploy security patches as soon as possible
              
              ## Security Best Practices
              - Prioritize CRITICAL and HIGH severity vulnerabilities
              - Consider the exploitability and impact of each vulnerability
              - Update to the latest secure versions when available
              - Monitor for new vulnerabilities in dependencies
              
              **This issue was automatically created by the dependency security scan workflow.**
              `;
              
              await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: `🚨 Critical Security Vulnerabilities Detected - ${new Date().toISOString().split('T')[0]}`,
                body: issueBody,
                labels: ['security', 'critical', 'dependencies', 'automated']
              });
            }
      
      # Comment on PR with security scan results
      - name: Comment PR with security results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            
            let comment = `## 🔒 Dependency Security Scan Results\n\n`;
            comment += `**Scan completed on**: ${new Date().toISOString()}\n`;
            comment += `**Workflow run**: [${context.runId}](${context.payload.repository.html_url}/actions/runs/${context.runId})\n\n`;
            
            // Check for vulnerabilities
            try {
              const reportExists = fs.existsSync('target/dependency-check-report.html');
              if (reportExists) {
                comment += `✅ OWASP Dependency Check completed\n`;
                comment += `📊 Detailed report available in workflow artifacts\n\n`;
              }
            } catch (error) {
              comment += `❌ Could not generate dependency check report\n\n`;
            }
            
            comment += `### Security Checklist\n`;
            comment += `- [ ] Review vulnerability report\n`;
            comment += `- [ ] Verify no critical vulnerabilities introduced\n`;
            comment += `- [ ] Check license compliance\n`;
            comment += `- [ ] Update dependencies if needed\n\n`;
            
            comment += `*This comment was automatically generated by the dependency security scan workflow.*`;
            
            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  dependency-update-compatibility-test:
    name: Dependency Update Compatibility Test
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' && contains(github.event.pull_request.labels.*.name, 'dependencies')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          cache: maven
      
      - name: Run compatibility tests
        run: |
          # Run full test suite to ensure compatibility
          mvn clean test -Dspring.profiles.active=test
          
          # Run integration tests
          mvn failsafe:integration-test failsafe:verify -Dspring.profiles.active=integration-test
      
      - name: Generate compatibility report
        if: always()
        run: |
          echo "# Dependency Update Compatibility Report" > compatibility-report.md
          echo "" >> compatibility-report.md
          echo "**Test Results:**" >> compatibility-report.md
          
          if [ $? -eq 0 ]; then
            echo "✅ All tests passed - dependency updates are compatible" >> compatibility-report.md
          else
            echo "❌ Some tests failed - dependency updates may have compatibility issues" >> compatibility-report.md
          fi
          
          echo "" >> compatibility-report.md
          echo "**Recommendation:**" >> compatibility-report.md
          if [ $? -eq 0 ]; then
            echo "✅ Safe to merge dependency updates" >> compatibility-report.md
          else
            echo "⚠️ Review test failures before merging" >> compatibility-report.md
          fi
      
      - name: Upload compatibility report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: compatibility-report
          path: compatibility-report.md
          retention-days: 30

  dependency-license-compliance:
    name: License Compliance Check
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          cache: maven

      - name: Generate license report
        run: |
          mvn org.codehaus.mojo:license-maven-plugin:aggregate-third-party-report
          mvn org.codehaus.mojo:license-maven-plugin:check-file-header

      - name: Check for prohibited licenses
        run: |
          # Define prohibited licenses
          PROHIBITED_LICENSES="GPL-2.0 GPL-3.0 AGPL-3.0 LGPL-2.1 LGPL-3.0"

          # Check license report for prohibited licenses
          if [ -f target/site/aggregate-third-party-report.html ]; then
            for license in $PROHIBITED_LICENSES; do
              if grep -q "$license" target/site/aggregate-third-party-report.html; then
                echo "ERROR: Prohibited license found: $license"
                exit 1
              fi
            done
            echo "✅ No prohibited licenses found"
          fi

      - name: Upload license compliance report
        uses: actions/upload-artifact@v4
        with:
          name: license-compliance-report
          path: target/site/aggregate-third-party-report.html
          retention-days: 90
