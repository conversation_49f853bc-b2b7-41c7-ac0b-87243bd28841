# Performance Benchmarking Guide - DMS Service

## Overview
This guide provides comprehensive performance benchmarking strategies, tools, and procedures for the Document Management Service (DMS).

## Benchmarking Strategy

### 1. Performance Metrics
- **Throughput**: Requests per second (RPS)
- **Latency**: Response time (p50, p95, p99)
- **Resource Utilization**: CPU, memory, disk I/O
- **Storage Performance**: Upload/download speeds
- **Database Performance**: Query execution times
- **Cache Hit Ratio**: Redis cache efficiency

### 2. Test Categories
- **Load Testing**: Normal expected load
- **Stress Testing**: Peak load conditions
- **Spike Testing**: Sudden load increases
- **Volume Testing**: Large data sets
- **Endurance Testing**: Extended periods

## Benchmarking Tools

### 1. JMeter Test Plans

#### GraphQL Operations Test Plan

```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.4.1">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="DMS GraphQL Performance Test">
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables">
        <collectionProp name="Arguments.arguments">
          <elementProp name="BASE_URL" elementType="Argument">
            <stringProp name="Argument.name">BASE_URL</stringProp>
            <stringProp name="Argument.value">http://localhost:8080</stringProp>
          </elementProp>
          <elementProp name="THREADS" elementType="Argument">
            <stringProp name="Argument.name">THREADS</stringProp>
            <stringProp name="Argument.value">100</stringProp>
          </elementProp>
          <elementProp name="RAMP_TIME" elementType="Argument">
            <stringProp name="Argument.name">RAMP_TIME</stringProp>
            <stringProp name="Argument.value">300</stringProp>
          </elementProp>
          <elementProp name="DURATION" elementType="Argument">
            <stringProp name="Argument.name">DURATION</stringProp>
            <stringProp name="Argument.value">600</stringProp>
          </elementProp>
        </collectionProp>
      </elementProp>
    </TestPlan>
    
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="DMS Load Test">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControllerGui" testclass="LoopController" testname="Loop Controller">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <intProp name="LoopController.loops">-1</intProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">${THREADS}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">${RAMP_TIME}</stringProp>
        <boolProp name="ThreadGroup.scheduler">true</boolProp>
        <stringProp name="ThreadGroup.duration">${DURATION}</stringProp>
      </ThreadGroup>
      
      <hashTree>
        <!-- Authentication Setup -->
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="JWT Authentication">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{
  "query": "mutation { authenticate(input: { username: \"testuser\", password: \"password\" }) { token user { id username } } }"
}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">${BASE_URL}</stringProp>
          <stringProp name="HTTPSampler.path">/dms/graphql</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
        </HTTPSamplerProxy>
        
        <!-- Document Search Test -->
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Search Documents">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{
  "query": "query { searchDocuments(input: { title: \"test\", pagination: { page: 0, size: 20 } }) { content { id title filename createdAt } totalElements } }"
}</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">${BASE_URL}</stringProp>
          <stringProp name="HTTPSampler.path">/dms/graphql</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
        </HTTPSamplerProxy>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

#### File Upload Performance Test

```bash
#!/bin/bash
# upload_performance_test.sh

BASE_URL="http://localhost:8080"
CONCURRENT_USERS=50
TEST_DURATION=300
FILE_SIZES=("1MB" "10MB" "50MB" "100MB")

# Generate test files
generate_test_files() {
    echo "Generating test files..."
    for size in "${FILE_SIZES[@]}"; do
        dd if=/dev/zero of="test_${size}.bin" bs=1024 count=$(echo ${size} | sed 's/MB/024/')
    done
}

# Run upload tests
run_upload_tests() {
    for size in "${FILE_SIZES[@]}"; do
        echo "Testing upload performance for ${size} files..."
        
        # JMeter command for file upload
        jmeter -n -t upload_test.jmx \
            -Jfile_path="test_${size}.bin" \
            -Jthreads=${CONCURRENT_USERS} \
            -Jduration=${TEST_DURATION} \
            -l "upload_${size}_results.jtl" \
            -e -o "upload_${size}_report"
    done
}

# Analyze results
analyze_results() {
    echo "Performance Test Results Summary"
    echo "================================"
    
    for size in "${FILE_SIZES[@]}"; do
        echo "File Size: ${size}"
        echo "------------------------"
        
        # Extract metrics from JTL file
        awk -F',' '
        NR > 1 {
            sum += $2
            count++
            if ($2 > max) max = $2
            if (min == 0 || $2 < min) min = $2
        }
        END {
            avg = sum / count
            print "Average Response Time: " avg " ms"
            print "Min Response Time: " min " ms"
            print "Max Response Time: " max " ms"
            print "Total Samples: " count
        }' "upload_${size}_results.jtl"
        echo ""
    done
}

# Main execution
generate_test_files
run_upload_tests
analyze_results
```

### 2. K6 Performance Scripts

#### GraphQL Performance Test

```javascript
// k6_graphql_test.js
import http from 'k6/http';
import { check, group } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const uploadTrend = new Trend('upload_duration');
const downloadTrend = new Trend('download_duration');

export const options = {
  stages: [
    { duration: '2m', target: 20 },  // Ramp up
    { duration: '5m', target: 20 },  // Steady state
    { duration: '2m', target: 50 },  // Load increase
    { duration: '5m', target: 50 },  // High load
    { duration: '2m', target: 0 },   // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
    errors: ['rate<0.1'],              // Error rate under 10%
  },
};

const BASE_URL = 'http://localhost:8080/dms';

// Authentication
function authenticate() {
  const query = `
    mutation {
      authenticate(input: {
        username: "testuser"
        password: "password"
      }) {
        token
        user {
          id
          username
        }
      }
    }
  `;
  
  const response = http.post(`${BASE_URL}/graphql`, 
    JSON.stringify({ query }), {
      headers: { 'Content-Type': 'application/json' }
    }
  );
  
  check(response, {
    'authentication successful': (r) => r.status === 200,
    'token received': (r) => JSON.parse(r.body).data.authenticate.token != null,
  });
  
  return JSON.parse(response.body).data.authenticate.token;
}

// Search documents
function searchDocuments(token) {
  const query = `
    query {
      searchDocuments(input: {
        title: "test"
        pagination: { page: 0, size: 20 }
      }) {
        content {
          id
          title
          filename
          createdAt
        }
        totalElements
      }
    }
  `;
  
  const response = http.post(`${BASE_URL}/graphql`,
    JSON.stringify({ query }), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    }
  );
  
  check(response, {
    'search successful': (r) => r.status === 200,
    'results returned': (r) => JSON.parse(r.body).data.searchDocuments.content.length >= 0,
  });
  
  errorRate.add(response.status !== 200);
}

// Upload document
function uploadDocument(token) {
  const formData = {
    operations: JSON.stringify({
      query: `
        mutation($file: Upload!) {
          uploadDocument(input: {
            file: $file
            title: "Performance Test Document"
            description: "Test document for performance testing"
          }) {
            id
            title
            filename
          }
        }
      `,
      variables: { file: null }
    }),
    map: JSON.stringify({ "0": ["variables.file"] }),
    "0": http.file(createTestFile(), 'test.txt', 'text/plain')
  };
  
  const start = Date.now();
  const response = http.post(`${BASE_URL}/graphql`, formData, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  uploadTrend.add(Date.now() - start);
  
  check(response, {
    'upload successful': (r) => r.status === 200,
    'document created': (r) => JSON.parse(r.body).data.uploadDocument.id != null,
  });
  
  errorRate.add(response.status !== 200);
}

// Helper function to create test file content
function createTestFile() {
  return 'Performance test file content. '.repeat(100);
}

export default function() {
  const token = authenticate();
  
  group('Document Operations', () => {
    searchDocuments(token);
    uploadDocument(token);
  });
}
```

### 3. Database Performance Testing

#### MySQL Performance Script

```sql
-- database_performance_test.sql

-- Test document search performance
EXPLAIN ANALYZE 
SELECT d.*, u.username as created_by_username
FROM documents d
JOIN users u ON d.created_by = u.id
WHERE d.title LIKE '%test%'
  AND d.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY d.created_at DESC
LIMIT 20;

-- Test audit log query performance  
EXPLAIN ANALYZE
SELECT al.*, u.username
FROM audit_logs al
JOIN users u ON al.user_id = u.id
WHERE al.timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
  AND al.action = 'DOCUMENT_UPLOAD'
ORDER BY al.timestamp DESC
LIMIT 100;

-- Test permission check performance
EXPLAIN ANALYZE
SELECT COUNT(*)
FROM document_permissions dp
JOIN roles r ON dp.role_id = r.id
WHERE dp.document_id = 1
  AND r.name IN ('ADMIN', 'EDITOR')
  AND dp.permission_type = 'READ';

-- Index effectiveness analysis
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    PACKED,
    NULLABLE,
    INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'dms_prod'
ORDER BY TABLE_NAME, INDEX_NAME;

-- Query performance monitoring
SELECT 
    query_id,
    exec_count,
    avg_timer_wait/1000000000 as avg_time_sec,
    max_timer_wait/1000000000 as max_time_sec,
    sum_timer_wait/1000000000 as total_time_sec,
    sum_rows_examined,
    sum_rows_sent,
    digest_text
FROM performance_schema.events_statements_summary_by_digest
WHERE digest_text LIKE '%documents%'
ORDER BY avg_timer_wait DESC
LIMIT 10;
```

## Performance Monitoring Setup

### 1. Application Metrics with Micrometer

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'dms-service'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/dms/actuator/prometheus'
    scrape_interval: 10s
```

### 2. Grafana Dashboard Configuration

```json
{
  "dashboard": {
    "title": "DMS Performance Dashboard",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_server_requests_seconds_count[5m])",
            "legendFormat": "{{method}} {{uri}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph", 
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_server_requests_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_server_requests_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "JVM Memory",
        "type": "graph",
        "targets": [
          {
            "expr": "jvm_memory_used_bytes{area=\"heap\"}",
            "legendFormat": "Heap Used"
          },
          {
            "expr": "jvm_memory_max_bytes{area=\"heap\"}",
            "legendFormat": "Heap Max"
          }
        ]
      }
    ]
  }
}
```

### 3. JVM Performance Monitoring

```bash
#!/bin/bash
# jvm_monitoring.sh

JVM_PID=$(pgrep -f "dms-svc")

# GC monitoring
echo "=== GC Statistics ==="
jstat -gc $JVM_PID 1s 10

# Memory usage
echo "=== Memory Usage ==="
jmap -histo $JVM_PID | head -20

# Thread analysis
echo "=== Thread Analysis ==="
jstack $JVM_PID | grep -A 5 -B 5 "BLOCKED\|WAITING"

# CPU profiling
echo "=== CPU Profiling ==="
top -H -p $JVM_PID -n 3 -d 1
```

## Benchmark Execution Procedures

### 1. Pre-Test Setup

```bash
#!/bin/bash
# pre_test_setup.sh

# Clean logs
rm -rf /var/log/dms/*

# Reset database state
mysql -u dms_user -p dms_prod < clean_test_data.sql

# Clear Redis cache
redis-cli -h localhost -p 6379 FLUSHALL

# Restart application
systemctl restart dms

# Wait for application startup
sleep 30

# Verify health
curl -f http://localhost:8080/dms/actuator/health
```

### 2. Test Execution

```bash
#!/bin/bash
# run_performance_tests.sh

TEST_SCENARIOS=("light_load" "medium_load" "heavy_load" "spike_test")
RESULTS_DIR="performance_results_$(date +%Y%m%d_%H%M%S)"

mkdir -p $RESULTS_DIR

for scenario in "${TEST_SCENARIOS[@]}"; do
    echo "Running $scenario test..."
    
    # Start monitoring
    ./start_monitoring.sh $RESULTS_DIR/$scenario &
    MONITOR_PID=$!
    
    # Run K6 test
    k6 run --out json=$RESULTS_DIR/${scenario}_results.json \
           --config ${scenario}_config.js \
           k6_graphql_test.js
    
    # Stop monitoring
    kill $MONITOR_PID
    
    # Generate report
    k6 run --out html=$RESULTS_DIR/${scenario}_report.html \
           $RESULTS_DIR/${scenario}_results.json
    
    # Cool down period
    sleep 60
done

# Generate summary report
./generate_summary_report.sh $RESULTS_DIR
```

### 3. Results Analysis

```python
#!/usr/bin/env python3
# analyze_results.py

import json
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime

def analyze_k6_results(file_path):
    """Analyze K6 JSON results"""
    with open(file_path, 'r') as f:
        data = [json.loads(line) for line in f if line.strip()]
    
    # Extract metrics
    http_reqs = [d for d in data if d.get('metric') == 'http_req_duration']
    
    df = pd.DataFrame(http_reqs)
    
    # Calculate statistics
    stats = {
        'avg_response_time': df['value'].mean(),
        'p95_response_time': df['value'].quantile(0.95),
        'p99_response_time': df['value'].quantile(0.99),
        'min_response_time': df['value'].min(),
        'max_response_time': df['value'].max(),
        'total_requests': len(df)
    }
    
    return stats

def generate_performance_report(results_dir):
    """Generate comprehensive performance report"""
    scenarios = ['light_load', 'medium_load', 'heavy_load', 'spike_test']
    
    report = {
        'test_date': datetime.now().isoformat(),
        'scenarios': {}
    }
    
    for scenario in scenarios:
        file_path = f"{results_dir}/{scenario}_results.json"
        try:
            stats = analyze_k6_results(file_path)
            report['scenarios'][scenario] = stats
        except FileNotFoundError:
            print(f"Results file not found for {scenario}")
    
    # Generate charts
    create_performance_charts(report)
    
    return report

def create_performance_charts(report):
    """Create performance visualization charts"""
    scenarios = list(report['scenarios'].keys())
    avg_times = [report['scenarios'][s]['avg_response_time'] for s in scenarios]
    p95_times = [report['scenarios'][s]['p95_response_time'] for s in scenarios]
    
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.bar(scenarios, avg_times, label='Average')
    plt.bar(scenarios, p95_times, label='95th Percentile', alpha=0.7)
    plt.title('Response Times by Scenario')
    plt.ylabel('Response Time (ms)')
    plt.legend()
    plt.xticks(rotation=45)
    
    plt.subplot(1, 2, 2)
    total_requests = [report['scenarios'][s]['total_requests'] for s in scenarios]
    plt.bar(scenarios, total_requests)
    plt.title('Total Requests by Scenario')
    plt.ylabel('Number of Requests')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig('performance_summary.png')
    plt.show()

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        results_dir = sys.argv[1]
        report = generate_performance_report(results_dir)
        
        with open(f"{results_dir}/performance_report.json", 'w') as f:
            json.dump(report, f, indent=2)
        
        print("Performance analysis complete!")
    else:
        print("Usage: python analyze_results.py <results_directory>")
```

## Performance Baselines

### Expected Performance Targets

| Metric | Target | Acceptable | Poor |
|--------|--------|------------|------|
| GraphQL Query Response Time (p95) | < 500ms | < 1000ms | > 1000ms |
| File Upload (10MB) | < 2s | < 5s | > 5s |
| File Download (10MB) | < 1s | < 3s | > 3s |
| Document Search (p95) | < 200ms | < 500ms | > 500ms |
| Throughput (Simple Queries) | > 1000 RPS | > 500 RPS | < 500 RPS |
| Memory Usage (4GB heap) | < 70% | < 85% | > 85% |
| CPU Usage (under load) | < 80% | < 90% | > 90% |

### Storage Provider Comparison

```bash
#!/bin/bash
# storage_performance_comparison.sh

PROVIDERS=("LOCAL" "S3" "SHAREPOINT")
FILE_SIZES=("1MB" "10MB" "50MB")

for provider in "${PROVIDERS[@]}"; do
    echo "Testing $provider storage performance..."
    
    # Update configuration for provider
    update_storage_config.sh $provider
    
    for size in "${FILE_SIZES[@]}"; do
        echo "Testing ${size} file with ${provider}..."
        
        # Upload test
        upload_start=$(date +%s%N)
        curl -X POST -F "file=@test_${size}.bin" \
             -H "Authorization: Bearer $JWT_TOKEN" \
             http://localhost:8080/dms/api/documents/upload
        upload_end=$(date +%s%N)
        upload_time=$(( (upload_end - upload_start) / 1000000 ))
        
        echo "${provider},${size},upload,${upload_time}ms" >> storage_performance.csv
        
        # Download test
        download_start=$(date +%s%N)
        curl -H "Authorization: Bearer $JWT_TOKEN" \
             http://localhost:8080/dms/api/documents/1/download > /dev/null
        download_end=$(date +%s%N)
        download_time=$(( (download_end - download_start) / 1000000 ))
        
        echo "${provider},${size},download,${download_time}ms" >> storage_performance.csv
    done
done

# Generate comparison report
python3 generate_storage_comparison.py storage_performance.csv
```

## Continuous Performance Monitoring

### 1. Automated Performance Tests

```yaml
# .github/workflows/performance.yml
name: Performance Tests

on:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM
  push:
    branches: [main]

jobs:
  performance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Java
        uses: actions/setup-java@v2
        with:
          java-version: '21'
          
      - name: Build Application
        run: mvn clean package -DskipTests
        
      - name: Start Services
        run: docker-compose -f docker-compose.test.yml up -d
        
      - name: Wait for Services
        run: sleep 60
        
      - name: Run Performance Tests
        run: k6 run --out json=results.json k6_test.js
        
      - name: Analyze Results
        run: python3 analyze_results.py results.json
        
      - name: Upload Results
        uses: actions/upload-artifact@v2
        with:
          name: performance-results
          path: results.json
```

### 2. Performance Alerts

```yaml
# prometheus_alerts.yml
groups:
  - name: dms_performance
    rules:
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_server_requests_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          
      - alert: HighErrorRate
        expr: rate(http_server_requests_seconds_count{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          
      - alert: HighMemoryUsage
        expr: jvm_memory_used_bytes{area="heap"} / jvm_memory_max_bytes{area="heap"} > 0.9
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High JVM memory usage"
```

## Troubleshooting Performance Issues

### Common Performance Problems

1. **Slow Database Queries**
   - Enable query logging
   - Add missing indexes
   - Optimize query structure

2. **Memory Leaks**
   - Monitor heap usage
   - Analyze heap dumps
   - Check for unclosed resources

3. **High CPU Usage**
   - Profile application
   - Check for infinite loops
   - Optimize algorithms

4. **Network Latency**
   - Use connection pooling
   - Enable compression
   - Optimize payload size

### Performance Tuning Checklist

- [ ] Database indexes optimized
- [ ] Connection pools configured
- [ ] JVM parameters tuned
- [ ] Cache hit ratio > 80%
- [ ] GraphQL query complexity limits set
- [ ] File upload size limits configured
- [ ] Monitoring and alerting active
- [ ] Performance tests automated

---

This guide provides a comprehensive framework for performance testing and monitoring of the DMS service. Adjust parameters and thresholds based on your specific requirements and infrastructure.
