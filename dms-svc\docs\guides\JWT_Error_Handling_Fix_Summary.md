# JWT Error Handling Fix Summary

## Problem
When providing an invalid JWT token in GraphQL requests, the system was returning generic internal server errors instead of proper authentication error responses.

**Original Error Response:**
```json
{
  "errors": [
    {
      "message": "An unexpected error occurred. Please try again later.",
      "locations": [{"column": 3, "line": 2}],
      "path": ["getDocumentById"],
      "extensions": {
        "type": "SYSTEM_ERROR",
        "operation": "getDocumentById",
        "code": "INTERNAL_ERROR",
        "classification": "DataFetchingException"
      }
    }
  ],
  "data": {"getDocumentById": null}
}
```

## Solution Implemented

### 1. Created Custom Authentication Exception
- **File:** `src/main/java/com/ascentbusiness/dms_svc/exception/InvalidTokenException.java`
- **Purpose:** Specific exception for JWT token validation failures
- **Extends:** `AuthenticationException` from Spring Security

### 2. Updated JWT Token Provider
- **File:** `src/main/java/com/ascentbusiness/dms_svc/security/JwtTokenProvider.java`
- **Changes:**
  - Modified `validateToken()` method to throw specific `InvalidTokenException` instead of returning false
  - Added detailed error messages for different JWT validation failure scenarios:
    - Malformed JWT tokens
    - Expired JWT tokens 
    - Unsupported JWT tokens
    - Empty/null JWT tokens
    - General validation failures

### 3. Enhanced GraphQL Exception Handler
- **File:** `src/main/java/com/ascentbusiness/dms_svc/config/GraphQLExceptionHandler.java`
- **Changes:**
  - Added handling for `InvalidTokenException`
  - Added handling for Spring Security `AuthenticationException`
  - Added handling for Spring Security `AccessDeniedException`
  - Each exception type now returns appropriate error codes and messages

### 4. Updated Document Resolver
- **File:** `src/main/java/com/ascentbusiness/dms_svc/resolver/DocumentResolver.java`
- **Changes:**
  - Added `checkAuthentication()` method to verify authentication context
  - Added authentication check to `getDocumentById()` method
  - Throws `UnauthorizedException` when no valid authentication is present

## Expected Behavior After Fix

### Invalid JWT Token Response
```json
{
  "errors": [
    {
      "message": "Invalid or malformed JWT token",
      "locations": [{"column": 3, "line": 2}],
      "path": ["getDocumentById"],
      "extensions": {
        "code": "INVALID_TOKEN",
        "type": "AUTHENTICATION_ERROR",
        "operation": "getDocumentById"
      }
    }
  ],
  "data": {"getDocumentById": null}
}
```

### No Authentication Token Response
```json
{
  "errors": [
    {
      "message": "Authentication required. Please provide a valid JWT token.",
      "locations": [{"column": 3, "line": 2}],
      "path": ["getDocumentById"],
      "extensions": {
        "code": "FORBIDDEN",
        "type": "AUTHORIZATION_ERROR",
        "operation": "getDocumentById"
      }
    }
  ],
  "data": {"getDocumentById": null}
}
```

### Expired JWT Token Response
```json
{
  "errors": [
    {
      "message": "JWT token has expired",
      "locations": [{"column": 3, "line": 2}],
      "path": ["getDocumentById"],
      "extensions": {
        "code": "INVALID_TOKEN",
        "type": "AUTHENTICATION_ERROR",
        "operation": "getDocumentById"
      }
    }
  ],
  "data": {"getDocumentById": null}
}
```

## Testing the Fix

### Test Case 1: Invalid JWT Token
**Request Headers:**
```
Authorization: Bearer asdsadsadada
```

**GraphQL Query:**
```graphql
query {
  getDocumentById(id: "1") {
    id
    name
    status
  }
}
```

**Expected Result:** `INVALID_TOKEN` error with appropriate message

### Test Case 2: No Authorization Header
**Request Headers:** (none)

**GraphQL Query:**
```graphql
query {
  getDocumentById(id: "1") {
    id
    name
    status
  }
}
```

**Expected Result:** `FORBIDDEN` error indicating authentication required

### Test Case 3: Valid JWT Token
**Request Headers:**
```
Authorization: Bearer <valid_jwt_token>
```

**GraphQL Query:**
```graphql
query {
  getDocumentById(id: "1") {
    id
    name
    status
  }
}
```

**Expected Result:** Successful response with document data

## Error Code Reference

| Error Code | Type | Description |
|------------|------|-------------|
| `INVALID_TOKEN` | `AUTHENTICATION_ERROR` | JWT token is invalid, malformed, or expired |
| `AUTHENTICATION_REQUIRED` | `AUTHENTICATION_ERROR` | General authentication failure |
| `ACCESS_DENIED` | `AUTHORIZATION_ERROR` | User lacks sufficient permissions |
| `FORBIDDEN` | `AUTHORIZATION_ERROR` | User not authenticated |

## Files Modified

1. `src/main/java/com/ascentbusiness/dms_svc/exception/InvalidTokenException.java` (NEW)
2. `src/main/java/com/ascentbusiness/dms_svc/security/JwtTokenProvider.java`
3. `src/main/java/com/ascentbusiness/dms_svc/config/GraphQLExceptionHandler.java`
4. `src/main/java/com/ascentbusiness/dms_svc/resolver/DocumentResolver.java`

## Validation
The application compiles successfully with all changes, indicating proper integration with the existing codebase.
